"use strict";const e=require("../../common/vendor.js"),t=require("../../common/assets.js"),a={components:{uniLoadMore:()=>"../../components/uni-load-more.js"},data:()=>({topRefresh:!1,listData:[],dataType:1,order_id:0,last_page:0,page:1,list_rows:10,no_more:!1,loading:!0}),computed:{loadingType(){return this.loading?1:0!=this.listData.length&&this.no_more?2:0}},onShow(){this.initData(),this.getData()},onReachBottom(){let e=this;e.page<e.last_page&&(e.page++,e.getData()),e.no_more=!0},methods:{initData(){let e=this;e.page=1,e.listData=[],e.no_more=!1},orderStateFunc(e){let t=this;t.loading||t.dataType!=e&&(t.page=1,t.loading=!0,t.listData=[],t.dataType=e,t.getData())},getData(){if(!this.getUserId())return void(this.loading=!1);let e=this;e.loading=!0,e._get("user.order/lists",{dataType:e.dataType,page:e.page,list_rows:e.list_rows},(function(t){e.loading=!1,e.listData=e.listData.concat(t.data.list.data),e.last_page=t.data.list.last_page,t.data.list.last_page<=1?e.no_more=!0:e.no_more=!1}))},gotoOrder(e){this.gotoPage("/pages/order/order-detail?order_id="+e)},onPayOrder(e){let t="/pages/order/cashier?order_type=10&order_id="+e;this.gotoPage(t,"reLaunch")},orderReceipt(t){let a=this;e.index.showModal({title:"提示",content:"您确定要收货吗?",success:function(o){o.confirm?(e.index.showLoading({title:"正在处理",mask:!0}),a._post("user.order/receipt",{order_id:t},(function(t){e.index.hideLoading(),e.index.showToast({title:t.msg,duration:2e3,icon:"success"}),a.listData=[],a.getData()}))):e.index.showToast({title:"取消收货",duration:1e3,icon:"none"})}})},cancelOrder(t){let a=this,o=t;e.index.showModal({title:"提示",content:"您确定要取消吗?",success:function(t){t.confirm&&(e.index.showLoading({title:"正在处理",mask:!0}),a._get("user.order/cancel",{order_id:o},(function(t){e.index.hideLoading(),e.index.showToast({title:"操作成功",duration:2e3,icon:"success"}),a.listData=[],a.getData()})))}})},gohome(){this.gotoPage("/pages/index/index")}}};if(!Array){e.resolveComponent("uni-load-more")()}const o=e._export_sfc(a,[["render",function(a,o,r,i,s,d){return e.e({a:e.n(1==s.dataType?"tab-item active":"tab-item"),b:e.o((e=>d.orderStateFunc(1))),c:e.n(2==s.dataType?"tab-item active":"tab-item"),d:e.o((e=>d.orderStateFunc(2))),e:e.f(s.listData,((t,a,o)=>e.e({a:e.t(t.order_label),b:t.supplier},t.supplier?{c:e.t(t.supplier.name)}:{},{d:e.t(t.state_text),e:e.f(t.product,((t,a,o)=>e.e({a:a<=2},a<=2?{b:t.image?t.image.file_path:"",c:e.t(t.product_name),d:a}:{}))),f:e.t(t.pay_price),g:e.t(t.productNum),h:e.t(t.create_time),i:10==t.order_status.value},10==t.order_status.value?e.e({j:10==t.pay_status.value&&30!=t.order_source},10==t.pay_status.value&&30!=t.order_source?{k:e.o((e=>d.cancelOrder(t.order_id)),a)}:{},{l:10==t.pay_status.value&&30!=t.order_source},10==t.pay_status.value&&30!=t.order_source?{m:e.o((e=>d.onPayOrder(t.order_id)),a)}:{},{n:10==t.pay_status.value&&30==t.order_source&&1==t.supplier.pay_open},10==t.pay_status.value&&30==t.order_source&&1==t.supplier.pay_open?{o:e.o((e=>d.onPayOrder(t.order_id)),a)}:{}):{},{p:a,q:e.o((e=>d.gotoOrder(t.order_id)),a)}))),f:0==s.listData.length&&!s.loading},0!=s.listData.length||s.loading?{i:e.p({loadingType:d.loadingType})}:{g:t._imports_0$2,h:e.o((e=>a.gotoPage("/pages/index/index")))},{j:a.theme(),k:e.n(a.theme()||"")})}]]);wx.createPage(o);
