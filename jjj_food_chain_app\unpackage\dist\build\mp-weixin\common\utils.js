"use strict";var e={scene_decode(e){if(void 0===e)return{};let r=decodeURIComponent(e).split(","),t={};for(let s in r){var a=r[s].split(":");a.length>0&&a[0]&&(t[a[0]]=a[1]||null)}return t},format_date:e=>e.replace(/\-/g,"/"),format_content:e=>e.replace(/\<img/gi,'<img style="display:block; margin:0 auto; max-width:100%;" '),urlEncode(e){var r=[];for(var t in e){var a=e[t];a.constructor==Array?a.forEach((e=>{r.push(t+"="+e)})):r.push(t+"="+a)}return r.join("&")},objForEach(e,r){Object.keys(e).forEach((t=>{r(e[t],t)}))},inArray(e,r){for(var t in r)if(r[t]==e)return!0;return!1},isPositiveInteger:e=>/(^[0-9]\d*$)/.test(e),getSceneData(e){return e.scene?this.scene_decode(e.scene):{}},getMebutype(){},isVail(e){if(!/^\d{17}(\d|x)$/i.test(e))return!1;var r=new Date,t=Number(e.substr(6,4)),a=Number(e.substr(10,2))+1,s=Number(e.substr(12,2)),i=!1;t<=Number(r.getFullYear())&&t>0&&(a<=12&&a>0&&s<=new Date(t,a-1,0).getDate()&&s>0&&(i=!0));if(!i)return!1;var n=0;e=e.replace(/x$/i,"a");for(var u=17;u>=0;u--)n+=Math.pow(2,u)%11*parseInt(e.charAt(17-u),11);return n%11==1},isPoneAvailable:e=>!!/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(e),isTelAvailable(e){var r=!1;return/^(([0\+]\d{2,3}-)?(0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/.test(e)&&(r=!0),/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(e)&&(r=!0),!!r},isMail:e=>!!/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(e),isNum:e=>!!/^[0-9]*$/.test(e)};exports.utils=e;
