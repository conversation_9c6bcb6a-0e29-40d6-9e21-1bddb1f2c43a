"use strict";const t=require("../../../common/vendor.js"),e={data:()=>({}),props:["itemData"],created(){},methods:{gotoDetail(t){let e=this;console.log(t),t.startsWith("scanQrcode")?e[t]():e.gotoPage(t)},scanQrcode:function(){this.$emit("scanQrcode")}}};const a=t._export_sfc(e,[["render",function(e,a,o,i,r,d){return{a:t.f(o.itemData.data,((e,a,o)=>({a:e.imgUrl,b:t.t(e.title),c:e.titlecolor,d:t.t(e.text),e:e.textcolor,f:a,g:t.o((t=>d.gotoDetail(e.linkUrl)),a)}))),b:2*o.itemData.style.topRadio+"rpx ",c:2*o.itemData.style.topRadio+"rpx ",d:2*o.itemData.style.bottomRadio+"rpx ",e:2*o.itemData.style.bottomRadio+"rpx",f:"linear-gradient(to bottom, "+(o.itemData.style.background1||"#fff")+", "+(o.itemData.style.background2||"#fff")+")",g:o.itemData.style.bgcolor,h:2*o.itemData.style.paddingLeft+"rpx ",i:2*o.itemData.style.paddingLeft+"rpx ",j:2*o.itemData.style.paddingTop+"rpx ",k:2*o.itemData.style.paddingBottom+"rpx"}}],["__scopeId","data-v-9d3f1f0d"]]);wx.createComponent(a);
