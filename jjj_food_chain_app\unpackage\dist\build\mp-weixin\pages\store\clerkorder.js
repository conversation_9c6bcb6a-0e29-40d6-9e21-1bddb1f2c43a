"use strict";const t=require("../../common/vendor.js"),e={data:()=>({indicatorDots:!0,autoplay:!0,interval:2e3,duration:500,isPayPopup:!1,order_no:0,detail:{order_status:[],address:{region:[]},product:[],pay_type:[],delivery_type:[],pay_status:[]},extractStore:{}}),components:{},onLoad(t){this.order_no=t.order_no},mounted(){this.getData()},methods:{getData(){let e=this;t.index.showLoading({title:"加载中"}),e._get("store.order/detail",{order_no:e.order_no},(function(r){e.detail=r.data.order,e.extractStore=r.data.order.extractStore,t.index.hideLoading()}),(function(e){t.index.switchTab({url:"/pages/user/my_shop/my_shop"})}))},onSubmitExtract(e){let r=this;t.index.showModal({title:"提示",content:"您确定要核销吗?",success:function(a){a.confirm&&r._post("store.order/extract",{order_id:e},(function(e){t.index.showToast({title:e.msg,duration:2e3,icon:"success"}),setTimeout((function(){r.getData()}),2e3)}))}})}}};const r=t._export_sfc(e,[["render",function(e,r,a,o,d,i){return t.e({a:t.t(d.detail.state_text),b:20==d.detail.delivery_type.value},20==d.detail.delivery_type.value?{c:t.t(d.extractStore.store_name),d:t.t(d.extractStore.phone),e:t.t(d.extractStore.region.province),f:t.t(d.extractStore.region.city),g:t.t(d.extractStore.region.region),h:t.t(d.extractStore.address)}:{},{i:t.f(d.detail.product,((e,r,a)=>({a:e.image.file_path,b:t.t(e.product_name),c:t.t(e.product_price),d:t.t(e.total_num),e:r}))),j:t.t(d.detail.order_no),k:t.t(d.detail.create_time),l:t.t(d.detail.pay_type.text),m:t.t(d.detail.delivery_type.text),n:t.t(d.detail.order_price),o:t.t(d.detail.express_price),p:t.t(d.detail.order_price),q:20!=d.detail.order_status.value},20!=d.detail.order_status.value?t.e({r:20==d.detail.pay_status.value&&20==d.detail.delivery_type.value&&10==d.detail.delivery_status.value},20==d.detail.pay_status.value&&20==d.detail.delivery_type.value&&10==d.detail.delivery_status.value?{s:t.o((t=>i.onSubmitExtract(d.detail.order_id)))}:{}):{})}],["__scopeId","data-v-e9fda74e"]]);wx.createPage(r);
