<?php
//000000000000
 exit();?>
O:26:"app\common\model\app\AppWx":44:{s:19:" think\Model exists";b:1;s:18:" think\Model force";b:0;s:20:" think\Model replace";b:0;s:9:" * suffix";N;s:24:" think\Model updateWhere";a:2:{i:0;a:3:{i:0;s:21:"jjjfood_app_wx.app_id";i:1;s:1:"=";i:2;s:5:"10001";}i:1;a:4:{i:0;s:6:"app_id";i:1;s:1:"=";i:2;s:5:"10001";i:3;N;}}s:13:" * connection";N;s:7:" * name";s:6:"app_wx";s:6:" * key";N;s:8:" * table";N;s:20:" * defaultSoftDelete";N;s:14:" * globalScope";a:1:{i:0;s:6:"app_id";}s:5:" * pk";s:6:"app_id";s:9:" * schema";a:0:{}s:8:" * field";a:0:{}s:7:" * type";a:0:{}s:9:" * disuse";a:0:{}s:11:" * readonly";a:0:{}s:17:" think\Model data";a:11:{s:6:"app_id";i:10001;s:8:"wxapp_id";s:18:"wx69fd4e5acd990bef";s:12:"wxapp_secret";s:32:"e5cbe1211ab5d9942b2349e9c2db5d08";s:5:"mchid";s:0:"";s:6:"apikey";s:0:"";s:8:"cert_pem";s:0:"";s:7:"key_pem";s:0:"";s:10:"is_recycle";i:0;s:9:"is_delete";i:0;s:11:"create_time";i:1970;s:11:"update_time";i:1755428383;}s:19:" think\Model origin";a:11:{s:6:"app_id";i:10001;s:8:"wxapp_id";s:18:"wx69fd4e5acd990bef";s:12:"wxapp_secret";s:32:"e5cbe1211ab5d9942b2349e9c2db5d08";s:5:"mchid";s:0:"";s:6:"apikey";s:0:"";s:8:"cert_pem";s:0:"";s:7:"key_pem";s:0:"";s:10:"is_recycle";i:0;s:9:"is_delete";i:0;s:11:"create_time";i:1970;s:11:"update_time";i:1755428383;}s:7:" * json";a:0:{}s:11:" * jsonType";a:0:{}s:12:" * jsonAssoc";b:0;s:9:" * strict";b:1;s:16:" think\Model get";a:0:{}s:21:" think\Model withAttr";a:0:{}s:13:" * lazyFields";a:0:{}s:19:" think\Model parent";N;s:21:" think\Model relation";a:0:{}s:21:" think\Model together";a:0:{}s:16:" * relationWrite";a:0:{}s:12:" * withEvent";b:1;s:21:" * autoWriteTimestamp";s:3:"int";s:13:" * createTime";s:11:"create_time";s:13:" * updateTime";s:11:"update_time";s:13:" * dateFormat";s:11:"Y-m-d H:i:s";s:10:" * visible";a:0:{}s:9:" * hidden";a:0:{}s:9:" * append";a:0:{}s:8:" * scene";a:0:{}s:10:" * mapping";a:0:{}s:16:" * resultSetType";N;s:21:" * convertNameToCamel";N;s:8:" * alias";s:0:"";s:8:" * error";s:0:"";}