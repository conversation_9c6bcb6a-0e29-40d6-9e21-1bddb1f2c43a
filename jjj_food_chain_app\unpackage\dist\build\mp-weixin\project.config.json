{"description": "项目配置文件。", "packOptions": {"ignore": []}, "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "newFeature": true, "bigPackageSizeSupport": true}, "compileType": "miniprogram", "libVersion": "", "appid": "wx69fd4e5acd990bef", "projectname": "启云点餐", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": 0, "list": [{"name": "", "path": "", "query": "", "id": 0}]}}}