"use strict";const t=require("../../common/vendor.js"),e={name:"Modal",props:{show:{type:Boolean,default:!1},custom:{type:Boolean,default:!1},width:{type:String,default:"100%"},padding:{type:String,default:"40rpx"},radius:{type:String,default:"24rpx"},title:{type:String,default:""},content:{type:String,default:""},color:{type:String,default:"#999"},size:{type:Number,default:28},shape:{type:String,default:"square"},button:{type:Array,default:function(){return[{text:"取消",type:"red",plain:!0},{text:"确定",type:"red",plain:!1}]}},maskClosable:{type:Boolean,default:!0},fadein:{type:Boolean,default:!1}},data:()=>({}),methods:{handleClick(t){if(!this.show)return;const e=t.currentTarget.dataset;this.$emit("click",{index:Number(e.index)})},handleClickCancel(){this.maskClosable&&this.$emit("cancel")}}};const n=t._export_sfc(e,[["render",function(e,n,l,a,o,i){return t.e({a:l.custom},l.custom?{}:t.e({b:l.title},l.title?{c:t.t(l.title)}:{},{d:t.n(l.title?"":"mtop"),e:l.color,f:l.size+"rpx",g:t.f(l.button,((e,n,l)=>({a:t.t(e.text||"确定"),b:t.n((e.type||"primary")+(e.plain?"-outline":"")),c:t.n("btn-"+(e.size||"default")),d:(e.plain?"outline":e.type||"primary")+"-hover",e:n,f:t.o(((...t)=>i.handleClick&&i.handleClick(...t)),n),g:n}))),h:t.n(2!=l.button.length?"btn-width":""),i:t.n(l.button.length>2?"mbtm":""),j:t.n("circle"==l.shape?"circle-btn":""),k:t.n(2!=l.button.length?"flex-column":"")}),{l:l.width,m:l.padding,n:l.radius,o:t.n(l.fadein||l.show?"modal-normal":"modal-scale"),p:t.n(l.show?"modal-show":""),q:t.n(l.show?"mask-show":""),r:t.o(((...t)=>i.handleClickCancel&&i.handleClickCancel(...t))),s:t.o((()=>{}))})}]]);wx.createComponent(n);
