/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.o-i.data-v-292fdf7d {
  overflow: initial;
}
.data-v-292fdf7d .uni-swiper-wrapper {
  overflow: initial;
}
.diy-banner-box.data-v-292fdf7d {
  box-sizing: border-box;
  position: relative;
}
.diy-banner-box.data-v-292fdf7d,
.diy-banner-box .swiper.data-v-292fdf7d {
  width: 100%;
  height: 200%;
  /* background-color: #FFFFFF; */
}
.diy-banner-box .images.data-v-292fdf7d {
  width: 100%;
}
.diy-banner-box .swiper-dots.data-v-292fdf7d {
  position: absolute;
  bottom: 20rpx;
  left: 0;
  right: 0;
  margin: auto;
  width: 100%;
  z-index: 2;
}
.swiper-dots.square .swiper-dot.data-v-292fdf7d {
  width: 14rpx;
  height: 14rpx;
  margin: 0 4rpx;
  background: #ebedf0;
  opacity: 0.3;
}
.swiper-dots.round .swiper-dot.data-v-292fdf7d {
  width: 22rpx;
  height: 22rpx;
  margin: 0 4rpx;
  background: #ebedf0;
  opacity: 0.3;
  border-radius: 50%;
}
.swiper-dots.rectangle .swiper-dot.data-v-292fdf7d {
  width: 40rpx;
  height: 6rpx;
  margin: 0 4rpx;
  background: #ebedf0;
  opacity: 0.3;
  border-radius: 4rpx;
}
.swiper-dots.round .swiper-dot.active.data-v-292fdf7d,
.swiper-dots.square .swiper-dot.active.data-v-292fdf7d,
.swiper-dots.rectangle .swiper-dot.active.data-v-292fdf7d {
  opacity: 1;
}

/* 登录状态 */
.diy-user-wrap.data-v-292fdf7d {
  position: absolute;
  left: 50%;
  width: 100%;
  transform: translateX(-50%);
  bottom: 0;
  z-index: 1;
}
.diy-user-bg.data-v-292fdf7d {
  margin: 0 auto;
  height: 84rpx;
  box-shadow: 0px 10rpx 24rpx 0px rgba(6, 0, 1, 0.03);
  border-radius: 30rpx;
  position: relative;
  overflow: hidden;
}
.diy-user-bg .bg.data-v-292fdf7d {
  width: 100%;
  height: 100%;
  opacity: 1;
  position: absolute;
  bottom: 0;
}
.diy-user-bg .diy-user-content.data-v-292fdf7d {
  position: absolute;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.diy-user-bg .no-login-wrap.data-v-292fdf7d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}
.diy-user-bg .no-login-wrap .txt.data-v-292fdf7d {
  color: #fff;
}
.diy-user-bg .no-login-wrap .btn.data-v-292fdf7d {
  background: #fff;
  padding: 10rpx 16rpx;
  border-radius: 30rpx;
  color: #FFCC00;
}
.diy-user-bg .login-wrap.data-v-292fdf7d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  color: #fff;
}
.diy-user-bg .login-wrap .img.data-v-292fdf7d {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
}
.diy-user-bg .login-wrap .txt.data-v-292fdf7d {
  margin-left: 8rpx;
}