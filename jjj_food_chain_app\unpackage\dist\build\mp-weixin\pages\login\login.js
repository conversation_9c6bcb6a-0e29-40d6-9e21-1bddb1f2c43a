"use strict";const e=require("../../common/vendor.js"),i={data:()=>({wx_phone:!1,loading:!0,background:"",listData:[],invitation_id:0,user_id:"",mobile:!0,isRead:!1,setting:{login_desc:"",login_logo:"",name:""}}),onShow(){},onLoad(i){this.invitation_id=e.index.getStorageSync("invitation_id")||0;let o=this;this.getCodeType(),e.index.getUserInfo({success:function(i){const n=i.userInfo;console.log(n),e.index.login({provider:"weixin",success:i=>{console.log("res-login",i),o.code=i.code,"login:ok"==i.errMsg&&(o.code,n.nickName,n.avatarUrl,o._post("user.user/login",{code:o.code,source:"wx",invitation_id:o.invitation_id,referee_id:e.index.getStorageSync("referee_id")},(e=>{o.user_id=e.data.user_id,o.mobile=e.data.mobile}),!1,(()=>{o.loading=!1})))}})}})},methods:{xieyi(e){this.gotoPage("/pages/webview/ue?type="+e)},getCodeType(){let e=this;e._post("index/loginSetting",{},(i=>{e.setting=i.data.setting,e.wx_phone=i.data.setting.wx_phone}))},onNotLogin:function(){this.gotoPage("/pages/index/index")},UserLogin(){let i=this;i.loading||(i.isRead?(e.index.showLoading({title:"正在处理",mask:!0}),e.wx$1.login({success(o){let n={code:o.code};i._post("user.user/userLogin",n,(i=>{e.index.setStorageSync("token",i.data.token),e.index.setStorageSync("user_id",i.data.user_id),e.index.navigateBack()}),!1,(()=>{e.index.hideLoading()}))}})):e.index.showToast({title:"请勾选并同意《隐私政策》和《用户协议》",icon:"none"}))},getUserInfo:function(i){let o=this;if(!o.loading)if(o.isRead){if("getPhoneNumber:ok"!==i.detail.errMsg)return!1;e.index.showLoading({title:"正在处理",mask:!0}),e.wx$1.login({success(n){let t={code:n.code,user_id:o.user_id,encrypted_data:i.detail.encryptedData,iv:i.detail.iv};o._post("user.user/bindMobile",t,(i=>{e.index.setStorageSync("token",i.data.token),e.index.setStorageSync("user_id",i.data.user_id),e.index.navigateBack()}),!1,(()=>{e.index.hideLoading()}))}})}else e.index.showToast({title:"请勾选并同意《隐私政策》和《用户协议》",icon:"none"})},onGetAuthorize(i){console.log("开始授权");let o=this;e.index.login({provider:"alipay",success:function(i){console.log("sucss"),e.index.getUserInfo({provider:"alipay",success:function(e){o.aliPayLogin(i,e)}})},fail(e){console.log(e)}})},aliPayLogin(i,o){let n=this;console.log(i),console.log(o),e.index.showLoading({title:"登录中",mask:!0}),n._post("user.useralipay/login",{code:i.code,avatar:o.avatar,nickName:o.nickName},(i=>{console.log(i.data.token),e.index.setStorageSync("token",i.data.token),e.index.setStorageSync("user_id",i.data.user_id),n.gotoPage("/pages/index/index","redirect")}),!1,(()=>{n.gotoPage("/pages/index/index","redirect")}))}}};const o=e._export_sfc(i,[["render",function(i,o,n,t,s,a){return e.e({a:s.setting.login_logo||"/static/login-default.png",b:e.t(s.setting.name),c:e.t(s.setting.login_desc),d:!s.mobile},s.mobile?{h:e.o(((...e)=>a.UserLogin&&a.UserLogin(...e)))}:e.e({e:s.wx_phone},s.wx_phone?{f:e.o(((...e)=>a.getUserInfo&&a.getUserInfo(...e)))}:{g:e.o((e=>a.UserLogin()))}),{i:e.n(s.isRead?"active agreement":"agreement"),j:e.o((e=>a.xieyi("service"))),k:e.o((e=>a.xieyi("privacy"))),l:e.o((e=>s.isRead=!s.isRead)),m:e.o(((...e)=>a.onNotLogin&&a.onNotLogin(...e))),n:i.theme(),o:e.n(i.theme()||"")})}]]);wx.createPage(o);
