"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_utils = require("../../../common/utils.js");
const common_assets = require("../../../common/assets.js");
const prospec = () => "./popup/spec.js";
const popupLayer = () => "../../../components/popup-layer/popup-layer.js";
const _sfc_main = {
  components: {
    popupLayer,
    prospec
  },
  data() {
    return {
      loadingClock: false,
      isSearch: false,
      isLogin: false,
      isDetail: false,
      isLoading: true,
      goods: [],
      //所有商品
      supplier: {
        name: "",
        business_status: 0
      },
      loading: true,
      currentCateId: 0,
      //默认分类
      cateScrollTop: 0,
      menuScrollIntoView: "",
      cart: [],
      //购物车
      // good: {}, //当前饮品
      category: {},
      //当前饮品所在分类
      cartPopupVisible: false,
      sizeCalcState: false,
      listData: [],
      productList: [],
      productModel: {},
      clock: false,
      cart_total_num: 0,
      cart_product_id: 0,
      total_price: 0,
      total_bag_price: 0,
      cart_list: [],
      orderType: "takein",
      takeout_address: {},
      phoneHeight: 0,
      /*可滚动视图区域高度*/
      scrollviewHigh: 0,
      delivery_time: ["00:00", "00:00"],
      store_time: ["00:00", "00:00"],
      officeTime: {
        now: 0,
        delivery_start: 0,
        delivery_end: 0,
        store_start: 0,
        store_end: 0
      },
      addclock: false,
      longitude: 0,
      latitude: 0,
      bag_type: 1,
      shop_supplier_id: 0,
      /* 10配送20自提30店内 */
      dinner_type: 30,
      cart_type: 1,
      table_id: 0,
      order_id: 0,
      addorder_id: 0,
      discountInfo: [],
      reduce: {},
      reduce_diff_value: 0,
      line_price: 0,
      isFirst: true,
      store_set: [],
      num: 1,
      table_detail: null,
      options: {},
      settle_type: 0,
      status: "",
      meal_num: "",
      isShopDetail: false,
      supplierDetail: null
    };
  },
  onLoad(e) {
    let self = this;
    let scene = common_utils.utils.getSceneData(e);
    self.options = e;
    self.shop_supplier_id = e.shop_supplier_id ? e.shop_supplier_id : scene.sid;
    self.table_id = e.table_id ? e.table_id : scene.tid;
    if (!self.table_id) {
      self.table_id = 0;
    }
    if (self.table_id == 0) {
      self.shop_supplier_id = common_vendor.index.getStorageSync("selectedId") ? common_vendor.index.getStorageSync("selectedId") : 0;
    }
    self.num = e.num || 0;
    self.meal_num = e.meal_num || 0;
    self.status = e.status;
    self.addorder_id = e.order_id || 0;
    common_vendor.index.setNavigationBarTitle({
      title: self.table_id == 0 ? "快餐模式" : "堂食点餐"
    });
  },
  onShow() {
    let self = this;
    self.isDetail = false;
    self.getUserInfo();
  },
  computed: {
    menuCartNum() {
      return (id) => this.cart.reduce((acc, cur) => {
        if (cur.cate_id === id) {
          return acc += cur.number;
        }
        return acc;
      }, 0);
    }
  },
  methods: {
    /* 获取用户登录状态 */
    getUserInfo() {
      let self = this;
      self.loading = true;
      self.isLogin = false;
      self._get(
        "index/userInfo",
        {},
        function(res) {
          if (res.data && res.data.user) {
            self.isLogin = true;
          }
          self.init();
        }
      );
    },
    init() {
      this.addclock = false;
      this.category = {};
      this.clock = false;
      this.loading = true;
      this.isLoading = true;
      this.productList = [];
      this.sizeCalcState = false;
      this.getCategory(true);
    },
    goBack() {
      if (this.table_id > 0 && this.status == 1) {
        return;
      }
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    scrollInit() {
      let self = this;
      common_vendor.index.getSystemInfo({
        success(res) {
          self.phoneHeight = res.windowHeight;
          let view = common_vendor.index.createSelectorQuery().select(".nav");
          view.boundingClientRect((data) => {
            let h = self.phoneHeight - data.height;
            self.scrollviewHigh = h;
          }).exec();
        }
      });
    },
    takout() {
      if (this.orderType == "takeout")
        return;
      this.orderType = "takeout";
      this.dinner_type = 10;
      this.init();
    },
    takein() {
      if (this.orderType == "takein")
        return;
      this.orderType = "takein";
      this.dinner_type = 20;
      this.init();
    },
    /* 获取商品类型 */
    getCategory(flag) {
      let self = this;
      this.sizeCalcState = false;
      self._get(
        "product.category/index",
        {
          /* 0外卖分类，1堂食分类 */
          type: 1,
          shop_supplier_id: self.shop_supplier_id,
          longitude: 0,
          latitude: 0,
          table_id: self.table_id,
          /* 30 打包 40店内 */
          delivery: self.orderType == "takeout" ? 30 : 40,
          order_type: self.table_id == 0 ? 1 : 2,
          cart_type: self.cart_type || 1
        },
        function(res) {
          self.supplier = res.data.supplier;
          self.discountInfo = res.data.discountInfo;
          self.productList = res.data.list;
          if (!self.currentCateId) {
            self.currentCateId = self.productList[0].category_id;
          }
          self.store_set = res.data.supplier.store_set;
          if (self.table_id != 0) {
            self.orderType = "takein";
          }
          self.shop_supplier_id = res.data.supplier.shop_supplier_id;
          self.bag_type = res.data.supplier.storebag_type;
          if (self.orderType == "takein") {
            self.bag_type = 1;
          }
          self.loading = false;
          self.isLoading = false;
          self.$nextTick(function() {
            self.scrollInit();
          });
          if (self.isLogin && flag) {
            self.getCart();
          }
        },
        function() {
          self.gotoPage("/pages/index/index");
        }
      );
    },
    reCart(res) {
      let self = this;
      self.loadingClock = false;
      self.cart_total_num = res.data.cartInfo.cart_total_num;
      self.total_price = res.data.cartInfo.total_pay_price;
      self.line_price = res.data.cartInfo.total_line_money;
      self.total_bag_price = res.data.cartInfo.total_bag_price;
      self.reduce = res.data.cartInfo.reduce;
      self.reduce_diff_value = res.data.cartInfo.reduce_diff_value;
      if (!self.cart_list || self.cart_list == "") {
        self.cartPopupVisible = false;
      }
    },
    addCart(goods) {
      let self = this;
      if (goods.spec_types == 20) {
        self.gotoDetail(goods);
        return;
      }
      if (self.addclock) {
        return;
      }
      if (goods.limit_num != 0 && goods.limit_num <= goods.cart_num) {
        common_vendor.index.showToast({
          icon: "none",
          title: "超过限购数量"
        });
        return;
      }
      if (goods.product_stock <= 0 || goods.product_stock <= goods.cart_num) {
        common_vendor.index.showToast({
          icon: "none",
          title: "没有更多库存了"
        });
        return;
      }
      let params = {
        product_id: goods.product_id,
        product_num: 1,
        product_sku_id: goods.sku[0].product_sku_id,
        attr: "",
        feed: "",
        describe: "",
        price: goods.sku[0].product_price,
        bag_price: goods.sku[0].bag_price,
        shop_supplier_id: self.shop_supplier_id,
        table_id: self.table_id,
        cart_type: 1,
        dinner_type: self.dinner_type,
        product_price: goods.sku[0].line_price,
        delivery: self.orderType == "takeout" ? 30 : 40
      };
      self.addclock = true;
      let url = "order.cart/add";
      common_vendor.index.showLoading({
        title: "",
        mask: true
      });
      self._post(
        url,
        params,
        function(res) {
          self.reCart(res);
          common_vendor.index.hideLoading();
          self.addclock = false;
          self.getCategory(false);
        },
        function(err) {
          common_vendor.index.hideLoading();
          self.addclock = false;
        }
      );
    },
    reduceFunc(goods) {
      let self = this;
      if (self.addclock || goods.cart_num <= 0) {
        return;
      }
      if (goods.spec_types == 20) {
        self.openCartPopup(goods.product_id);
        return;
      }
      let product_id = goods.product_id;
      self.addclock = true;
      let url = "order.cart/productSub";
      common_vendor.index.showLoading({
        title: "",
        mask: true
      });
      self._post(
        url,
        {
          product_id,
          type: "down",
          cart_type: 1,
          shop_supplier_id: self.shop_supplier_id,
          table_id: self.table_id,
          dinner_type: self.dinner_type,
          delivery: self.orderType == "takeout" ? 30 : 40
        },
        function(res) {
          common_vendor.index.hideLoading();
          self.reCart(res);
          self.addclock = false;
          self.getCategory(false);
        },
        function() {
          common_vendor.index.hideLoading();
          self.addclock = false;
        }
      );
    },
    getCartLoading() {
      let self = this;
      let url = "order.cart/lists";
      let params = {
        shop_supplier_id: self.shop_supplier_id,
        cart_type: 1,
        delivery: self.orderType == "takeout" ? 30 : 40,
        product_id: self.cart_product_id,
        table_id: self.table_id || 0
      };
      let callBack = function(res) {
        common_vendor.index.hideLoading();
        self.isLoading = true;
        self.reCart(res);
        self.cart_list = res.data.productList;
      };
      self._get(
        url,
        params,
        function(res) {
          callBack(res);
        },
        (err) => {
          common_vendor.index.hideLoading();
        }
      );
    },
    getCart() {
      let self = this;
      if (!self.isLogin) {
        return;
      }
      return new Promise((resolve, reject) => {
        let url = "order.cart/lists";
        let params = {
          shop_supplier_id: self.shop_supplier_id,
          cart_type: 1,
          delivery: self.orderType == "takeout" ? 30 : 40,
          product_id: self.cart_product_id,
          table_id: self.table_id || 0
        };
        let callBack = function(res) {
          common_vendor.index.hideLoading();
          self.isLoading = true;
          self.reCart(res);
          self.cart_list = res.data.productList;
        };
        common_vendor.index.showLoading({
          title: "",
          mask: true
        });
        self._get(
          url,
          params,
          function(res) {
            callBack(res);
            resolve(true);
          },
          (err) => {
            common_vendor.index.hideLoading();
          }
        );
      });
    },
    /* 购物车商品添加 */
    cartAdd(goods) {
      let self = this;
      if (self.addclock) {
        return;
      }
      self.addclock = true;
      let product_id = goods.product_id;
      let total_num = 1;
      let url = "order.cart/sub";
      common_vendor.index.showLoading({
        title: "",
        mask: true
      });
      self._post(
        url,
        {
          product_id,
          total_num,
          cart_id: goods.cart_id,
          type: "up",
          cart_type: 1,
          delivery: self.orderType == "takeout" ? 30 : 40,
          dinner_type: self.dinner_type,
          shop_supplier_id: self.shop_supplier_id,
          table_id: self.table_id
        },
        function(res) {
          self.addclock = false;
          self.getCategory(false);
          self.getCartLoading();
        },
        function() {
          common_vendor.index.hideLoading();
          self.addclock = false;
        }
      );
    },
    /* 购物车商品减少 */
    cartReduce(goods) {
      let self = this;
      if (self.addclock) {
        return;
      }
      self.addclock = true;
      let product_id = goods.product_id;
      let url = "order.cart/sub";
      common_vendor.index.showLoading({
        title: "",
        mask: true
      });
      self._post(
        url,
        {
          product_id,
          total_num: 1,
          cart_id: goods.cart_id,
          type: "down",
          cart_type: 1,
          dinner_type: self.dinner_type,
          shop_supplier_id: self.shop_supplier_id,
          table_id: self.table_id,
          delivery: self.orderType == "takeout" ? 30 : 40
        },
        function(res) {
          self.addclock = false;
          self.getCategory(false);
          self.getCartLoading();
        },
        function() {
          common_vendor.index.hideLoading();
          self.addclock = false;
        }
      );
    },
    //清空购物车
    handleCartClear() {
      let self = this;
      common_vendor.index.showModal({
        title: "提示",
        content: "确定清空购物车么",
        success(res) {
          if (res.confirm) {
            self.clearCart();
          } else if (res.cancel) {
            common_vendor.index.__f__("log", "at pages/product/list/store.vue:682", "用户点击取消");
          }
        }
      });
    },
    /*  清空 */
    clearCart() {
      let self = this;
      let url = "order.cart/delete";
      let params = {
        shop_supplier_id: self.shop_supplier_id,
        cart_type: 1,
        table_id: self.table_id || 0
      };
      self._post(url, params, function(res) {
        self.cartPopupVisible = false;
        self.cart_list = [];
        self.init();
      });
    },
    /* 提交 */
    toPay() {
      let self = this;
      self.fastToPay();
    },
    /* 快餐结算 */
    fastToPay() {
      let self = this;
      self.loadingClock = true;
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      let delivery = self.orderType == "takeout" ? 30 : 40;
      self._get(
        "order.cart/lists",
        {
          shop_supplier_id: self.shop_supplier_id,
          cart_type: 1,
          delivery,
          table_id: self.table_id || 0
        },
        function(res) {
          self.reCart(res);
          self.cart_list = res.data.productList;
          let arrIds = [];
          self.cart_list.forEach((item) => {
            arrIds.push(item.cart_id);
          });
          if (arrIds.length == 0) {
            common_vendor.index.showToast({
              title: "请选择商品",
              icon: "none"
            });
            return false;
          }
          common_vendor.index.hideLoading();
          common_vendor.index.navigateTo({
            url: "/pages/order/confirm-order?order_type=cart&cart_ids=" + arrIds.join(",") + "&delivery=" + delivery + "&shop_supplier_id=" + self.shop_supplier_id + "&cart_type=1&dinner_type=30&table_id=" + self.table_id
          });
        },
        (err) => {
          self.loadingClock = false;
        }
      );
    },
    /* 加餐 */
    addpay() {
      let self = this;
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      self.addpayFunc();
    },
    /* 快餐模式加餐 */
    addpayFunc() {
      let self = this;
      self._get(
        "order.cart/lists",
        {
          shop_supplier_id: self.shop_supplier_id,
          cart_type: 1,
          delivery: self.orderType == "takeout" ? 30 : 40,
          table_id: self.table_id || 0
        },
        function(res) {
          self.cart_total_num = res.data.cartInfo.cart_total_num;
          self.total_price = res.data.cartInfo.total_price;
          self.cart_list = res.data.productList;
          let arrIds = [];
          self.cart_list.forEach((item) => {
            arrIds.push(item.cart_id);
          });
          if (arrIds.length == 0) {
            common_vendor.index.showToast({
              title: "请选择商品",
              icon: "none"
            });
            return false;
          }
          common_vendor.index.hideLoading();
          let url = "/pages/order/addorder?order_type=cart&cart_ids=" + arrIds.join(",") + "&delivery=40&shop_supplier_id=" + self.shop_supplier_id + "&cart_type=1&dinner_type=30&table_id=" + self.table_id + "&order_id=" + self.addorder_id;
          self.gotoPage(url);
        }
      );
    },
    /* 新 打开商品详情弹窗 */
    gotoDetail(e) {
      let self = this;
      let delivery = self.orderType == "takeout" ? 30 : 40;
      self.productModel = {
        product_id: e.product_id || 0,
        delivery,
        bag_type: self.bag_type || 0,
        dinner_type: self.dinner_type || 0,
        cart_type: self.cart_type || 0,
        table_id: self.table_id || 0,
        shop_supplier_id: self.shop_supplier_id || 0
      };
      self.isDetail = true;
    },
    //点击菜单项事件
    handleMenuTap(id) {
      let self = this;
      if (!self.sizeCalcState) {
        self.calcSize();
      }
      self.currentCateId = id;
      self.$nextTick(() => {
        self.cateScrollTop = self.productList.find((item) => item.category_id == id).top;
        if (!self.cateScrollTop && self.cateScrollTop != 0) {
          setTimeout(function() {
            self.handleMenuTap(id);
          }, 200);
        }
      });
    },
    //商品列表滚动事件
    handleGoodsScroll({
      detail
    }) {
      if (!this.sizeCalcState) {
        this.calcSize();
      }
      const {
        scrollTop
      } = detail;
      let tabs = this.productList.filter((item) => item.top - 5 <= scrollTop).reverse();
      if (tabs.length > 0) {
        this.currentCateId = tabs[0].category_id;
      }
    },
    calcSize() {
      let h = 0;
      this.productList.forEach((item) => {
        let view = common_vendor.index.createSelectorQuery().select(`#cate-${item.category_id}`);
        view.fields(
          {
            size: true
          },
          (data) => {
            item.top = h;
            if (data != null) {
              h += data.height;
            }
            item.bottom = h;
          }
        ).exec();
      });
      this.sizeCalcState = true;
    },
    closeGoodDetailModal(num, res) {
      this.isDetail = false;
      this.clock = false;
      if (num) {
        this.reCart(res);
        this.getCategory(false);
      }
    },
    async openCartPopup(n) {
      if (!this.cartPopupVisible) {
        this.cart_list = [];
        this.cart_product_id = n;
        await this.getCart();
        this.cartPopupVisible = !this.cartPopupVisible;
      } else {
        this.cartPopupVisible = !this.cartPopupVisible;
      }
    },
    closeCallBack() {
      this.cart_product_id = 0;
      this.cart_list = [];
      this.cartPopupVisible = false;
    }
  }
};
if (!Array) {
  const _easycom_popup_layer2 = common_vendor.resolveComponent("popup-layer");
  const _component_prospec = common_vendor.resolveComponent("prospec");
  (_easycom_popup_layer2 + _component_prospec)();
}
const _easycom_popup_layer = () => "../../../components/popup-layer/popup-layer.js";
if (!Math) {
  _easycom_popup_layer();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.supplier.name),
    b: $data.table_id == 0
  }, $data.table_id == 0 ? {
    c: common_vendor.f($data.store_set, (item, index, i0) => {
      return common_vendor.e({
        a: item == "30"
      }, item == "30" ? {
        b: $data.orderType == "takeout" ? 1 : "",
        c: common_vendor.o((...args) => $options.takout && $options.takout(...args), item)
      } : {}, {
        d: item
      });
    }),
    d: common_vendor.f($data.store_set, (item, index, i0) => {
      return common_vendor.e({
        a: item == "40"
      }, item == "40" ? {
        b: $data.orderType == "takein" ? 1 : "",
        c: common_vendor.o((...args) => $options.takein && $options.takein(...args), item)
      } : {}, {
        d: item
      });
    })
  } : {}, {
    e: $data.table_id != 0 && $data.table_detail ? 1 : "",
    f: common_vendor.f($data.productList, (item, index, i0) => {
      return common_vendor.e({
        a: item.products.length != 0
      }, item.products.length != 0 ? common_vendor.e({
        b: item.images
      }, item.images ? {
        c: item.images.file_path
      } : {}, {
        d: common_vendor.t(item.name),
        e: item.product_num
      }, item.product_num ? {
        f: common_vendor.t(item.product_num)
      } : {}, {
        g: `menu-${item.category_id}`,
        h: item.category_id === $data.currentCateId ? 1 : "",
        i: common_vendor.o(($event) => $options.handleMenuTap(item.category_id), index)
      }) : {}, {
        j: index
      });
    }),
    g: common_vendor.s("height:" + $data.scrollviewHigh + "px;"),
    h: $data.menuScrollIntoView,
    i: common_vendor.f($data.productList, (item, index, i0) => {
      return common_vendor.e({
        a: item.products.length != 0
      }, item.products.length != 0 ? {
        b: common_vendor.t(item.name),
        c: common_vendor.f(item.products, (good, key, i1) => {
          return common_vendor.e({
            a: good.product_stock <= 0
          }, good.product_stock <= 0 ? {} : {}, {
            b: good.product_image,
            c: common_vendor.t(good.product_name),
            d: common_vendor.t(good.selling_point),
            e: common_vendor.t(good.product_price),
            f: good.product_price * 1 != good.line_price * 1
          }, good.product_price * 1 != good.line_price * 1 ? {
            g: common_vendor.t(good.line_price * 1)
          } : {}, {
            h: good.cart_num != 0
          }, good.cart_num != 0 ? {
            i: common_vendor.o(($event) => $options.reduceFunc(good), key),
            j: "/static/icon/cart/reduce-" + _ctx.theme() + ".png"
          } : {}, {
            k: good.cart_num != 0
          }, good.cart_num != 0 ? {
            l: common_vendor.t(good.cart_num)
          } : {}, {
            m: good.product_stock > 0
          }, good.product_stock > 0 ? {
            n: common_vendor.o(($event) => $options.addCart(good), key),
            o: "/static/icon/cart/add-" + _ctx.theme() + ".png"
          } : {}, {
            p: good.product_stock <= 0
          }, good.product_stock <= 0 ? {
            q: common_assets._imports_0$3
          } : {}, {
            r: common_vendor.o(($event) => $options.gotoDetail(good), key),
            s: key
          });
        }),
        d: `cate-${item.category_id}`
      } : {}, {
        e: index
      });
    }),
    j: common_vendor.s("padding-bottom:" + ($data.scrollviewHigh * 2 - 238) + "rpx;"),
    k: common_vendor.s("height:" + $data.scrollviewHigh + "px;"),
    l: $data.cateScrollTop,
    m: common_vendor.o((...args) => $options.handleGoodsScroll && $options.handleGoodsScroll(...args)),
    n: $data.cart_total_num
  }, $data.cart_total_num ? {
    o: common_vendor.t($data.cart_total_num)
  } : {}, {
    p: $data.cart_total_num
  }, $data.cart_total_num ? {
    q: common_vendor.t($data.total_price),
    r: common_vendor.t($data.line_price)
  } : {}, {
    s: $data.table_id != 0
  }, $data.table_id != 0 ? {
    t: common_vendor.o((...args) => $options.toPay && $options.toPay(...args))
  } : common_vendor.e({
    v: $data.addorder_id == 0
  }, $data.addorder_id == 0 ? {
    w: common_vendor.o((...args) => $options.toPay && $options.toPay(...args))
  } : {}), {
    x: common_vendor.o(($event) => $options.openCartPopup(0)),
    y: $data.cart_total_num > 0
  }, $data.cart_total_num > 0 ? common_vendor.e({
    z: $data.total_bag_price
  }, $data.total_bag_price ? {
    A: common_vendor.t($data.total_bag_price)
  } : {}, {
    B: common_vendor.o((...args) => $options.handleCartClear && $options.handleCartClear(...args)),
    C: common_vendor.f($data.cart_list, (item, index, i0) => {
      return common_vendor.e({
        a: item.product_num > 0
      }, item.product_num > 0 ? common_vendor.e({
        b: item.image.file_path,
        c: common_vendor.t(item.product.product_name),
        d: common_vendor.t(item.describe),
        e: common_vendor.t(item.price),
        f: $data.bag_type != 1
      }, $data.bag_type != 1 ? {
        g: common_vendor.t(item.bag_price)
      } : {}, {
        h: common_vendor.o(($event) => $options.cartReduce(item), index),
        i: "/static/icon/cart/reduce-" + _ctx.theme() + ".png",
        j: common_vendor.t(item.product_num),
        k: common_vendor.o(($event) => $options.cartAdd(item), index),
        l: "/static/icon/cart/add-" + _ctx.theme() + ".png"
      }) : {}, {
        m: index
      });
    }),
    D: common_vendor.o($options.closeCallBack),
    E: common_vendor.p({
      direction: "top",
      ["show-pop"]: $data.cartPopupVisible
    })
  }) : {}, {
    F: $data.isDetail
  }, $data.isDetail ? {
    G: common_vendor.o($options.closeGoodDetailModal),
    H: common_vendor.p({
      productModel: $data.productModel
    })
  } : {}, {
    I: $data.loading
  }, $data.loading ? {
    J: common_assets._imports_1$1
  } : {}, {
    K: _ctx.theme(),
    L: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/product/list/store.js.map
