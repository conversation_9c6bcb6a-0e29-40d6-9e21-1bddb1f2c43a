"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      /*是否加载完成*/
      loadding: true,
      indicatorDots: true,
      autoplay: true,
      interval: 2e3,
      duration: 500,
      /*数据*/
      listData: [],
      /*默认地址id*/
      default_id: "0",
      options: {}
    };
  },
  onLoad: function(options) {
    this.options = options;
  },
  onShow: function() {
    common_vendor.index.showLoading({
      title: "加载中"
    });
    this.getData();
  },
  methods: {
    /*获取数据*/
    getData() {
      let self = this;
      self.dataType;
      self._get("user.address/list", {
        shop_supplier_id: self.options.shop_supplier_id
      }, function(res) {
        self.listData = res.data.list;
        self.default_id = res.data.default_id + "";
        self.loadding = false;
        common_vendor.index.hideLoading();
      });
    },
    /*跳转页面*/
    addAddress() {
      let delta = 1;
      if (this.options.source === "order") {
        delta = 2;
      }
      common_vendor.index.navigateTo({
        url: "/pages/user/address/add/add?delta=" + delta
      });
    },
    /*点击单选*/
    radioChange(e) {
      let self = this;
      self.default_id = e;
      self._post("user.address/setDefault", {
        address_id: e
      }, function(res) {
        self.$fire.fire("takeout", true);
        common_vendor.index.navigateBack();
      });
      return false;
    },
    /*编辑地址*/
    editAddress(e) {
      common_vendor.index.navigateTo({
        url: "/pages/user/address/edit/edit?address_id=" + e
      });
    },
    /*删除地址*/
    delAddress(e) {
      let self = this;
      common_vendor.index.showModal({
        title: "提示",
        content: "您确定要移除当前收货地址吗?",
        success: function(o) {
          o.confirm && self._get("user.address/delete", {
            address_id: e
          }, function(result) {
            if (result.code == 1) {
              common_vendor.index.showToast({
                title: "删除成功",
                duration: 2e3
              });
              self.getData();
            }
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.loadding
  }, !$data.loadding ? common_vendor.e({
    b: $data.listData.length > 0
  }, $data.listData.length > 0 ? {
    c: common_vendor.f($data.listData, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.phone),
        c: common_vendor.t(item.detail),
        d: common_vendor.t(item.address),
        e: common_vendor.t(item.distance),
        f: item.status == 0
      }, item.status == 0 ? {} : {}, {
        g: common_vendor.o(($event) => $options.delAddress(item.address_id), index),
        h: common_vendor.o(($event) => $options.editAddress(item.address_id), index),
        i: item.address_id + "",
        j: $data.default_id == item.address_id + "",
        k: common_vendor.o(($event) => $options.radioChange(item.address_id), index),
        l: index
      });
    }),
    d: common_assets._imports_0$1,
    e: common_assets._imports_1,
    f: _ctx.getThemeColor(),
    g: common_vendor.o(($event) => $options.addAddress())
  } : {
    h: common_assets._imports_2,
    i: common_vendor.o(($event) => $options.addAddress()),
    j: common_vendor.o(($event) => _ctx.gotoPage("/pages/index/index"))
  }, {
    k: _ctx.theme(),
    l: common_vendor.n(_ctx.theme() || "")
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/user/address/storeaddress.js.map
