/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
page {
  background-color: #FFFFFF;
}
.address-list {
  border-top: 16rpx solid #F2F2F2;
  padding: 0 20rpx;
  padding-bottom: 90rpx;
}
.foot-btns {
  padding: 0;
}
.foot-btns .btn-red {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 0;
}
.none_add {
  padding: 314rpx 214rpx 60rpx 214rpx;
}
.no_add {
  width: 322rpx;
  height: 180rpx;
}
.no_add_add {
  width: 320rpx;
  height: 80rpx;
  border-radius: 40rpx;
  text-align: center;
  line-height: 80rpx;
  font-size: 32rpx;
  font-family: PingFang SC;
  font-weight: 500;
  border: 2rpx solid;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.gray-btn {
  color: #666666;
  border-color: #666666;
}
[data-theme=theme0] .red-btn {
  color: #fee238 !important;
}
[data-theme=theme1] .red-btn {
  color: #b91d32 !important;
}
[data-theme=theme2] .red-btn {
  color: #09b4f1 !important;
}
[data-theme=theme3] .red-btn {
  color: #00a348 !important;
}
[data-theme=theme4] .red-btn {
  color: #fd8103 !important;
}
[data-theme=theme5] .red-btn {
  color: #b99970 !important;
}
[data-theme=theme6] .red-btn {
  color: #a4a4d6 !important;
}
[data-theme=theme0] .red-btn {
  border-color: #fee238 !important;
}
[data-theme=theme1] .red-btn {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .red-btn {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .red-btn {
  border-color: #00a348 !important;
}
[data-theme=theme4] .red-btn {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .red-btn {
  border-color: #b99970 !important;
}
[data-theme=theme6] .red-btn {
  border-color: #a4a4d6 !important;
}
.add_add {
  height: 64rpx;
  line-height: 64rpx;
  font-size: 26rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #0777CD;
  padding: 0 35rpx;
  border-bottom: 1rpx solid #D9D9D9;
}
.defaul_add {
  padding: 9rpx 14rpx 10rpx 15rpx;
  background: #FFE7E4;
  font-size: 22rpx;
  font-family: PingFang SC;
  font-weight: 500;
  color: #F6220C;
}
[data-theme=theme0] .defaul_add {
  color: #fee238 !important;
}
[data-theme=theme1] .defaul_add {
  color: #b91d32 !important;
}
[data-theme=theme2] .defaul_add {
  color: #09b4f1 !important;
}
[data-theme=theme3] .defaul_add {
  color: #00a348 !important;
}
[data-theme=theme4] .defaul_add {
  color: #fd8103 !important;
}
[data-theme=theme5] .defaul_add {
  color: #b99970 !important;
}
[data-theme=theme6] .defaul_add {
  color: #a4a4d6 !important;
}
.add_icon_img {
  width: 30rpx;
  height: 30rpx;
}
.none_line {
  width: 1rpx;
  height: 44rpx;
  background: #D9D9D9;
}
.add_add-btn {
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  width: 710rpx;
  margin: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.pbenv {
  padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
  box-sizing: border-box;
}