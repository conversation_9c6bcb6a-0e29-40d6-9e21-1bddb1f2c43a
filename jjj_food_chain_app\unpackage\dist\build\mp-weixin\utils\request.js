"use strict";const e=require("../common/vendor.js");exports.requestFun=function(t){t.config.globalProperties._get=function(t,o,n,i,a){console.log(this.websiteUrl,"llll"),(o=o||{}).token=e.index.getStorageSync("token")||"",o.app_id=this.getAppId(),e.index.request({url:this.websiteUrl+"/index.php/api/"+t,data:o,dataType:"json",method:"GET",header:{appId:this.getAppId()},success:t=>{if(200!==t.statusCode||"object"!=typeof t.data)return!1;if(-2===t.data.code)this.showError(t.data.msg,(function(){e.index.removeStorageSync("token")}));else if(-1===t.data.code)console.log("登录态失效, 重新登录"),this.doLogin();else{if(0===t.data.code)return this.showError(t.data.msg,(function(){i&&i(t)})),!1;if(10===t.data.code)return this.showError(t.data.msg,(function(){e.index.removeStorageSync("selectedId"),e.index.reLaunch({url:"/pages/index/index"})})),!1;n&&n(t.data)}},fail:e=>{i&&i(e)},complete:t=>{e.index.hideLoading(),a&&a(t)}})},t.config.globalProperties._post=function(t,o,n,i,a){(o=o||{}).token=e.index.getStorageSync("token")||"",o.app_id=this.getAppId(),e.index.request({url:this.websiteUrl+"/index.php/api/"+t,data:o,dataType:"json",method:"POST",header:{"content-type":"application/x-www-form-urlencoded",appId:this.getAppId()},success:e=>{if(200!==e.statusCode||"object"!=typeof e.data)return!1;if(-1===e.data.code)console.log("登录态失效, 重新登录"),this.doLogin();else{if(0===e.data.code)return this.showError(e.data.msg,(function(){i&&i(e)})),!1;n&&n(e.data)}},fail:e=>{i&&i(e)},complete:t=>{e.index.hideLoading(),a&&a(t)}})},t.config.globalProperties.doLogin=function(){let t=getCurrentPages();if(t.length){let o=t[t.length-1];"pages/login/login"!=o.route&&"pages/login/weblogin"!=o.route&&"pages/login/openlogin"!=o.route&&(e.index.setStorageSync("currentPage",o.route),e.index.setStorageSync("currentPageOptions",o.options))}console.log("app_ID="+this.getAppId()),this.gotoPage("/pages/login/login")}};
