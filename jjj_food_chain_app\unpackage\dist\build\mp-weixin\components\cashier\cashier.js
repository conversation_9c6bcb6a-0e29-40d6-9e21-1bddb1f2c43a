"use strict";const e=require("../../common/vendor.js"),p=require("../../common/assets.js"),o={data:()=>({checkedPay:[10,20],pay_type:10}),props:["isPayPopup"],methods:{hidePopupFunc(e){this.$emit("close",e)},payTypeFunc(e){this.pay_type=e},subFunc(){this.$emit("submit",this.pay_type)}}};const s=e._export_sfc(o,[["render",function(o,s,c,t,n,u){return{a:e.o((e=>u.hidePopupFunc(null))),b:p._imports_0$4,c:e.f(n.checkedPay,((p,o,s)=>e.e({a:20==p},{},{b:30==p},{},{c:10==p},{},{d:e.n(n.pay_type==p?"active":""),e:e.o((e=>u.payTypeFunc(p)),o),f:o}))),d:e.o(((...e)=>u.subFunc&&u.subFunc(...e))),e:e.o((()=>{})),f:e.n(c.isPayPopup?"pop-bg open cashier-pop":"pop-bg close cashier-pop"),g:e.o((()=>{}))}}]]);wx.createComponent(s);
