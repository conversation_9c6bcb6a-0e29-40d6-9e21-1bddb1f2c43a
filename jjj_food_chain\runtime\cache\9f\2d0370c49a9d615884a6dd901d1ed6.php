<?php
//000000000000
 exit();?>
O:22:"think\model\Collection":1:{s:8:" * items";a:2:{i:0;O:32:"app\common\model\page\CenterMenu":44:{s:19:" think\Model exists";b:1;s:18:" think\Model force";b:0;s:20:" think\Model replace";b:0;s:9:" * suffix";N;s:24:" think\Model updateWhere";N;s:13:" * connection";N;s:7:" * name";s:11:"center_menu";s:6:" * key";N;s:8:" * table";N;s:20:" * defaultSoftDelete";N;s:14:" * globalScope";a:1:{i:0;s:6:"app_id";}s:5:" * pk";s:7:"menu_id";s:9:" * schema";a:0:{}s:8:" * field";a:0:{}s:7:" * type";a:0:{}s:9:" * disuse";a:0:{}s:11:" * readonly";a:0:{}s:17:" think\Model data";a:11:{s:7:"menu_id";i:1;s:5:"title";s:12:"收货地址";s:9:"image_url";s:22:"image/menu/address.png";s:4:"sort";i:1;s:8:"link_url";s:27:"/pages/user/address/address";s:4:"name";s:12:"收货地址";s:7:"sys_tag";s:7:"address";s:6:"status";i:1;s:6:"app_id";i:10001;s:11:"create_time";i:1723513276;s:11:"update_time";i:1723513276;}s:19:" think\Model origin";a:11:{s:7:"menu_id";i:1;s:5:"title";s:12:"收货地址";s:9:"image_url";s:22:"image/menu/address.png";s:4:"sort";i:1;s:8:"link_url";s:27:"/pages/user/address/address";s:4:"name";s:12:"收货地址";s:7:"sys_tag";s:7:"address";s:6:"status";i:1;s:6:"app_id";i:10001;s:11:"create_time";i:1723513276;s:11:"update_time";i:1723513276;}s:7:" * json";a:0:{}s:11:" * jsonType";a:0:{}s:12:" * jsonAssoc";b:0;s:9:" * strict";b:1;s:16:" think\Model get";a:0:{}s:21:" think\Model withAttr";a:0:{}s:13:" * lazyFields";a:0:{}s:19:" think\Model parent";N;s:21:" think\Model relation";a:0:{}s:21:" think\Model together";a:0:{}s:16:" * relationWrite";a:0:{}s:12:" * withEvent";b:1;s:21:" * autoWriteTimestamp";s:3:"int";s:13:" * createTime";s:11:"create_time";s:13:" * updateTime";s:11:"update_time";s:13:" * dateFormat";s:11:"Y-m-d H:i:s";s:10:" * visible";a:0:{}s:9:" * hidden";a:0:{}s:9:" * append";a:0:{}s:8:" * scene";a:0:{}s:10:" * mapping";a:0:{}s:16:" * resultSetType";N;s:21:" * convertNameToCamel";N;s:8:" * alias";s:0:"";s:8:" * error";s:0:"";}i:1;O:32:"app\common\model\page\CenterMenu":44:{s:19:" think\Model exists";b:1;s:18:" think\Model force";b:0;s:20:" think\Model replace";b:0;s:9:" * suffix";N;s:24:" think\Model updateWhere";N;s:13:" * connection";N;s:7:" * name";s:11:"center_menu";s:6:" * key";N;s:8:" * table";N;s:20:" * defaultSoftDelete";N;s:14:" * globalScope";a:1:{i:0;s:6:"app_id";}s:5:" * pk";s:7:"menu_id";s:9:" * schema";a:0:{}s:8:" * field";a:0:{}s:7:" * type";a:0:{}s:9:" * disuse";a:0:{}s:11:" * readonly";a:0:{}s:17:" think\Model data";a:11:{s:7:"menu_id";i:2;s:5:"title";s:6:"设置";s:9:"image_url";s:22:"image/menu/setting.png";s:4:"sort";i:2;s:8:"link_url";s:19:"/pages/user/set/set";s:4:"name";s:6:"设置";s:7:"sys_tag";s:7:"setting";s:6:"status";i:1;s:6:"app_id";i:10001;s:11:"create_time";i:1723513276;s:11:"update_time";i:1723513276;}s:19:" think\Model origin";a:11:{s:7:"menu_id";i:2;s:5:"title";s:6:"设置";s:9:"image_url";s:22:"image/menu/setting.png";s:4:"sort";i:2;s:8:"link_url";s:19:"/pages/user/set/set";s:4:"name";s:6:"设置";s:7:"sys_tag";s:7:"setting";s:6:"status";i:1;s:6:"app_id";i:10001;s:11:"create_time";i:1723513276;s:11:"update_time";i:1723513276;}s:7:" * json";a:0:{}s:11:" * jsonType";a:0:{}s:12:" * jsonAssoc";b:0;s:9:" * strict";b:1;s:16:" think\Model get";a:0:{}s:21:" think\Model withAttr";a:0:{}s:13:" * lazyFields";a:0:{}s:19:" think\Model parent";N;s:21:" think\Model relation";a:0:{}s:21:" think\Model together";a:0:{}s:16:" * relationWrite";a:0:{}s:12:" * withEvent";b:1;s:21:" * autoWriteTimestamp";s:3:"int";s:13:" * createTime";s:11:"create_time";s:13:" * updateTime";s:11:"update_time";s:13:" * dateFormat";s:11:"Y-m-d H:i:s";s:10:" * visible";a:0:{}s:9:" * hidden";a:0:{}s:9:" * append";a:0:{}s:8:" * scene";a:0:{}s:10:" * mapping";a:0:{}s:16:" * resultSetType";N;s:21:" * convertNameToCamel";N;s:8:" * alias";s:0:"";s:8:" * error";s:0:"";}}}