/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.banner {
  position: relative;
  width: 100%;
  height: 600rpx;
}
.banner .bg {
  width: 100%;
  height: 600rpx;
}
.banner .intro {
  position: absolute;
  top: calc(50rpx + var(--status-bar-height));
  left: 40rpx;
  color: #ffffff;
  display: flex;
  flex-direction: column;
}
.banner .intro .greet {
  font-size: 32rpx;
  margin-bottom: 10rpx;
}
.banner .intro .note {
  font-size: 24rpx;
}
.content {
  padding: 0 30rpx;
}
.entrance {
  position: relative;
  margin-top: -80rpx;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
  background-color: #ffffff;
  box-shadow: 0 20rpx 20rpx -20rpx rgba(51, 51, 51, 0.1);
  padding: 40rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.entrance .item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}
.entrance .item:nth-child(1):after {
  content: "";
  position: absolute;
  width: 1rpx;
  background-color: #ddd;
  right: 0;
  height: 100%;
  transform: scaleX(0.5) scaleY(0.8);
}
.entrance .item .icon {
  width: 120rpx;
  height: 120rpx;
  margin: 28rpx;
}
.entrance .item .title {
  font-size: 40rpx;
  color: #5a5b5c;
  font-weight: 600;
}
.entrance .item .content {
  font-size: 28rpx;
  color: #878889;
  font-weight: 400;
}
.info {
  position: relative;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
  background-color: #ffffff;
  box-shadow: 0 20rpx 20rpx -20rpx rgba(51, 51, 51, 0.1);
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.info .integral_section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.info .integral_section .top {
  display: flex;
  align-items: center;
}
.info .integral_section .top .title {
  color: #5a5b5c;
  font-size: 28rpx;
  margin-right: 10rpx;
}
.info .integral_section .top .value {
  font-size: 44rpx;
  font-weight: bold;
}
.info .integral_section .bottom {
  font-size: 24rpx;
  color: #919293;
  display: flex;
  align-items: center;
}
.info .qrcode_section {
  color: #f5a654;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}
.info .qrcode_section image {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 10rpx;
}
.navigators {
  width: 100%;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  background-color: #ffffff;
  box-shadow: 0 20rpx 20rpx -20rpx rgba(51, 51, 51, 0.1);
  padding: 20rpx;
  display: flex;
  align-items: stretch;
}
.navigators .left {
  width: 340rpx;
  margin-right: 20rpx;
  display: flex;
  padding: 0 20rpx;
  flex-direction: column;
  font-size: 24rpx;
  color: #5a5b5c;
  background-color: #f2f2e6;
}
.navigators .left .grid {
  height: 50%;
  display: flex;
}
.navigators .right {
  width: 290rpx;
  display: flex;
  flex-direction: column;
}
.navigators .right .tea-activity,
.navigators .right .member-gifts {
  width: 100%;
  display: flex;
  padding: 20rpx;
  font-size: 24rpx;
  color: #5a5b5c;
  align-items: center;
  position: relative;
}
.navigators .right .tea-activity {
  background-color: #fdf3f2;
  margin-bottom: 20rpx;
}
.navigators .right .member-gifts {
  background-color: #fcf6d4;
}
.navigators .right .right-img {
  flex: 1;
  position: relative;
  margin-left: 20rpx;
  margin-right: -20rpx;
  margin-bottom: -20rpx;
  display: flex;
  align-items: flex-end;
}
.navigators .right .right-img image {
  width: 100%;
}
.navigators .mark-img {
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
}
.navigators .yzclh-img {
  height: 122.96rpx;
  width: 214.86rpx;
}
.member-news {
  width: 100%;
  margin-bottom: 30rpx;
}
.member-news .header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}
.member-news .header .title {
  font-size: 32rpx;
  font-weight: bold;
}
.member-news .header .iconfont {
  font-size: 52rpx;
  color: #919293;
}
.member-news .list {
  width: 100%;
  display: flex;
  flex-direction: column;
}
.member-news .list .item {
  width: 100%;
  height: 240rpx;
  position: relative;
}
.member-news .list .item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.member-news .list .item .title {
  position: relative;
  font-size: 32rpx;
  font-weight: 500;
  width: 100%;
  top: -70rpx;
  left: 16rpx;
  color: #ffffff;
}
.collection-box {
  position: fixed;
  width: 380rpx;
  padding: 20rpx;
  top: 20rpx;
  right: 20rpx;
  line-height: 40rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
  background: #ffffff;
  border: 1px solid #eeeeee;
  box-shadow: 0 0 6rpx 0 rgba(0, 0, 0, 0.08);
  z-index: 99;
}
.collection-box::after {
  position: absolute;
  content: "";
  display: block;
  right: 140rpx;
  top: -15rpx;
  transform: rotate(45deg);
  width: 30rpx;
  height: 30rpx;
  transform: rotate;
  background: #ffffff;
  border-left: 1px solid #eeeeee;
  border-top: 1px solid #eeeeee;
}
.collection-box .point {
  width: 20rpx;
  height: 20rpx;
  font-size: 60rpx;
  line-height: 0;
  color: #666666;
}
.collection-box .point-big {
  font-size: 80rpx;
}
.collection-box .close-btn {
  position: absolute;
  padding: 0;
  right: 10rpx;
  top: 10rpx;
  width: 40rpx;
  height: 40rpx;
  line-height: 30rpx;
  background: #ffffff;
  color: #999999;
  border-radius: 50%;
}
.follow-gzh {
  position: fixed;
  left: 0;
  right: 0;
  bottom: calc(var(--window-bottom));
  border-radius: 16rpx;
  box-shadow: 0 0 20rpx 0 rgba(0, 0, 0, 0.1);
  background: #ffffff;
  z-index: 10;
}
.follow-gzh .iconfont {
  display: block;
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  z-index: 99;
}