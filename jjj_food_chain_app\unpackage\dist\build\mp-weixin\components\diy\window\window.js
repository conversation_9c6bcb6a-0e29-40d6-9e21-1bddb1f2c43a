"use strict";const t=require("../../../common/vendor.js"),a={data:()=>({}),props:["itemData"],methods:{gotoPages(t){this.gotoPage(t.linkUrl)}}};const e=t._export_sfc(a,[["render",function(a,e,i,o,m,d){return t.e({a:i.itemData.style.layout>-1},i.itemData.style.layout>-1?{b:t.f(i.itemData.data,((a,e,i)=>({a:a.imgUrl,b:e,c:t.o((t=>d.gotoPages(a)),e)}))),c:t.n("column__"+i.itemData.style.layout)}:t.e({d:i.itemData.data[0].imgUrl,e:t.o((t=>d.gotoPages(i.itemData.data[0]))),f:i.itemData.data.length>=2},i.itemData.data.length>=2?{g:i.itemData.data[1].imgUrl,h:t.o((t=>d.gotoPages(i.itemData.data[1])))}:{},{i:i.itemData.data.length>=3},i.itemData.data.length>=3?{j:i.itemData.data[2].imgUrl,k:t.o((t=>d.gotoPages(i.itemData.data[2])))}:{},{l:i.itemData.data.length>=4},i.itemData.data.length>=4?{m:i.itemData.data[3].imgUrl,n:t.o((t=>d.gotoPages(i.itemData.data[3])))}:{},{o:i.itemData.style.paddingTop+"px "+i.itemData.style.paddingLeft+"px"}),{p:i.itemData.style.background,q:2*i.itemData.style.paddingTop+"rpx "+2*i.itemData.style.paddingLeft+"rpx "+2*i.itemData.style.paddingBottom+"rpx "+2*i.itemData.style.paddingLeft+"rpx"})}]]);wx.createComponent(e);
