"use strict";const e=require("../../common/vendor.js"),t=require("../../common/pay.js"),a={components:{cashier:()=>"../../components/cashier/cashier.js"},data:()=>({checkedPay:[10,20],pay_type:20,loadding:!0,indicatorDots:!0,autoplay:!0,interval:2e3,duration:500,isPayPopup:!1,order_id:0,detail:{order_status:[],address:{region:[]},product:[],pay_type:[],delivery_type:[],pay_status:[]},extractStore:{},is_fightgroup:!1,showAlipay:!1,qrimg:""}),onLoad(e){this.order_id=e.order_id},mounted(){e.index.showLoading({title:"加载中"}),this.getData()},methods:{getData(){let t=this,a=t.order_id;t._get("user.order/detail",{order_id:a},(function(a){t.detail=a.data.order,t.extractStore=a.data.order.extractStore,t.loadding=!1,e.index.hideLoading()}))},hidePopupFunc(){this.isPayPopup=!1},cancelOrder(t){let a=this,i=t;e.index.showModal({title:"提示",content:"您确定要取消当前订单吗?",success:function(t){t.confirm&&(e.index.showLoading({title:"正在处理"}),a._get("user.order/cancel",{order_id:i},(function(t){e.index.hideLoading(),e.index.showToast({title:"操作成功",duration:2e3,icon:"success"}),a.getData()})))}})},orderReceipt(t){let a=this;e.index.showModal({title:"提示",content:"您确定要收货吗?",success:function(i){i.confirm&&(e.index.showLoading({title:"正在处理"}),a._post("user.order/receipt",{order_id:t},(function(t){e.index.hideLoading(),e.index.showToast({title:t.msg,duration:2e3,icon:"success"}),a.getData()})))}})},gotoExpress(t){e.index.navigateTo({url:"/pages/order/express/express?order_id="+t})},onApplyRefund(t){e.index.navigateTo({url:"/pages/order/refund/apply/apply?order_product_id="+t})},payTypeFunc(e){this.pay_type=e},subFunc(a){let i=this;if(!i.isPayPopup)return;i.isPayPopup=!1;let d=i.order_id;e.index.showLoading({title:"加载中"}),i._post("user.order/pay",{payType:a,order_id:d,pay_source:i.getPlatform()},(function(a){e.index.hideLoading(),t.pay(a,i)}))},onPayOrder(e){this.isPayPopup=!0,this.order_id=e}}};if(!Array){e.resolveComponent("cashier")()}Math;const i=e._export_sfc(a,[["render",function(t,a,i,d,l,r){return e.e({a:!l.loadding},l.loadding?{}:e.e({b:e.t(l.detail.state_text),c:l.detail.callNo&&10!=l.detail.pay_status.value&&(10==l.detail.order_status.value||30==l.detail.order_status.value)},!l.detail.callNo||10==l.detail.pay_status.value||10!=l.detail.order_status.value&&30!=l.detail.order_status.value?{}:{d:e.t(l.detail.callNo)},{e:10==l.detail.pay_status.value&&20!=l.detail.order_status.value&&l.detail.pay_end_time},10==l.detail.pay_status.value&&20!=l.detail.order_status.value&&l.detail.pay_end_time?{f:e.t(l.detail.pay_end_time)}:{},{g:1!=l.detail.order_type&&10==l.detail.delivery_type.value&&10==l.detail.order_status.value&&l.detail.deliver_status>=1&&10!=l.detail.pay_status.value},1!=l.detail.order_type&&10==l.detail.delivery_type.value&&10==l.detail.order_status.value&&l.detail.deliver_status>=1&&10!=l.detail.pay_status.value?e.e({h:1==l.detail.deliver_status},(l.detail.deliver_status,{}),{i:2==l.detail.deliver_status},(l.detail.deliver_status,{}),{j:3==l.detail.deliver_status},(l.detail.deliver_status,{}),{k:4==l.detail.deliver_status},(l.detail.deliver_status,{}),{l:t.deliver_source},(t.deliver_source,{}),{m:e.o(((...e)=>t.gotoMap&&t.gotoMap(...e)))}):{},{n:l.detail.supplier.logo||"/static/default.png",o:e.t(l.detail.supplier.name),p:l.detail.supplier},l.detail.supplier?{q:e.o((e=>t.openmap(l.detail.supplier.latitude,l.detail.supplier.longitude,l.detail.supplier.name,l.detail.supplier.address))),r:e.o((e=>t.callPhone(l.detail.supplier.link_phone)))}:{},{s:e.f(l.detail.product,((t,a,i)=>({a:t.image.file_path,b:e.t(t.product_name),c:e.t(t.product_attr),d:e.t(t.total_num),e:e.t(t.product_price),f:e.t(t.line_price),g:a}))),t:e.t(l.detail.total_price),v:0!=l.detail.bag_price},0!=l.detail.bag_price?{w:e.t(l.detail.bag_price)}:{},{x:l.detail.express_price>0},l.detail.express_price>0?{y:e.t(l.detail.express_price)}:{},{z:e.t(l.detail.product.length),A:e.t(l.detail.pay_price),B:e.t(l.detail.order_type_text),C:l.detail.mealtime},l.detail.mealtime?{D:e.t(l.detail.mealtime)}:{},{E:1!=l.detail.order_type&&null!=l.detail.address},1!=l.detail.order_type&&null!=l.detail.address?{F:e.t(l.detail.address.detail+l.detail.address.address),G:e.t(l.detail.address.name+" "+l.detail.address.phone)}:{},{H:e.t(l.detail.order_no),I:l.detail.table_no},l.detail.table_no?{J:e.t(l.detail.table_no)}:{},{K:e.t(l.detail.create_time),L:10!=l.detail.pay_status.value},10!=l.detail.pay_status.value?e.e({M:l.detail.online_money>0},l.detail.online_money>0?{N:e.t(l.detail.pay_type.text),O:e.t(l.detail.online_money)}:{},{P:l.detail.balance>0},l.detail.balance>0?{Q:e.t(l.detail.balance)}:{}):{},{R:e.t(l.detail.buyer_remark),S:10==l.detail.pay_status.value&&10==l.detail.order_status},10==l.detail.pay_status.value&&10==l.detail.order_status?{T:e.o((e=>r.onPayOrder(l.detail.order_id)))}:{},{U:e.o(r.hidePopupFunc),V:e.o(r.subFunc),W:e.p({isPayPopup:l.isPayPopup})}),{X:t.theme(),Y:e.n(t.theme()||"")})}]]);wx.createPage(i);
