/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.order-bg {
  position: absolute;
  width: 100%;
  height: 519rpx;
  z-index: 0;
}
[data-theme=theme0] .order-bg {
  background-color: #fee238 !important;
}
[data-theme=theme1] .order-bg {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .order-bg {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .order-bg {
  background-color: #00a348 !important;
}
[data-theme=theme4] .order-bg {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .order-bg {
  background-color: #b99970 !important;
}
[data-theme=theme6] .order-bg {
  background-color: #a4a4d6 !important;
}
.icon.icon-guanbi1 {
  font-size: 30rpx;
  color: #333;
  position: absolute;
  right: 23rpx;
  font-weight: bold;
}
.page-body {
  z-index: 1;
}
.status-info {
  position: fixed;
  left: 23rpx;
  bottom: 20rpx;
  width: 704rpx;
  min-height: 0;
  border-radius: 20rpx;
  padding: 30rpx;
  box-sizing: border-box;
  z-index: 99;
  background-color: #fff;
}
.status-info .icon-jiantou1 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.pop-info.close {
  transform: translate3d(0, 400rpx, 0);
}
.pop-info {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  min-height: 0;
  border-radius: 20rpx 20rpx 0 0;
  padding: 33rpx 25rpx 20rpx 27rpx;
  box-sizing: border-box;
  z-index: 1001;
  background-color: #fff;
  transform: translate3d(0, 0, 0);
  transition: transform 0.2s cubic-bezier(0, 0, 0.25, 1);
}
.pop-info-item {
  height: 66rpx;
  padding-left: 30rpx;
  position: relative;
  color: #999999;
}
[data-theme=theme0] .pop-info-item.active {
  color: #fee238 !important;
}
[data-theme=theme1] .pop-info-item.active {
  color: #b91d32 !important;
}
[data-theme=theme2] .pop-info-item.active {
  color: #09b4f1 !important;
}
[data-theme=theme3] .pop-info-item.active {
  color: #00a348 !important;
}
[data-theme=theme4] .pop-info-item.active {
  color: #fd8103 !important;
}
[data-theme=theme5] .pop-info-item.active {
  color: #b99970 !important;
}
[data-theme=theme6] .pop-info-item.active {
  color: #a4a4d6 !important;
}
[data-theme=theme0] .pop-info-item.active::before {
  background-color: #fee238 !important;
}
[data-theme=theme1] .pop-info-item.active::before {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .pop-info-item.active::before {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .pop-info-item.active::before {
  background-color: #00a348 !important;
}
[data-theme=theme4] .pop-info-item.active::before {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .pop-info-item.active::before {
  background-color: #b99970 !important;
}
[data-theme=theme6] .pop-info-item.active::before {
  background-color: #a4a4d6 !important;
}
.pop-info-item.active::after {
  display: none;
}
.pop-info-item::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  left: 0;
  width: 14rpx;
  height: 14rpx;
  border-radius: 50%;
  background-color: #eeeeee;
}
.pop-info-item::after {
  content: "";
  position: absolute;
  bottom: -50%;
  left: 7rpx;
  width: 2rpx;
  height: 76rpx;
  background-color: #eeeeee;
}
.order-box {
  padding: 26rpx;
  position: relative;
  z-index: 1;
}
.top-state {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  margin-bottom: 30rpx;
}
.top-state .state-height {
  height: 100rpx;
  line-height: 100rpx;
}
.order-content {
  padding: 0 30rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
}
.order-content .shop-name {
  height: 78rpx;
  line-height: 78rpx;
}
.order-content .shop-name .iconfont {
  width: 51rpx;
  height: 51rpx;
  font-size: 26rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-left: 34rpx;
}
[data-theme=theme0] .order-content .shop-name .iconfont {
  background-color: rgba(254, 226, 56, 0.15) !important;
}
[data-theme=theme1] .order-content .shop-name .iconfont {
  background-color: rgba(185, 29, 50, 0.15) !important;
}
[data-theme=theme2] .order-content .shop-name .iconfont {
  background-color: rgba(9, 180, 241, 0.15) !important;
}
[data-theme=theme3] .order-content .shop-name .iconfont {
  background-color: rgba(0, 163, 72, 0.15) !important;
}
[data-theme=theme4] .order-content .shop-name .iconfont {
  background-color: rgba(253, 129, 3, 0.15) !important;
}
[data-theme=theme5] .order-content .shop-name .iconfont {
  background-color: rgba(185, 153, 112, 0.15) !important;
}
[data-theme=theme6] .order-content .shop-name .iconfont {
  background-color: rgba(164, 164, 214, 0.15) !important;
}
[data-theme=theme0] .order-content .shop-name .iconfont {
  color: #fee238 !important;
}
[data-theme=theme1] .order-content .shop-name .iconfont {
  color: #b91d32 !important;
}
[data-theme=theme2] .order-content .shop-name .iconfont {
  color: #09b4f1 !important;
}
[data-theme=theme3] .order-content .shop-name .iconfont {
  color: #00a348 !important;
}
[data-theme=theme4] .order-content .shop-name .iconfont {
  color: #fd8103 !important;
}
[data-theme=theme5] .order-content .shop-name .iconfont {
  color: #b99970 !important;
}
[data-theme=theme6] .order-content .shop-name .iconfont {
  color: #a4a4d6 !important;
}
.order-content .order-prolist .proitem {
  padding: 24rpx 0;
}
.order-content .order-prolist .proitem .pro-image {
  width: 148rpx;
  height: 148rpx;
  border-radius: 20rpx;
  margin-right: 32rpx;
}
.order-content .order-prolist .proitem .pro-image image {
  width: 148rpx;
  height: 148rpx;
  border-radius: 20rpx;
}
.order-content .order-prolist .proitem .pro-price {
  height: 148rpx;
}
.order-content .order-prolist .proitem .pro-price .pro-price-item {
  width: 170rpx;
  box-sizing: border-box;
  text-align: right;
}
.order-content .pro-cont-item {
  height: 92rpx;
  border-bottom: 1px solid #eeeeee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.order-content .pro-cont-item.pro-cont-total {
  justify-content: flex-end;
  border: none;
}
.drinks-img {
  width: 260rpx;
  height: 260rpx;
}
.tips {
  margin: 60rpx 0 80rpx;
  line-height: 48rpx;
}
.drink-btn {
  width: 320rpx;
  border-radius: 50rem !important;
  margin-bottom: 40rpx;
  font-size: 28rpx;
  line-height: 3;
}
.pay-cell {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  color: #5a5b5c;
  margin-bottom: 40rpx;
}
.pay-cell:nth-last-child(1) {
  margin-bottom: 0;
}
.sort-num {
  font-size: 64rpx;
  font-weight: bold;
  color: #5a5b5c;
  line-height: 2;
}
.steps__img-column {
  display: flex;
  margin: 30rpx 0;
}
.steps__img-column .steps__img-column-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.steps__img-column .steps__img-column-item image {
  width: 80rpx;
  height: 80rpx;
}
.steps__text-column {
  display: flex;
  margin-bottom: 40rpx;
}
.steps__text-column .steps__text-column-item {
  flex: 1;
  display: inline-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #919293;
}
.steps__text-column .steps__text-column-item.active {
  color: #5a5b5c;
  font-weight: bold;
}
.steps__text-column .steps__text-column-item.active .steps__column-item-line {
  background-color: #5a5b5c;
}
.steps__text-column .steps__text-column-item .steps__column-item-line {
  flex: 1;
  height: 2rpx;
  background-color: #919293;
  transform: scaleY(0.5);
}
.steps__text-column .steps__text-column-item .steps__text-column-item-text {
  margin: 0 8px;
}
.pay_btn {
  padding: 10rpx 20rpx;
  border-radius: 42rpx;
  width: 710rpx;
  height: 84rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: 800;
  margin-top: 40rpx;
  box-sizing: border-box;
}
.qr_img {
  width: 350rpx;
  height: 350rpx;
  margin: 0 auto;
}
.w100 {
  width: 100%;
}
.other_box {
  margin-top: 22rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 0 34rpx;
  box-sizing: border-box;
  padding-bottom: 30rpx;
}
.other_box .meal_item-title {
  height: 88rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  font-weight: 800;
  color: #282828;
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 30rpx;
}
.other_box .meal_item {
  font-size: 24rpx;
  margin-bottom: 38rpx;
  color: rgba(40, 40, 40, 0.6);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.other_box .meal_item .right {
  width: 360rpx;
  text-align: right;
  color: #282828;
}
.pop-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
.pop-bg .pop-content {
  position: fixed;
  z-index: 1001;
  bottom: 0;
  width: 100%;
  height: 719rpx;
  padding: 40rpx 24rpx 0 23rpx;
  box-sizing: border-box;
  transform: translate3d(0, 0, 0);
  transition: transform 0.2s cubic-bezier(0, 0, 0.25, 1);
  background-color: #ffffff;
  border-radius: 15rpx 15rpx 0rpx 0rpx;
}
.pop-bg .pop-content .icon.icon-guanbi {
  background: #dedede;
  border-radius: 50%;
  color: #ffffff;
  position: absolute;
  right: 26rpx;
  top: 40rpx;
  font-size: 22rpx;
  display: flex;
  width: 40rpx;
  height: 40rpx;
  justify-content: center;
  align-items: center;
}
.close-img {
  width: 32rpx;
  height: 32rpx;
  position: absolute;
  right: 32rpx;
  top: 32rpx;
}
.pop-bg.close {
  height: 0;
}
.pop-bg.close .pop-content {
  transform: translate3d(0, 2000rpx, 0);
}
.cashier-item {
  height: 89rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eeeeee;
}
.cashier-item .icon-box {
  width: 38rpx;
  height: 38rpx;
  border: 1px solid #dddddd;
  border-radius: 50%;
}
.cashier-item .icon-box .icon-tijiaochenggong {
  font-size: 26rpx;
  color: #ffffff;
}
.cashier-item .icon-box.border {
  border-radius: 50%;
}
.cashier-item.active .icon-box {
  border: 1px solid #72deed;
}
[data-theme=theme0] .cashier-item.active .icon-box {
  border-color: #fee238 !important;
}
[data-theme=theme1] .cashier-item.active .icon-box {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .cashier-item.active .icon-box {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .cashier-item.active .icon-box {
  border-color: #00a348 !important;
}
[data-theme=theme4] .cashier-item.active .icon-box {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .cashier-item.active .icon-box {
  border-color: #b99970 !important;
}
[data-theme=theme6] .cashier-item.active .icon-box {
  border-color: #a4a4d6 !important;
}
[data-theme=theme0] .cashier-item.active .icon-box {
  background-color: #fee238 !important;
}
[data-theme=theme1] .cashier-item.active .icon-box {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .cashier-item.active .icon-box {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .cashier-item.active .icon-box {
  background-color: #00a348 !important;
}
[data-theme=theme4] .cashier-item.active .icon-box {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .cashier-item.active .icon-box {
  background-color: #b99970 !important;
}
[data-theme=theme6] .cashier-item.active .icon-box {
  background-color: #a4a4d6 !important;
}
.pay-btn {
  width: 750rpx;
  height: 96rpx;
  font-size: 32rpx;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  bottom: 0;
}
[data-theme=theme0] .pay-btn {
  background-color: #fee238 !important;
}
[data-theme=theme1] .pay-btn {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .pay-btn {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .pay-btn {
  background-color: #00a348 !important;
}
[data-theme=theme4] .pay-btn {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .pay-btn {
  background-color: #b99970 !important;
}
[data-theme=theme6] .pay-btn {
  background-color: #a4a4d6 !important;
}
.foot-pay-btns button {
  margin: 0;
  font-size: 32rpx;
  padding: 0 50rpx;
  height: 84rpx;
  line-height: 84rpx;
  border-radius: 50rpx;
}
[data-theme=theme0] .foot-pay-btns button {
  background-color: #fee238 !important;
}
[data-theme=theme1] .foot-pay-btns button {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .foot-pay-btns button {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .foot-pay-btns button {
  background-color: #00a348 !important;
}
[data-theme=theme4] .foot-pay-btns button {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .foot-pay-btns button {
  background-color: #b99970 !important;
}
[data-theme=theme6] .foot-pay-btns button {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .foot-pay-btns button {
  color: #ffffff !important;
}
[data-theme=theme1] .foot-pay-btns button {
  color: #ffffff !important;
}
[data-theme=theme2] .foot-pay-btns button {
  color: #ffffff !important;
}
[data-theme=theme3] .foot-pay-btns button {
  color: #ffffff !important;
}
[data-theme=theme4] .foot-pay-btns button {
  color: #ffffff !important;
}
[data-theme=theme5] .foot-pay-btns button {
  color: #ffffff !important;
}
[data-theme=theme6] .foot-pay-btns button {
  color: #ffffff !important;
}
.callNo-tips {
  font-size: 24rpx;
  height: 44rpx;
  width: 111rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  border-radius: 25rpx 22rpx 22rpx 1rpx;
}
[data-theme=theme0] .order-box-text {
  color: #111111 !important;
}
[data-theme=theme1] .order-box-text {
  color: #ffffff !important;
}
[data-theme=theme2] .order-box-text {
  color: #ffffff !important;
}
[data-theme=theme3] .order-box-text {
  color: #ffffff !important;
}
[data-theme=theme4] .order-box-text {
  color: #ffffff !important;
}
[data-theme=theme5] .order-box-text {
  color: #fff !important;
}
[data-theme=theme6] .order-box-text {
  color: #ffffff !important;
}
.sup_img {
  width: 44rpx;
  height: 44rpx;
  background: rgba(0, 0, 0, 0);
  opacity: 1;
  border-radius: 50%;
  margin-right: 10rpx;
  display: block;
}