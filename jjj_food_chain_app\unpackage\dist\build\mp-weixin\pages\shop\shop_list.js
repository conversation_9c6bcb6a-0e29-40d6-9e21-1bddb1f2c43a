"use strict";const e=require("../../common/vendor.js"),t={components:{uniLoadMore:()=>"../../components/uni-load-more.js"},data:()=>({distance:"",loading:!0,storeList:[],longitude:"",latitude:"",selectedId:{region:{city:"",store_id:-1,distance_unit:"",log:{file_path:""}},store_name:""},store_name:"",list_rows:10,last_page:0,page:1,no_more:!1,scrollviewHigh:0,longitude:"",latitude:"",selectedId:""}),computed:{loadingType(){return this.loading?1:0!=this.storeList.length&&this.no_more?2:0}},onLoad(e){},onShow(){this.restoreData(),this.getcityData()},mounted(){this.selectedId=e.index.getStorageSync("selectedId"),this.init()},methods:{init(){let t=this;e.index.getSystemInfo({success(e){t.scrollviewHigh=e.windowHeight}})},restoreData(){this.storeList=[],this.search="",this.no_more=!1,this.page=1},scrolltolowerFunc(){let e=this;if(e.bottomRefresh=!0,e.page++,e.loading=!0,e.page>e.last_page)return e.loading=!1,void(e.no_more=!0);e.getData()},getcityData(){this.getLocation()},onAuthorize(){let t=this;e.index.openSetting({success(e){e.authSetting["scope.userLocation"]&&(t.isAuthor=!0,setTimeout((()=>{t.getLocation((e=>{}))}),1e3))}})},getLocation(t){let o=this;e.index.getLocation({type:"wgs84",success(e){o.longitude=e.longitude,o.latitude=e.latitude,o.getData()},fail(t){o.getData(),e.index.showToast({title:"获取定位失败，请点击右下角按钮打开定位权限",duration:2e3,icon:"none"})}})},getWxLocation(e,t){let o=this;var i=require("jweixin-module");i.config(JSON.parse(e)),i.ready((function(e){i.getLocation({type:"wgs84",success:function(e){o.longitude=e.longitude,o.latitude=e.latitude,o.getData()},fail(e){o.getData()}})})),i.error((function(e){console.log(e)}))},getData(){let t=this;t.loading=!0,e.index.showLoading({title:"加载中"}),t._get("supplier.index/list",{longitude:t.longitude||0,latitude:t.latitude||0},(function(o){e.index.hideLoading(),t.loading=!1,t.storeList=t.storeList.concat(o.data.list.data),t.last_page=o.data.list.last_page,o.data.list.last_page<=1&&(t.no_more=!0)}))},chooseLocation(){let t=this;e.index.chooseLocation({success:function(e){console.log("位置名称："+e.name),console.log("详细地址："+e.address),console.log("纬度："+e.latitude),console.log("经度："+e.longitude),t.longitude=e.longitude,t.latitude=e.latitude,t.getrecord(),t.getData()}})},search(){let e=this;e.loading=!0,e._get("store.store/lists",{store_name:e.store_name,longitude:e.longitude,latitude:e.latitude},(function(t){e.loading=!1,e.storeList=t.data.list}))},onSelectedStore(t,o){this.selectedId=t,e.index.setStorageSync("selectedId",this.selectedId),o&&e.index.navigateBack()},getrecord(){let e=this,t=e.selectedId.store_id;e._post("user.storelog/record",{store_id:t,longitude:e.longitude,latitude:e.latitude},(function(t){e.distance=t.data.detail.distance_unit}))},addSotre(e){let t=e.store_id;this._post("user.storelog/add",{store_id:t},(function(e){}))},isShopid:t=>e.index.getStorageSync("selectedId")==t}};if(!Array){e.resolveComponent("uni-load-more")()}const o=e._export_sfc(t,[["render",function(t,o,i,s,n,d){return e.e({a:e.f(n.storeList,((o,i,s)=>e.e({a:e.n(o.shop_supplier_id!=n.selectedId?"select-id":"select-id icon iconfont icon-tijiaochenggong"),b:e.t(o.name),c:e.t(o.distance),d:e.t(o.address),e:o.delivery_time.length>0},o.delivery_time.length>0?{f:e.t(o.delivery_time[0]+"-"+o.delivery_time[1])}:{},{g:0==o.status},(o.status,{}),{h:e.o((e=>t.callPhone(o.link_phone)),i),i:e.o((e=>t.openmap(o.latitude,o.longitude)),i),j:e.o((e=>d.onSelectedStore(o.shop_supplier_id,!0)),i),k:e.o((e=>d.onSelectedStore(o.shop_supplier_id)),i),l:e.n(d.isShopid(o.shop_supplier_id)?"active":""),m:i}))),b:0==n.storeList.length&&!n.loading},0!=n.storeList.length||n.loading?{c:e.p({loadingType:d.loadingType})}:{},{d:e.s("height:"+n.scrollviewHigh+"px;"),e:e.o(((...e)=>d.scrolltolowerFunc&&d.scrolltolowerFunc(...e))),f:t.theme(),g:e.n(t.theme()||"")})}]]);wx.createPage(o);
