"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {};
  },
  props: ["itemData"],
  methods: {}
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.itemData.style.height + "px",
    b: $props.itemData.style.background,
    c: $props.itemData.style.topRadio + "px",
    d: $props.itemData.style.topRadio + "px",
    e: $props.itemData.style.bottomRadio + "px",
    f: $props.itemData.style.bottomRadio + "px",
    g: $props.itemData.style.bgcolor,
    h: $props.itemData.style.paddingLeft + "px",
    i: $props.itemData.style.paddingLeft + "px",
    j: $props.itemData.style.paddingTop + "px",
    k: $props.itemData.style.paddingBottom + "px"
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/components/diy/blank/blank.js.map
