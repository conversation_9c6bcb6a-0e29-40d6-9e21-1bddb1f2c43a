"use strict";const t=require("../../../common/vendor.js"),a={data:()=>({indicatorDots:!0,autoplay:!0,interval:2e3,duration:500,indicatorActiveColor:"#ffffff",current:0,userInfo:{},isLogin:!1,loading:!0}),props:["itemData"],created(){this.indicatorActiveColor=this.itemData.style.btnColor},methods:{changeSwiper(t){this.current=t.detail.current},gotoPages(t){this.gotoPage(t.linkUrl)}}};const e=t._export_sfc(a,[["render",function(a,e,o,i,r,d){return{a:t.f(o.itemData.data,((e,i,d)=>({a:e.imgUrl,b:t.f(o.itemData.data,((a,e,o)=>({a:t.n(r.current==e?"swiper-dot active":"swiper-dot"),b:e}))),c:i,d:t.o((t=>a.gotoPage(e.linkUrl)),i)}))),b:2*o.itemData.style.topRadio+"rpx "+2*o.itemData.style.topRadio+"rpx "+2*o.itemData.style.bottomRadio+"rpx "+2*o.itemData.style.bottomRadio+"rpx",c:t.s("background-color:"+r.indicatorActiveColor),d:t.n(o.itemData.style.imgShape),e:2*o.itemData.style.topRadio+"rpx "+2*o.itemData.style.topRadio+"rpx "+2*o.itemData.style.bottomRadio+"rpx "+2*o.itemData.style.bottomRadio+"rpx",f:r.autoplay,g:r.duration,h:t.o(((...t)=>d.changeSwiper&&d.changeSwiper(...t))),i:o.itemData.style.background,j:2*o.itemData.style.paddingLeft+"rpx",k:2*o.itemData.style.paddingLeft+"rpx",l:2*o.itemData.style.paddingTop+"rpx",m:2*o.itemData.style.paddingBottom+"rpx",n:o.itemData.style.height+"rpx"}}],["__scopeId","data-v-5b112dfc"]]);wx.createComponent(e);
