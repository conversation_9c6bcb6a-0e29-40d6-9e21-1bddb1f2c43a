/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.p-30-75.data-v-facbca1a {
  padding: 30rpx 75rpx;
}
.p-0-75.data-v-facbca1a {
  padding: 0 75rpx;
}
.t-r.data-v-facbca1a {
  text-align: right;
}
.login-container.data-v-facbca1a {
  background: #ffffff;
}
.login-container input.data-v-facbca1a {
  height: 88rpx;
  line-height: 88rpx;
}
.wechatapp.data-v-facbca1a {
  padding: 80rpx 0 48rpx;
  border-bottom: 1rpx solid #e3e3e3;
  margin-bottom: 72rpx;
  text-align: center;
}
.wechatapp .header.data-v-facbca1a {
  width: 190rpx;
  height: 190rpx;
  border: 2px solid #fff;
  margin: 0rpx auto 0;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 1px 0px 5px rgba(50, 50, 50, 0.3);
}
.auth-title.data-v-facbca1a {
  color: #585858;
  font-size: 34rpx;
  margin-bottom: 40rpx;
}
.auth-subtitle.data-v-facbca1a {
  color: #888;
  margin-bottom: 88rpx;
  font-size: 28rpx;
}
.login-btn.data-v-facbca1a {
  padding: 0 20rpx;
}
.login-btn button.data-v-facbca1a {
  height: 88rpx;
  line-height: 88rpx;
  background: #04be01;
  color: #fff;
  font-size: 30rpx;
  border-radius: 999rpx;
  text-align: center;
}
.no-login-btn.data-v-facbca1a {
  margin-top: 20rpx;
  padding: 0 20rpx;
}
.no-login-btn button.data-v-facbca1a {
  height: 88rpx;
  line-height: 88rpx;
  background: #dfdfdf;
  color: #fff;
  font-size: 30rpx;
  border-radius: 999rpx;
  text-align: center;
}
.get-code-btn.data-v-facbca1a {
  width: 200rpx;
  height: 80rpx;
  line-height: 76rpx;
  padding: 0rpx 30rpx;
  border-radius: 40rpx;
  white-space: nowrap;
  background-color: #FFFFFF;
  color: #ffcc00;
  font-size: 30rpx;
}
.get-code-btn[disabled=true].data-v-facbca1a {
  background-color: #FFFFFF;
}
.btns button.data-v-facbca1a {
  height: 90rpx;
  line-height: 90rpx;
  font-size: 34rpx;
  border-radius: 45rpx;
  background: #ffcc00;
  color: #ffffff;
}
.login_topbpx.data-v-facbca1a {
  padding: 181rpx 0;
  padding-bottom: 110rpx;
}
.login_tit.data-v-facbca1a {
  font-size: 52rpx;
  font-weight: 600;
  margin-bottom: 33rpx;
}
.login_top.data-v-facbca1a {
  font-size: 24rpx;
  color: #adafb3;
}
.input_botom.data-v-facbca1a {
  border-bottom: 1px solid #f4f4f4;
}
.bottom_nav.data-v-facbca1a {
  width: 100%;
  position: absolute;
  bottom: 100rpx;
}
.bottom-box.data-v-facbca1a {
  width: 70%;
  margin: 0 auto;
}
.other_tit.data-v-facbca1a {
  height: 1rpx;
  background-color: #CACACA;
  width: 100%;
  line-height: 1rpx;
  text-align: center;
}
.weixin_box.data-v-facbca1a {
  background-color: #04BE01;
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
}
.weixin_box .icon-weixin.data-v-facbca1a {
  font-size: 40rpx;
  color: #FFFFFF;
}