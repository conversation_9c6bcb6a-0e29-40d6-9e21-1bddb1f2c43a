"use strict";const e=require("../../../../common/vendor.js"),o=require("../../../../common/utils.js"),t={components:{mpvueCityPicker:()=>"../../../../components/mpvue-citypicker/mpvueCityPicker.js"},data:()=>({urls:"",cityPickerValueDefault:[0,0,0],selectCity:"选择省,市,区",province_id:0,city_id:0,region_id:0,address:{latitude:"",longitude:"",detail:""},delta:1,signPackage:"",openWb:!1,wburl:""}),onLoad:function(o){this.delta=o.delta||1,"locationPicker"==o.module&&(this.address=e.index.getStorageSync("addressData"),e.index.removeStorageSync("addressData"),this.address.detail=o.addr,this.address.latitude=o.latng.split(",")[0],this.address.longitude=o.latng.split(",")[1])},methods:{handlePostMessage(e){},chooseLocation(o){let t=this;e.index.chooseLocation({success:function(e){t.address.longitude=e.longitude,t.address.latitude=e.latitude,t.address.detail=e.address,console.log("位置名称："+e.name),console.log("详细地址："+e.address),console.log("纬度："+e.latitude),console.log("经度："+e.longitude)},fail(o){e.index.setStorageSync("address",""),console.log(o)},complete(e){console.log(e)}})},formSubmit:function(t){let s=this;var i=t.detail.value;return i.latitude=s.address.latitude,i.longitude=s.address.longitude,""==i.name?(e.index.showToast({title:"请输入收货人姓名",duration:1e3,icon:"none"}),!1):o.utils.isTelAvailable(i.phone)?0!=i.latitude&&0!=i.longitude||""!=i.detail?void s._post("user.address/add",i,(function(o){console.log(s.delta),s.showSuccess(o.msg,(function(){e.index.navigateBack({delta:1})}))})):(e.index.showToast({title:"请选择正确的地址",duration:1e3,icon:"none"}),!1):void e.index.showToast({title:"请输入正确的联系方式",duration:2e3,icon:"none"})},formReset:function(e){console.log("清空数据")},showMulLinkageThreePicker(){this.$refs.mpvueCityPicker.show()},onConfirm(e){this.selectCity=e.label,this.province_id=e.cityCode[0],this.city_id=e.cityCode[1],this.region_id=e.cityCode[2]}}};if(!Array){e.resolveComponent("mpvue-city-picker")()}const s=e._export_sfc(t,[["render",function(o,t,s,i,d,a){return e.e({a:d.address.name,b:e.o((e=>d.address.name=e.detail.value)),c:d.address.phone,d:e.o((e=>d.address.phone=e.detail.value)),e:d.address.detail,f:e.o((e=>d.address.detail=e.detail.value)),g:e.o(((...e)=>a.chooseLocation&&a.chooseLocation(...e))),h:d.address.address,i:e.o((e=>d.address.address=e.detail.value)),j:e.o(((...e)=>a.formSubmit&&a.formSubmit(...e))),k:e.o(((...e)=>a.formReset&&a.formReset(...e))),l:e.sr("mpvueCityPicker","bb945fce-0"),m:e.o(a.onConfirm),n:e.p({pickerValueDefault:d.cityPickerValueDefault}),o:d.openWb},d.openWb?{p:d.wburl,q:e.o(((...e)=>a.handlePostMessage&&a.handlePostMessage(...e)))}:{},{r:o.theme(),s:e.n(o.theme()||"")})}]]);wx.createPage(s);
