"use strict";const e=require("../../../common/vendor.js"),r={data:()=>({form:{},arr:[]}),mounted(){this.getData()},methods:{getData(){let r=this;e.index.getStorage({key:"search_list",success:function(e){null!=e&&null!=e.data&&(r.arr=e.data)}})},search(r){let t=null;if(null!=r)t=r;else{t=this.form.keyWord;let r=this.arr;if(void 0===t||null==t||""==t)return e.index.showToast({title:"请输入搜索的关键字",icon:"none",duration:2e3}),!1;r.push(t),e.index.setStorage({key:"search_list",data:r,success:function(){console.log("success")}})}e.index.navigateTo({url:"/pages/product/list/list?search="+t+"&category_id=0&sortType=all"})},clearStorage(){let r=this;e.index.removeStorage({key:"search_list",success:function(e){r.arr=[]}})}}};const t=e._export_sfc(r,[["render",function(r,t,a,s,o,c){return{a:e.o((e=>c.search())),b:o.form.keyWord,c:e.o((e=>o.form.keyWord=e.detail.value)),d:e.o(((...e)=>c.clearStorage&&c.clearStorage(...e))),e:e.f(o.arr,((r,t,a)=>({a:e.t(o.arr[t]),b:t,c:e.o((e=>c.search(o.arr[t])),t)})))}}]]);wx.createPage(t);
