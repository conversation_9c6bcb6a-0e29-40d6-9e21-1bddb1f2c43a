"use strict";const e=require("./vendor.js");function a(a,r,t){t?t(a):function(a){e.index.reLaunch({url:"/pages/order/pay-success/pay-success?order_id="+a.data.order_id})}(a)}function r(a,r){r?r(a):50!=a.data.order_type&&e.index.redirectTo({url:"/pages/order/order-detail?order_id="+a.data.order_id})}exports.pay=(t,d,n,o)=>{if(-10===t.code)return d.showError(t.msg),!1;20==t.data.pay_type&&e.index.requestPayment({provider:"wxpay",timeStamp:t.data.payment.timeStamp,nonceStr:t.data.payment.nonceStr,package:t.data.payment.package,signType:t.data.payment.signType,paySign:t.data.payment.paySign,success:e=>{a(t,d,n)},fail:e=>{d.showError("订单未支付成功",(()=>{r(t,o)}))}}),10==t.data.pay_type&&a(t,d,n),30==t.data.pay_type&&function(t,d,n,o){console.log(t.data.payment),e.index.requestPayment({provider:"alipay",orderInfo:t.data.payment,success(e){a(t,d,n)},fail(e){console.log(e),d.showError("订单未支付成功",(()=>{r(t,o)}))}})}(t,d,n,o)};
