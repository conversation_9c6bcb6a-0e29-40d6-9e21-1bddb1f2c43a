"use strict";const e=require("../../../common/vendor.js");require("../../../env/config.js");const o={components:{Popup:()=>"../../../components/uni-popup.js",Upload:()=>"../../../components/upload/upload.js"},data:()=>({userInfo:{},isBirthday:!1,birthday:"",imageList:[],newName:"",type:"",isUpload:!1,mobileModel:{mobile:"",code:""},passwordModel:{mobile:"",code:"",password:"",repassword:""},isPhone:!1,isPassword:!1,sms_open:!1}),onShow(){this.getData()},methods:{clearStorage(){e.index.clearStorageSync()},maskPhone:e=>e&&11===e.length?e.replace(/(\d{3})\d{4}(\d{4})/,"$1***$2"):e,onChooseAvatar(e){this.uploadFile([e.detail.avatarUrl])},getData(){let o=this;e.index.showLoading({title:"加载中",mask:!0}),o._get("user.index/setting",{},(function(t){o.userInfo=t.data.userInfo,e.index.hideLoading()}))},changeAvatarUrl(){this.isUpload=!0},uploadFile:function(o){let t=this;t.imageList=[];let a=0,n=o.length,i={token:e.index.getStorageSync("token"),app_id:t.getAppId()};e.index.showLoading({title:"图片上传中",mask:!0}),o.forEach((function(o,s){e.index.uploadFile({url:t.websiteUrl+"/index.php?s=/api/file.upload/image",filePath:o,name:"iFile",formData:i,success:function(e){let o="object"==typeof e.data?e.data:JSON.parse(e.data);1===o.code?t.imageList.push(o.data):t.showError(o.msg)},complete:function(){a++,n===a&&(e.index.hideLoading(),t.getImgsFunc(t.imageList))}})}))},getImgsFunc(e){if(e&&void 0!==e){let o=this;o.userInfo.avatarUrl=e[0].file_path,o.update(),o.isUpload=!1}},logout(){let o=this;o._post("/user.User/logOut",{},(t=>{e.index.removeStorageSync("token"),e.index.removeStorageSync("user_id"),o.gotoPage("/pages/index/index")}))},update(){let o=this;if(o.loading)return;e.index.showLoading({title:"加载中",mask:!0});let t=o.userInfo;o.loading=!0,o._post("user.user/updateInfo",t,(function(t){o.showSuccess("修改成功",(function(){o.loading=!1,e.index.hideLoading(),o.getData()}),(function(t){e.index.hideLoading(),o.loading=!1}))}))}}};if(!Array){e.resolveComponent("Upload")()}const t=e._export_sfc(o,[["render",function(o,t,a,n,i,s){return e.e({a:i.userInfo.avatarUrl||"/static/default.png",b:e.o(((...e)=>s.onChooseAvatar&&s.onChooseAvatar(...e))),c:e.t(i.userInfo.user_id),d:i.userInfo.nickName,e:e.o((e=>i.userInfo.nickName=e.detail.value)),f:e.t(s.maskPhone(i.userInfo.mobile)),g:e.o(((...e)=>s.update&&s.update(...e))),h:e.o((e=>s.logout())),i:i.isUpload},i.isUpload?{j:e.o(s.getImgsFunc),k:e.p({num:1})}:{},{l:o.theme(),m:e.n(o.theme()||"")})}]]);wx.createPage(t);
