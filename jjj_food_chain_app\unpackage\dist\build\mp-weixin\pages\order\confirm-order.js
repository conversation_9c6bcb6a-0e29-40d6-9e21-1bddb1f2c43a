"use strict";const e=require("../../common/vendor.js"),t={components:{timepicker:()=>"../../components/timepicker/timepicker.js"},data:()=>({loading:!0,options:{},tab_type:0,product_id:"",product_num:"",ProductData:[],OrderData:[],exist_address:!1,Address:{region:[]},extract_store:{},last_extract:{},product_sku_id:0,delivery:0,linkman:"",phone:"",remark:"",deliverySetting:[],temlIds:[],showAlipay:!1,takeout_address:{},isTimer:!1,mealtime:"",wmtime:"",estitime:"",is_pack:1,supplier:{},dinner_type:10,cart_type:0,store_set:[],delivery_set:[],table_id:0,min_money:0}),onLoad(e){let t=this;t.options=e,t.cart_type=e.cart_type,t.table_id=e.table_id||0,t.dinner_type=e.dinner_type,t.delivery=e.delivery},onShow(){this.$fire.on("takeout",(function(e){self.takeout_address=e,self.orderType="takeout"})),this.getData()},methods:{hasType(e){return-1!=this.deliverySetting.indexOf(e)},changeTime(e){},getTime(e){let t=new Date,r=t.getHours();r<10&&(r="0"+r);let i=t.getMinutes();i<10&&(i="0"+i);let a=t.getHours(),d=t.getMinutes()+15;return d>=60&&(d-=60,a+=1),d<10&&(d="0"+d),a<10&&(a="0"+a),"my"==e?r+":"+i:"wm"==e?a+":"+d:void 0},getData(){let t=this;e.index.showLoading({title:"加载中"}),t.loading=!0;let r=function(e){t.OrderData=e.data.orderInfo,t.min_money=e.data.orderInfo.supplier.min_money,t.temlIds=e.data.template_arr,t.exist_address=t.OrderData.exist_address,t.Address=t.OrderData.address,t.extract_store=t.OrderData.extract_store,t.last_extract=t.OrderData.last_extract,t.ProductData=t.OrderData.product_list,t.supplier=e.data.orderInfo.supplier,t.linkman=e.data.orderInfo.last_extract.linkman,t.phone=e.data.orderInfo.last_extract.phone,t.delivery_set=e.data.orderInfo.supplier.delivery_set,t.store_set=e.data.orderInfo.supplier.store_set,"10"==t.OrderData.delivery&&(t.tab_type=0),"20"==t.OrderData.delivery&&(t.tab_type=1),"30"==t.OrderData.delivery&&(t.tab_type=3),"40"==t.OrderData.delivery&&(t.tab_type=4),0==t.cart_type?-1==t.delivery_set.indexOf(t.delivery)&&("10"==t.delivery_set[0]?(console.log("执行"),t.tabFunc(0,!0)):t.tabFunc(1,!0)):-1==t.store_set.indexOf(t.delivery)&&("30"==t.store_set[0]?t.tabFunc(3,!0):t.tabFunc(4,!0)),t.wmtime=t.getTime("wm"),t.mealtime=t.getTime("my"),t.estitime=t.getTime("wm"),t.deliverySetting=t.OrderData.deliverySetting,t.loading=!1},i={delivery:t.delivery||0,store_id:1,mealtime:"",pay_source:t.getPlatform()};t.table_id&&(i.table_id=t.table_id),"buy"===t.options.order_type?t._get("order.order/buy",Object.assign({},i,{product_id:t.options.product_id,product_num:t.options.product_num,product_sku_id:t.options.product_sku_id}),(function(e){r(e)})):"cart"===t.options.order_type&&t._get("order.order/cart",Object.assign({},i,{cart_ids:t.options.cart_ids||0,shop_supplier_id:t.options.shop_supplier_id||0,order_type:t.options.cart_type,table_id:t.table_id}),(function(e){r(e)}),(function(e){1==t.tab_type?t.tabFunc(0):0==t.tab_type&&t.tabFunc(1)}))},tabFunc(e,t){0==e&&1*this.min_money>1*this.OrderData.order_pay_price?this.showError("未满足最低配送费用!"):(this.tab_type=e,0==e?(this.delivery="10",this.dinner_type=10):1==e?(this.delivery="20",this.dinner_type=20):3==e?(this.delivery="30",this.dinner_type=30):4==e&&(this.delivery="40",this.dinner_type=30),t||(console.log("切换"),this.getData()))},SubmitOrder(){let t=this;if(this.loading)return;let r={delivery:t.delivery,store_id:1,linkman:t.linkman,phone:t.phone,remark:t.remark,mealtime:t.mealtime,shop_supplier_id:t.options.shop_supplier_id,pay_source:t.getPlatform()};10==t.delivery&&(r.mealtime=t.wmtime),1==t.tab_type&&10!=t.delivery&&(r.mealtime=t.getTime("my"));let i="";"buy"===t.options.order_type&&(i="order.order/buy",r=Object.assign(r,{product_id:t.options.product_id,product_num:t.options.product_num,product_sku_id:t.options.product_sku_id})),"cart"===t.options.order_type&&(i="order.order/cart",r=Object.assign(r,{cart_ids:t.options.cart_ids||0,dinner_type:t.dinner_type,shop_supplier_id:t.options.shop_supplier_id||0,order_type:t.options.cart_type,table_id:t.table_id}));t.subMessage(t.temlIds,(function(){t.loading=!0,e.index.showLoading({title:"加载中",mask:!0}),t._post(i,r,(function(e){let r="/pages/order/cashier?order_type=10&order_id="+e.data.order_id;t.gotoPage(r,"reLaunch")}),(e=>{t.loading=!1}))}))},timepick(){this.isTimer=!0},closetimer(e){""!=e&&(this.wmtime=e,this.mealtime=e),this.isTimer=!1},packTypeFunc(e){this.is_pack=e}}};if(!Array){e.resolveComponent("timepicker")()}Math;const r=e._export_sfc(t,[["render",function(t,r,i,a,d,s){return e.e({a:!d.loading},d.loading?{}:e.e({b:0==d.cart_type},0==d.cart_type?{c:e.f(d.delivery_set,((t,r,i)=>e.e({a:"10"==t},"10"==t?{b:e.n(0==d.tab_type?"active":""),c:e.o((e=>s.tabFunc(0)),r+"1")}:{},{d:r+"1"}))),d:e.f(d.delivery_set,((t,r,i)=>e.e({a:"20"==t},"20"==t?{b:e.n(1==d.tab_type?"active":""),c:e.o((e=>s.tabFunc(1)),r+"2")}:{},{d:r+"2"})))}:{},{e:1==d.cart_type},1==d.cart_type?{f:e.f(d.store_set,((t,r,i)=>e.e({a:"30"==t},"30"==t?{b:e.n(3==d.tab_type?"active":""),c:e.o((e=>s.tabFunc(3)),t)}:{},{d:t}))),g:e.f(d.store_set,((t,r,i)=>e.e({a:"40"==t},"40"==t?{b:e.n(4==d.tab_type?"active":""),c:e.o((e=>s.tabFunc(4)),t)}:{},{d:t})))}:{},{h:0==d.cart_type},0==d.cart_type?e.e({i:1!=d.tab_type},1!=d.tab_type?e.e({j:null!=d.Address},null!=d.Address?{k:e.t(d.Address.detail+d.Address.address),l:e.t(d.Address.name),m:e.t(d.Address.phone)}:{},{n:e.o((e=>t.gotoPage("/pages/user/address/storeaddress?shop_supplier_id="+d.options.shop_supplier_id)))}):{},{o:1==d.tab_type},1==d.tab_type?{p:e.t(d.supplier.name),q:e.t(d.supplier.address)}:{},{r:1==d.tab_type&&10!=d.delivery},1==d.tab_type&&10!=d.delivery?{s:d.phone,t:e.o((e=>d.phone=e.detail.value))}:{},{v:1==d.tab_type&&10!=d.delivery},1==d.tab_type&&10!=d.delivery?{w:e.t(d.estitime)}:{},{x:0==d.tab_type&&10==d.delivery},0==d.tab_type&&10==d.delivery?{y:e.t(d.wmtime),z:e.o((e=>s.timepick()))}:{}):{},{A:e.t(d.supplier.name),B:e.f(d.ProductData,((t,r,i)=>({a:t.image.file_path,b:e.t(t.product.product_name),c:e.t(t.describe),d:e.t(t.product_num),e:e.t(t.price),f:e.t(t.product_price),g:r}))),C:e.t(d.OrderData.order_total_price),D:e.t(d.OrderData.order_bag_price),E:1!=d.tab_type&&0!=d.OrderData.express_price},1!=d.tab_type&&0!=d.OrderData.express_price?e.e({F:d.OrderData.total_express_price!=d.OrderData.express_price},d.OrderData.total_express_price!=d.OrderData.express_price?{G:e.t(d.OrderData.total_express_price)}:{},{H:e.t(d.OrderData.express_price)}):{},{I:d.OrderData.table_no},d.OrderData.table_no?{J:e.t(d.OrderData.table_no)}:{},{K:e.t(d.OrderData.order_total_num),L:e.t(d.OrderData.order_pay_price),M:d.remark,N:e.o((e=>d.remark=e.detail.value)),O:!d.OrderData.force_points},d.OrderData.force_points?{}:{P:e.t(d.OrderData.order_pay_price)},{Q:e.o(((...e)=>s.SubmitOrder&&s.SubmitOrder(...e))),R:e.o(s.closetimer),S:e.p({isTimer:d.isTimer})}),{T:t.theme(),U:e.n(t.theme()||"")})}]]);wx.createPage(r);
