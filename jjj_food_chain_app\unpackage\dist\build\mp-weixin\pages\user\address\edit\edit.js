"use strict";const e=require("../../../../common/vendor.js"),s=require("../../../../common/utils.js"),d={data:()=>({urls:"",longitude:0,latitude:0,detail:0,address_id:0,address:{name:"",phone:"",detail:"",address:""},openWb:!1,wburl:""}),onLoad(s){this.address_id=s.address_id,this.delta=s.delta||1,"locationPicker"==s.module?(this.address=e.index.getStorageSync("addressData"),e.index.removeStorageSync("addressData"),this.address.detail=s.addr,this.address.latitude=s.latng.split(",")[0],this.address.longitude=s.latng.split(",")[1]):this.getData()},mounted(){},methods:{handlePostMessage(e){console.log("接收到消息："+JSON.stringify(e.detail))},getData(){let e=this,s=e.address_id;e._get("user.address/detail",{address_id:s},(function(s){e.address=s.data.detail,e.address_id=s.data.detail.address_id}))},chooseLocation(s){let d=this;e.index.chooseLocation({success:function(s){d.address.longitude=s.longitude,d.address.latitude=s.latitude,d.address.detail=s.address,e.index.setStorageSync("mpaddress",d.address),console.log("位置名称："+s.name),console.log("详细地址："+s.address),console.log("纬度："+s.latitude),console.log("经度："+s.longitude)},fail(s){e.index.setStorageSync("address",""),console.log(s)},complete(e){console.log(e)}})},formSubmit:function(d){let t=this;var a=d.detail.value;if(a.longitude=t.address.longitude,a.latitude=t.address.latitude,a.address_id=t.address_id,""==a.name)return e.index.showToast({title:"请输入收货人姓名",duration:1e3,icon:"none"}),!1;s.utils.isTelAvailable(a.phone)?t._post("user.address/edit",a,(function(s){console.log(t.delta),t.showSuccess(s.msg,(function(){e.index.navigateBack({delta:1})}))})):e.index.showToast({title:"请输入正确的联系方式",duration:2e3,icon:"none"})},formReset:function(e){console.log("清空数据")},showMulLinkageThreePicker(){this.$refs.mpvueCityPicker.show()},onConfirm(e){this.region=e.label.split(","),this.selectCity=e.label,this.province_id=e.cityCode[0],this.city_id=e.cityCode[1],this.region_id=e.cityCode[2]}}};const t=e._export_sfc(d,[["render",function(s,d,t,a,o,i){return e.e({a:o.address.name,b:e.o((e=>o.address.name=e.detail.value)),c:o.address.phone,d:e.o((e=>o.address.phone=e.detail.value)),e:o.address.detail,f:e.o((e=>o.address.detail=e.detail.value)),g:e.o(((...e)=>i.chooseLocation&&i.chooseLocation(...e))),h:o.address.address,i:e.o((e=>o.address.address=e.detail.value)),j:e.o(((...e)=>i.formSubmit&&i.formSubmit(...e))),k:e.o(((...e)=>i.formReset&&i.formReset(...e))),l:o.openWb},o.openWb?{m:o.wburl,n:e.o(((...e)=>i.handlePostMessage&&i.handlePostMessage(...e)))}:{},{o:s.theme(),p:e.n(s.theme()||"")})}]]);wx.createPage(t);
