"use strict";const t=require("../../common/vendor.js"),e={data:()=>({type:"",content:""}),onLoad(e){this.type=e.type;let n="";n="service"==this.type?"用户协议":"隐私协议",t.index.setNavigationBarTitle({title:n}),this.getData()},methods:{getData(){let t=this;t._post("index/loginSetting",{},(function(e){"service"==t.type?t.content=e.data.setting.service:t.content=e.data.setting.privacy}))}}};const n=t._export_sfc(e,[["render",function(t,e,n,i,a,o){return{a:a.content}}]]);wx.createPage(n);
