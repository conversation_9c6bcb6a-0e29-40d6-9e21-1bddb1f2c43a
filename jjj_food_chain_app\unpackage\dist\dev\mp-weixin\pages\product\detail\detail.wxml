<view data-theme="{{B}}" class="{{['good-detail-modal', C]}}"><view class="tc header" style="{{d}}"><view class="left-icon pr" style="{{c}}"><image class="reg180" bindtap="{{a}}" src="{{b}}" mode="" style="width:48rpx;height:48rpx"></image></view></view><view class="cover"><image wx:if="{{e}}" src="{{f}}" class="image" mode="aspectFill"></image></view><view class="bg-white"><view class="good_basic"><view class="name text-ellipsis">{{g}}</view><view class="f22 gray9">已售{{h}}</view><view class="gray6 sell-point" style="line-height:1.5">{{i}}</view></view></view><view class="detail bg-white"><view class="wrapper"><view class="properties"><view wx:if="{{j}}" class="property"><view class="title"><text class="name">规格</text></view><view class="values"><view wx:for="{{k}}" wx:for-item="value" wx:key="c" bindtap="{{value.b}}" class="{{['value', value.d && 'default']}}">{{value.a}}</view></view></view><block wx:if="{{l}}"><view wx:for="{{m}}" wx:for-item="item" wx:key="c" class="property"><view class="title"><text class="name">{{item.a}}</text></view><view class="values"><block wx:if="{{n}}"><view wx:for="{{item.b}}" wx:for-item="value" wx:key="c" bindtap="{{value.b}}" class="{{['value', value.d && 'default']}}">{{value.a}}</view></block></view></view></block><view wx:if="{{o}}" class="property"><view class="title"><text class="name">加料</text></view><view class="values"><view wx:for="{{p}}" wx:for-item="item" wx:key="c" bindtap="{{item.b}}" class="{{['value', item.d && 'default']}}"> +{{item.a}}</view></view></view></view></view></view><view class="spec-bottom"><view class="action"><view class="d-c d-a-s"><view class="left mb10"><view class="price"><text class="f22 fb">￥</text><text class="f36 fb mr12">{{q}}</text><text class="f24 gray9 text-l-t mr10">￥{{r}}</text><text wx:if="{{s}}"><text class="f22 gray9">打包费￥{{t}}</text></text></view></view><view class="f22 gray9">{{v}}</view></view><view class="btn-group"><button type="default" plain class="btn theme-borderbtn" size="min" hover-class="none" bindtap="{{w}}"><view class="icon icon-jian iconfont iconsami-select"></view></button><view class="number">{{x}}</view><button type="primary" class="btn theme-btn" size="min" hover-class="none" bindtap="{{y}}"><view class="icon icon-jia iconfont iconadd-select"></view></button></view></view><view><view class="add-to-cart-btn" bindtap="{{z}}"><view>加入购物车</view></view></view></view><view class="product-content"><view class="border-b-e"><view class="group-hd d-s-c"><text class="min-name pr f30 fb">详情</text></view></view><view class="content-box"><rich-text nodes="{{A}}"/></view></view></view>