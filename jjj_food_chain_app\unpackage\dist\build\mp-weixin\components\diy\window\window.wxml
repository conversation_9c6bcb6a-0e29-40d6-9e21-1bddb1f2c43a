<view class="diy-window" style="{{'background:' + p + ';' + ('padding:' + q)}}"><view wx:if="{{a}}" class="{{['data-list', c]}}"><view wx:for="{{b}}" wx:for-item="item" wx:key="b" class="item" bindtap="{{item.c}}"><view class="item-image"><image class="images" src="{{item.a}}" mode="aspectFill"></image></view></view></view><view wx:else class="display" style="{{'padding:' + o}}"><view class="img-box-wrap-1"><view class="img-box" bindtap="{{e}}"><image class="images" src="{{d}}" mode="aspectFill"></image></view></view><view class="percent-w50 d-s-c d-c"><view wx:if="{{f}}" class="img-box-wrap-2"><view class="img-box" bindtap="{{h}}"><image class="images" src="{{g}}" mode="aspectFill"></image></view></view><view class="d-s-c img-box-wrap-3"><view wx:if="{{i}}" class="img-box-wrap-4"><view class="img-box" bindtap="{{k}}"><image class="images" src="{{j}}" mode="aspectFill"></image></view></view><view wx:if="{{l}}" class="img-box-wrap-4"><view class="img-box" bindtap="{{n}}"><image class="images" src="{{m}}" mode="aspectFill"></image></view></view></view></view></view></view>