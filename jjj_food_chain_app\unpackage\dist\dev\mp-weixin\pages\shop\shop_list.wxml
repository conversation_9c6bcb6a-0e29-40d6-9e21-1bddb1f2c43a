<view data-theme="{{f}}" class="{{g}}"><scroll-view scroll-y="true" class="scroll-Y" style="{{d}}" lower-threshold="50" bindscrolltolower="{{e}}"><view class="address-list"><view wx:for="{{a}}" wx:for-item="item" wx:key="m" bindtap="{{item.k}}" class="{{['address', 'pt26', 'p-0-26', 'bg-white', item.l]}}"><view class="info flex-1"><view class="user f34 d-b-c"><view class="d-s-c"><view class="{{item.a}}"></view><view class="f32 fb gray3">{{item.b}}</view></view><view class="distance"><text>距离:{{item.c}}</text></view></view><view class="d-b-c"><view class="text-ellipsis f22 gray6">{{item.d}}</view><view class="text-ellipsis f22 gray6"><text class="icon iconfont icon-time" style="color:#333333;font-size:22rpx;margin-right:15rpx"></text><text wx:if="{{item.e}}">{{item.f}}</text><text wx:else>24小时</text></view></view><view class="mt16 mb30 d-b-c"><view></view><view><text wx:if="{{item.g}}" class="shop_status theme-borderbtn">营业中</text><text wx:else class="shop_status theme-borderbtn">暂停营业</text></view></view></view><view class="info-right d-b-c"><view class="d-s-c gray3"><view class="d-s-c" catchtap="{{item.h}}"><view class="icon iconfont icon-002dianhua"></view><text class="f26 fb ml18">咨询服务方</text></view><view class="ml25 d-s-c" catchtap="{{item.i}}"><view class="icon iconfont icon-fasong"></view><text class="f26 fb ml18">导航去这里</text></view></view><view class="f26 gray3 theme-btn" catchtap="{{item.j}}">去下单 </view></view></view><view wx:if="{{b}}" class="d-c-c p30"><text class="iconfont icon-wushuju"></text><text class="cont">亲，暂无相关记录哦</text></view><uni-load-more wx:else u-i="078ba873-0" bind:__l="__l" u-p="{{c||''}}"></uni-load-more></view></scroll-view></view>