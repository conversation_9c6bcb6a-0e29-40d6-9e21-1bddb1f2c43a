"use strict";const e=require("../../common/vendor.js"),t={data:()=>({imageList:[]}),onLoad(){},mounted(){this.chooseImageFunc()},methods:{chooseImageFunc(){let t=this;e.index.chooseImage({count:9,sizeType:["original","compressed"],sourceType:["album"],success:function(e){t.uploadFile(e.tempFilePaths)},fail:function(e){t.$emit("getImgs",null)},complete:function(e){}})},uploadFile:function(t){let i=this,o=0,n=t.length,a={token:e.index.getStorageSync("token"),app_id:i.getAppId()};e.index.showLoading({title:"上传中"}),t.forEach((function(t,s){e.index.uploadFile({url:i.websiteUrl+"/index.php?s=/api/file.upload/image",filePath:t,name:"iFile",formData:a,header:{appId:i.getAppId()},success:function(e){let t="object"==typeof e.data?e.data:JSON.parse(e.data);1===t.code&&i.imageList.push(t.data)},complete:function(){o++,n===o&&(e.index.hideLoading(),i.$emit("getImgs",i.imageList))}})}))}}};const i=e._export_sfc(t,[["render",function(e,t,i,o,n,a){return{}}]]);wx.createComponent(i);
