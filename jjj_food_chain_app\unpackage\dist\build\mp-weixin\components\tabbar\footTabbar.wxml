<view wx:if="{{a}}" class="" catchtap="{{h}}"><view class="foot-bottom"></view><view wx:if="{{b}}" class="foot-tavbar-container d-a-c" style="{{g}}"><view wx:for="{{c}}" wx:for-item="item" wx:key="e" class="{{['item', 'd-c-c', item.d && 'active']}}" bindtap="{{item.f}}"><view style="height:0;width:0;opacity:0">{{d}}</view><view class="inner d-c-c d-c"><image wx:if="{{e}}" class="images" src="{{item.a}}" mode="aspectFill"></image><text wx:if="{{f}}" style="{{item.c}}" class="text-name">{{item.b}}</text></view></view></view></view>