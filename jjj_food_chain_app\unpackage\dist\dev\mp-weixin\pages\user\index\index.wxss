/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
page {
  background-color: #fff;
}
.user-topbg {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 0;
  width: 100%;
  padding-bottom: 263rpx;
}
[data-theme=theme0] .user-topbg {
  background: linear-gradient(-180deg, rgba(254, 226, 56, 0.25) 0, rgba(255, 255, 255, 0.63) 81%, white 100%) !important;
}
[data-theme=theme1] .user-topbg {
  background: linear-gradient(-180deg, #ffe1e1 0, #ffffff 81%, #ffffff 100%) !important;
}
[data-theme=theme2] .user-topbg {
  background: linear-gradient(-180deg, #b3e8fb 0, #ffffff 81%, #ffffff 100%) !important;
}
[data-theme=theme3] .user-topbg {
  background: linear-gradient(-180deg, #c7ebd6 0, rgba(255, 255, 255, 0.63) 81%, white 100%) !important;
}
[data-theme=theme4] .user-topbg {
  background: linear-gradient(-180deg, #ffe5cb 0, #ffffff 81%, #ffffff 100%) !important;
}
[data-theme=theme5] .user-topbg {
  background: linear-gradient(-180deg, #ede4d9 0, #ffffff 81%, #ffffff 100%) !important;
}
[data-theme=theme6] .user-topbg {
  background: linear-gradient(-180deg, #d9d9ee 0, rgba(255, 255, 255, 0.63) 81%, white 100%) !important;
}
.user-header {
  position: relative;
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.user-header .user-header-inner {
  position: relative;
  padding: 30rpx 24rpx 26rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  z-index: 1;
}
.user-header .user-info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.user-header .photo,
.user-header .photo image {
  width: 82rpx;
  height: 82rpx;
  border-radius: 50%;
}
.user-header .info {
  padding-left: 16rpx;
  box-sizing: border-box;
  overflow: hidden;
}
.user-header .info .userName {
  font-size: 32rpx;
  color: #333;
}
.menu-wrap {
  position: relative;
  z-index: 1;
}
.menu-wrap .group-bd {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding-bottom: 28rpx;
}
.menu-wrap .item {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 25%;
  height: 150rpx;
  font-size: 24rpx;
  margin: 28rpx 0;
}
.menu-wrap .item .name {
  margin-top: 19rpx;
}
.menu-wrap .item .iconfont {
  font-size: 40rpx;
  color: #ffffff;
}
.menu-wrap .icon-round {
  width: 92rpx;
  height: 92rpx;
  color: #ffffff;
}
.order-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  font-size: 30rpx;
  color: #333;
  margin: 0 24rpx;
}
.my-wallet {
  margin: 0 24rpx;
  box-sizing: border-box;
  border-radius: 25rpx;
  background: #ffffff;
  margin-bottom: 40rpx;
  position: relative;
  height: 148rpx;
  padding: 40rpx 26rpx 30rpx 25rpx;
  box-sizing: border-box;
  box-shadow: 0rpx 5rpx 12rpx 0rpx rgba(6, 0, 1, 0.03);
}
.my-wallet .order_num {
  font-size: 38rpx;
  color: #111;
  font-weight: bold;
}
.my-wallet .iconfont.icon-jiantou1 {
  font-size: 24rpx;
  color: #999;
}
.head_top {
  width: 100%;
  height: var(--status-bar-height);
  height: 24rpx;
}