"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const modal = () => "../../../components/modal/modal.js";
const popupLayer = () => "../../../components/popup-layer/popup-layer.js";
const prospec = () => "./popup/spec.js";
const _sfc_main = {
  components: {
    modal,
    popupLayer,
    prospec
  },
  data() {
    return {
      loadingClock: false,
      isDetail: false,
      isLoading: true,
      supplier: {
        name: "",
        business_status: 0
      },
      shop_supplier_id: 0,
      loading: true,
      currentCateId: 0,
      //默认分类
      cateScrollTop: 0,
      menuScrollIntoView: "",
      /* 分类定位初始化 */
      sizeCalcState: false,
      productList: [],
      /* 商品详情数据 */
      productModel: {},
      // 
      clock: false,
      total_price: 0,
      /* 购物车弹窗 */
      cart_total_num: 0,
      cart_product_id: 0,
      cartPopupVisible: false,
      /* 弹窗购物车列表 */
      cart_list: [],
      cart_type: 0,
      phoneHeight: 0,
      /*可滚动视图区域高度*/
      scrollviewHigh: 0,
      /* 添加商品锁 */
      addclock: false,
      /* 收货地址 */
      address_detail: "",
      longitude: 0,
      latitude: 0,
      address_id: 0,
      /* ---- */
      /* 是否收取打包费 */
      bag_type: 1,
      total_bag_price: 0,
      // orderType 与 dinner_type 重复待删除
      orderType: "",
      /* 10配送20自提30店内40外卖 */
      dinner_type: 20,
      // 配送方式
      delivery_set: [],
      isFirst: true,
      /* 起送 */
      min_money: 0,
      min_money_diff: 0,
      line_price: 0,
      scrollLast: 0
    };
  },
  onLoad(e) {
    let self = this;
    self.orderType = e.orderType;
    if (self.orderType == "takeout") {
      self.dinner_type = 10;
    } else {
      self.dinner_type = 20;
    }
    self.shop_supplier_id = common_vendor.index.getStorageSync("selectedId") ? common_vendor.index.getStorageSync("selectedId") : 0;
  },
  onShow() {
    let self = this;
    self.isDetail = false;
    self.init();
  },
  methods: {
    stopClick() {
      return false;
    },
    /* 获取用户登录状态 */
    getUserInfo() {
      let self = this;
      self.loading = true;
      self.isLogin = false;
      self._get(
        "index/userInfo",
        {},
        function(res) {
          if (res.data && res.data.user) {
            self.isLogin = true;
          }
          if (self.dinner_type == 10 && res.data && res.data.user) {
            if (res.data.user.address && res.data.user.address_id) {
              self.latitude = res.data.user.address.latitude;
              self.longitude = res.data.user.address.longitude;
              self.address_detail = res.data.user.address.detail;
              self.getCategory(true);
            } else {
              self.getcityData();
            }
          } else {
            self.getcityData();
          }
        }
      );
    },
    init() {
      this.addclock = false;
      this.clock = false;
      this.loadingClock = false;
      this.loading = true;
      this.isLoading = true;
      this.productList = [];
      this.sizeCalcState = false;
      this.getUserInfo();
    },
    scrollInit() {
      let self = this;
      if (self.scrollviewHigh) {
        return;
      }
      common_vendor.index.getSystemInfo({
        success(res) {
          self.phoneHeight = res.windowHeight;
          let view = common_vendor.index.createSelectorQuery().select(".nav");
          view.boundingClientRect((data) => {
            let h = self.phoneHeight - data.height;
            self.scrollviewHigh = h;
          }).exec();
        }
      });
    },
    /* 获取商品类型 */
    getCategory(flag) {
      let self = this;
      this.sizeCalcState = false;
      let delivery = self.orderType == "takeout" ? 10 : 20;
      self._get(
        "product.category/index",
        {
          type: 0,
          /* 0外卖，1店内 */
          shop_supplier_id: self.shop_supplier_id || 0,
          longitude: self.longitude,
          latitude: self.latitude,
          delivery,
          order_type: 0,
          table_id: 0,
          cart_type: self.cart_type || 0
        },
        function(res) {
          self.supplier = res.data.supplier;
          self.min_money = (res.data.supplier.min_money * 1).toFixed(2);
          self.productList = res.data.list;
          if (!self.currentCateId) {
            self.currentCateId = self.productList[0].category_id;
          }
          self.delivery_set = res.data.supplier.delivery_set;
          if (self.isFirst) {
            if (self.orderType) {
              let orderType = self.orderType == "takeout" ? "10" : "20";
              if (self.delivery_set.indexOf(orderType) == -1) {
                let content = "";
                if (orderType == "20") {
                  content = "当前门店不支持自取，已为您切换为配送";
                  self.orderType = "takeout";
                } else {
                  content = "当前门店不支持配送，已为您切换为自取";
                  self.orderType = "takein";
                }
                common_vendor.index.showModal({
                  title: "提示",
                  content,
                  showCancel: false
                });
              }
            }
            self.isFirst = false;
          }
          self.address_id = res.data.address_id;
          self.bag_type = res.data.supplier.bag_type;
          self.loading = false;
          self.isLoading = false;
          self.$nextTick(function() {
            self.scrollInit();
          });
          if (self.isLogin && flag) {
            self.getCart();
          }
        },
        function(err) {
          self.showError(err.msg, () => {
            self.gotoPage("/pages/index/index");
          });
        }
      );
    },
    reCart(res) {
      let self = this;
      self.loadingClock = false;
      self.cart_total_num = res.data.cartInfo.cart_total_num;
      self.line_price = res.data.cartInfo.total_line_money;
      self.total_price = res.data.cartInfo.total_pay_price;
      self.total_bag_price = res.data.cartInfo.total_bag_price;
      self.min_money_diff = res.data.cartInfo.min_money_diff;
      if (!self.cart_list || self.cart_list == "") {
        self.cartPopupVisible = false;
      }
    },
    addCart(goods) {
      let self = this;
      if (goods.spec_types == 20) {
        self.gotoDetail(goods);
        return;
      }
      if (self.addclock) {
        return;
      }
      if (goods.limit_num != 0 && goods.limit_num <= goods.cart_num) {
        common_vendor.index.showToast({
          icon: "none",
          title: "超过限购数量"
        });
        return;
      }
      if (goods.product_stock <= 0 || goods.product_stock <= goods.cart_num) {
        common_vendor.index.showToast({
          icon: "none",
          title: "没有更多库存了"
        });
        return;
      }
      let params = {
        product_id: goods.product_id,
        product_num: 1,
        product_sku_id: goods.sku[0].product_sku_id,
        attr: "",
        feed: "",
        describe: "",
        price: goods.sku[0].product_price,
        bag_price: goods.sku[0].bag_price,
        shop_supplier_id: goods.supplier.shop_supplier_id,
        cart_type: 0,
        dinner_type: self.dinner_type,
        product_price: goods.sku[0].line_price,
        delivery: self.orderType == "takeout" ? 10 : 20
      };
      self.addclock = true;
      common_vendor.index.showLoading({
        title: "",
        mask: true
      });
      self._post(
        "order.cart/add",
        params,
        function(res) {
          common_vendor.index.hideLoading();
          self.reCart(res);
          self.addclock = false;
          self.getCategory(false);
        },
        function(err) {
          common_vendor.index.hideLoading();
          self.addclock = false;
        }
      );
    },
    reduceFunc(goods) {
      let self = this;
      if (self.addclock || goods.cart_num <= 0) {
        return;
      }
      if (goods.spec_types == 20) {
        self.openCartPopup(goods.product_id);
        return;
      }
      let product_id = goods.product_id;
      self.addclock = true;
      common_vendor.index.showLoading({
        title: "",
        mask: true
      });
      self._post(
        "order.cart/productSub",
        {
          product_id,
          type: "down",
          cart_type: 0,
          dinner_type: self.dinner_type,
          shop_supplier_id: self.shop_supplier_id,
          delivery: self.orderType == "takeout" ? 10 : 20
        },
        function(res) {
          common_vendor.index.hideLoading();
          self.reCart(res);
          self.addclock = false;
          self.getCategory(false);
        },
        function() {
          common_vendor.index.hideLoading();
          self.addclock = false;
        }
      );
    },
    getCartLoading() {
      common_vendor.index.__f__("log", "at pages/product/list/takeaway.vue:530", "getCartLoading");
      let self = this;
      self._get(
        "order.cart/lists",
        {
          shop_supplier_id: self.shop_supplier_id,
          cart_type: 0,
          delivery: self.orderType == "takeout" ? 10 : 20,
          product_id: self.cart_product_id
        },
        function(res) {
          self.cart_list = res.data.productList;
          self.isLoading = true;
          self.reCart(res);
          common_vendor.index.hideLoading();
        },
        (err) => {
          common_vendor.index.hideLoading();
        }
      );
    },
    getCart() {
      let self = this;
      if (!self.isLogin) {
        return;
      }
      return new Promise((resolve, reject) => {
        common_vendor.index.showLoading({
          title: "",
          mask: true
        });
        self._get(
          "order.cart/lists",
          {
            shop_supplier_id: self.shop_supplier_id,
            cart_type: 0,
            delivery: self.orderType == "takeout" ? 10 : 20,
            product_id: self.cart_product_id
          },
          function(res) {
            common_vendor.index.hideLoading();
            self.isLoading = true;
            self.reCart(res);
            self.cart_list = res.data.productList;
            resolve(true);
          },
          (err) => {
            common_vendor.index.hideLoading();
          }
        );
      });
    },
    /* 购物车商品添加 */
    cartAdd(goods) {
      let self = this;
      if (self.addclock) {
        return;
      }
      self.addclock = true;
      let product_id = goods.product_id;
      let total_num = 1;
      common_vendor.index.showLoading({
        title: "",
        mask: true
      });
      self._post(
        "order.cart/sub",
        {
          product_id,
          total_num,
          cart_id: goods.cart_id,
          type: "up",
          cart_type: 0,
          dinner_type: self.dinner_type,
          shop_supplier_id: self.shop_supplier_id,
          delivery: self.orderType == "takeout" ? 10 : 20
        },
        function(res) {
          self.addclock = false;
          self.getCategory(false);
          self.getCartLoading();
        },
        function() {
          common_vendor.index.hideLoading();
          self.addclock = false;
        }
      );
    },
    /* 购物车商品减少 */
    cartReduce(goods) {
      let self = this;
      if (self.addclock) {
        return;
      }
      self.addclock = true;
      let product_id = goods.product_id;
      common_vendor.index.showLoading({
        title: "",
        mask: true
      });
      self._post(
        "order.cart/sub",
        {
          product_id,
          total_num: 1,
          cart_id: goods.cart_id,
          type: "down",
          cart_type: 0,
          dinner_type: self.dinner_type,
          shop_supplier_id: self.shop_supplier_id,
          delivery: self.orderType == "takeout" ? 10 : 20
        },
        function(res) {
          self.addclock = false;
          self.getCategory(false);
          self.getCartLoading();
        },
        function() {
          common_vendor.index.hideLoading();
          self.addclock = false;
        }
      );
    },
    takout() {
      if (this.orderType == "takeout")
        return;
      this.orderType = "takeout";
      this.dinner_type = 10;
      this.init();
    },
    takein() {
      if (this.orderType == "takein")
        return;
      this.orderType = "takein";
      this.dinner_type = 20;
      this.init();
    },
    handleMenuTap(id) {
      let self = this;
      if (!self.sizeCalcState) {
        self.calcSize();
      }
      self.currentCateId = id;
      self.$nextTick(() => {
        self.cateScrollTop = self.productList.find((item) => item.category_id == id).top;
        if (!self.cateScrollTop && self.cateScrollTop != 0) {
          setTimeout(function() {
            self.handleMenuTap(id);
          }, 300);
        }
      });
    },
    handleGoodsScroll({
      detail
    }) {
      if (!this.sizeCalcState) {
        this.calcSize();
      }
      this.scrollLast = detail.scrollTop;
      const {
        scrollTop
      } = detail;
      let tabs = this.productList.filter((item) => item.top - 5 <= scrollTop).reverse();
      if (tabs.length > 0) {
        this.currentCateId = tabs[0].category_id;
      }
    },
    calcSize() {
      let h = 0;
      this.productList.forEach((item) => {
        let view = common_vendor.index.createSelectorQuery().select(`#cate-${item.category_id}`);
        view.fields(
          {
            size: true
          },
          (data) => {
            item.top = h;
            if (data != null) {
              h += data.height;
            }
            item.bottom = h;
          }
        ).exec();
      });
      this.sizeCalcState = true;
    },
    closeGoodDetailModal(num, res) {
      this.isDetail = false;
      this.clock = false;
      if (num) {
        this.reCart(res);
        this.getCategory(false);
      }
    },
    async openCartPopup(n) {
      if (!this.cart_total_num) {
        return;
      }
      if (!this.cartPopupVisible) {
        this.cart_list = [];
        this.cart_product_id = n;
        await this.getCart();
        this.cartPopupVisible = !this.cartPopupVisible;
      } else {
        this.cartPopupVisible = !this.cartPopupVisible;
      }
    },
    closeCallBack() {
      this.cart_product_id = 0;
      this.cart_list = [];
      this.cartPopupVisible = false;
    },
    handleCartClear() {
      let self = this;
      common_vendor.index.showModal({
        title: "提示",
        content: "确定清空购物车么",
        success(res) {
          if (res.confirm) {
            self.clearCart();
          } else if (res.cancel) {
            common_vendor.index.__f__("log", "at pages/product/list/takeaway.vue:755", "用户点击取消");
          }
        }
      });
    },
    clearCart() {
      let self = this;
      self._post(
        "order.cart/delete",
        {
          shop_supplier_id: self.shop_supplier_id,
          cart_type: 0
        },
        function(res) {
          self.cartPopupVisible = false;
          self.cart_list = [];
          self.init();
        }
      );
    },
    toPay() {
      let self = this;
      if (self.address_id == 0 && self.orderType == "takeout") {
        common_vendor.index.showModal({
          title: "提示",
          content: "您还没选择收货地址,请先选择收货地址",
          success() {
            self.gotoPage("/pages/user/address/storeaddress?shop_supplier_id=" + self.shop_supplier_id);
          }
        });
        return;
      }
      if (self.loadingClock) {
        return;
      }
      self.loadingClock = true;
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      self._get(
        "order.cart/lists",
        {
          shop_supplier_id: self.shop_supplier_id,
          cart_type: 0,
          delivery: self.orderType == "takeout" ? 10 : 20
        },
        function(res) {
          common_vendor.index.hideLoading();
          self.reCart(res);
          self.cart_list = res.data.productList;
          let arrIds = [];
          self.cart_list.forEach((item) => {
            arrIds.push(item.cart_id);
          });
          if (arrIds.length == 0) {
            common_vendor.index.showToast({
              title: "请选择商品",
              icon: "none"
            });
            return false;
          }
          let delivery = self.orderType == "takeout" ? 10 : 20;
          common_vendor.index.navigateTo({
            url: "/pages/order/confirm-order?order_type=cart&cart_ids=" + arrIds.join(",") + "&delivery=" + delivery + "&shop_supplier_id=" + self.shop_supplier_id + "&cart_type=0&dinner_type=" + delivery
          });
          self.loadingClock = false;
        },
        () => {
          common_vendor.index.hideLoading();
          self.loadingClock = false;
        }
      );
    },
    /* 新 打开商品详情弹窗 */
    gotoDetail(e) {
      let self = this;
      let delivery = this.orderType == "takeout" ? 10 : 20;
      self.productModel = {
        product_id: e.product_id || 0,
        delivery,
        bag_type: self.bag_type || 0,
        dinner_type: self.dinner_type || 0,
        cart_type: self.cart_type || 0,
        table_id: 0,
        shop_supplier_id: self.shop_supplier_id || 0
      };
      self.isDetail = true;
    },
    /*  打开商品详情页面 暂留*/
    // gotoDetailPage() {
    // 	let self = this;
    // 	let delivery = this.orderType == 'takeout' ? 10 : 20;
    // 	let url = '/pages/product/detail/detail?product_id=' +
    // 		e.product_id +
    // 		'&delivery=' +
    // 		delivery +
    // 		'&bag_type=' +
    // 		this.bag_type +
    // 		'&dinner_type=' +
    // 		this.dinner_type +
    // 		'&cart_type=' +
    // 		this.cart_type
    // 	/* 登录后才能进入详情页 */
    // 	self._get('user.index/setting', {}, res => {
    // 		self.gotoPage(url);
    // 	})
    // },
    /*获取定位方式*/
    getcityData() {
      let self = this;
      self.getLocation();
    },
    /*获取用户坐标*/
    getLocation(callback) {
      let self = this;
      common_vendor.index.getLocation({
        type: "wgs84",
        success(res) {
          common_vendor.index.__f__("log", "at pages/product/list/takeaway.vue:906", "getLocation");
          common_vendor.index.__f__("log", "at pages/product/list/takeaway.vue:907", res);
          self.longitude = res.longitude;
          self.latitude = res.latitude;
          self.getCategory(true);
        },
        fail(err) {
          self.longitude = 0;
          self.latitude = 0;
          common_vendor.index.showToast({
            title: "获取定位失败，请点击右下角按钮打开定位权限",
            duration: 2e3,
            icon: "none"
          });
          self.getCategory(true);
        }
      });
    },
    /* 公众号获取坐标 */
    getWxLocation(signPackage, callback) {
    }
  }
};
if (!Array) {
  const _easycom_popup_layer2 = common_vendor.resolveComponent("popup-layer");
  const _component_prospec = common_vendor.resolveComponent("prospec");
  (_easycom_popup_layer2 + _component_prospec)();
}
const _easycom_popup_layer = () => "../../../components/popup-layer/popup-layer.js";
if (!Math) {
  _easycom_popup_layer();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.orderType == "takein"
  }, $data.orderType == "takein" ? {
    b: common_vendor.t($data.supplier.name),
    c: common_vendor.t($data.supplier.distance)
  } : {}, {
    d: $data.orderType == "takeout"
  }, $data.orderType == "takeout" ? common_vendor.e({
    e: common_vendor.t($data.supplier.name),
    f: $data.address_detail
  }, $data.address_detail ? {
    g: common_vendor.t($data.address_detail || "请选择收货地址"),
    h: common_vendor.o(($event) => _ctx.gotoPage("/pages/user/address/storeaddress?shop_supplier_id=" + $data.shop_supplier_id))
  } : {
    i: common_vendor.o(($event) => _ctx.gotoPage("/pages/user/address/storeaddress?shop_supplier_id=" + $data.shop_supplier_id))
  }) : {}, {
    j: common_vendor.f($data.delivery_set, (item, index, i0) => {
      return common_vendor.e({
        a: item == "10"
      }, item == "10" ? {
        b: $data.orderType == "takeout" ? 1 : "",
        c: common_vendor.o((...args) => $options.takout && $options.takout(...args), "a-" + index)
      } : {}, {
        d: "a-" + index
      });
    }),
    k: common_vendor.f($data.delivery_set, (item, index, i0) => {
      return common_vendor.e({
        a: item == "20"
      }, item == "20" ? {
        b: $data.orderType == "takein" ? 1 : "",
        c: common_vendor.o((...args) => $options.takein && $options.takein(...args), "b-" + index)
      } : {}, {
        d: "b-" + index
      });
    }),
    l: common_vendor.f($data.productList, (item, index, i0) => {
      return common_vendor.e({
        a: item.products.length != 0
      }, item.products.length != 0 ? common_vendor.e({
        b: item.images
      }, item.images ? {
        c: item.images.file_path
      } : {}, {
        d: common_vendor.t(item.name),
        e: item.product_num
      }, item.product_num ? {
        f: common_vendor.t(item.product_num)
      } : {}, {
        g: `menu-${item.category_id}`,
        h: item.category_id === $data.currentCateId ? 1 : "",
        i: common_vendor.o(($event) => $options.handleMenuTap(item.category_id), index)
      }) : {}, {
        j: index
      });
    }),
    m: common_vendor.s("height:" + $data.scrollviewHigh + "px;"),
    n: $data.menuScrollIntoView,
    o: common_vendor.f($data.productList, (item, index, i0) => {
      return common_vendor.e({
        a: item.products.length != 0
      }, item.products.length != 0 ? {
        b: common_vendor.t(item.name),
        c: common_vendor.f(item.products, (good, key, i1) => {
          return common_vendor.e({
            a: good.product_stock <= 0
          }, good.product_stock <= 0 ? {} : {}, {
            b: good.product_image,
            c: common_vendor.t(good.product_name),
            d: common_vendor.t(good.selling_point),
            e: common_vendor.t(good.product_price),
            f: good.product_price * 1 != good.line_price * 1
          }, good.product_price * 1 != good.line_price * 1 ? {
            g: common_vendor.t(good.line_price * 1)
          } : {}, {
            h: good.cart_num != 0
          }, good.cart_num != 0 ? {
            i: common_vendor.o(($event) => $options.reduceFunc(good), key),
            j: "/static/icon/cart/reduce-" + _ctx.theme() + ".png"
          } : {}, {
            k: good.cart_num != 0
          }, good.cart_num != 0 ? {
            l: common_vendor.t(good.cart_num)
          } : {}, {
            m: good.product_stock > 0
          }, good.product_stock > 0 ? {
            n: common_vendor.o(($event) => $options.addCart(good), key),
            o: "/static/icon/cart/add-" + _ctx.theme() + ".png"
          } : {}, {
            p: good.product_stock <= 0
          }, good.product_stock <= 0 ? {
            q: common_assets._imports_0$3
          } : {}, {
            r: common_vendor.o(($event) => $options.gotoDetail(good), key),
            s: key
          });
        }),
        d: `cate-${item.category_id}`
      } : {}, {
        e: index
      });
    }),
    p: common_vendor.s("padding-bottom:" + ($data.scrollviewHigh * 2 - 238) + "rpx;"),
    q: common_vendor.s("height:" + $data.scrollviewHigh + "px;"),
    r: $data.cateScrollTop,
    s: common_vendor.o((...args) => $options.handleGoodsScroll && $options.handleGoodsScroll(...args)),
    t: $data.cart_total_num
  }, $data.cart_total_num ? {
    v: common_vendor.t($data.cart_total_num)
  } : {}, {
    w: $data.cart_total_num
  }, $data.cart_total_num ? common_vendor.e({
    x: common_vendor.t($data.total_price),
    y: common_vendor.t($data.line_price),
    z: $data.total_bag_price != 0
  }, $data.total_bag_price != 0 ? {
    A: common_vendor.t($data.total_bag_price)
  } : {}) : {}, {
    B: $data.min_money_diff <= 0 || $data.orderType != "takeout"
  }, $data.min_money_diff <= 0 || $data.orderType != "takeout" ? {
    C: !$data.cart_total_num,
    D: common_vendor.o((...args) => $options.toPay && $options.toPay(...args))
  } : {}, {
    E: $data.min_money_diff > 0 && $data.total_price == 0 && $data.orderType == "takeout"
  }, $data.min_money_diff > 0 && $data.total_price == 0 && $data.orderType == "takeout" ? {
    F: common_vendor.t("￥" + $data.min_money + "起送")
  } : {}, {
    G: $data.min_money_diff > 0 && $data.total_price != 0 && $data.orderType == "takeout"
  }, $data.min_money_diff > 0 && $data.total_price != 0 && $data.orderType == "takeout" ? {
    H: common_vendor.t("还差￥" + $data.min_money_diff + "起送")
  } : {}, {
    I: common_vendor.o(($event) => $options.openCartPopup(0)),
    J: $data.cart_total_num > 0
  }, $data.cart_total_num > 0 ? common_vendor.e({
    K: $data.total_bag_price
  }, $data.total_bag_price ? {
    L: common_vendor.t($data.total_bag_price)
  } : {}, {
    M: common_vendor.o((...args) => $options.handleCartClear && $options.handleCartClear(...args)),
    N: common_vendor.f($data.cart_list, (item, index, i0) => {
      return common_vendor.e({
        a: item.product_num > 0
      }, item.product_num > 0 ? common_vendor.e({
        b: item.image.file_path,
        c: common_vendor.t(item.product.product_name),
        d: common_vendor.t(item.describe),
        e: common_vendor.t(item.price),
        f: $data.bag_type != 1
      }, $data.bag_type != 1 ? {
        g: common_vendor.t(item.bag_price)
      } : {}, {
        h: common_vendor.o(($event) => $options.cartReduce(item), index),
        i: "/static/icon/cart/reduce-" + _ctx.theme() + ".png",
        j: common_vendor.t(item.product_num),
        k: common_vendor.o(($event) => $options.cartAdd(item), index),
        l: "/static/icon/cart/add-" + _ctx.theme() + ".png"
      }) : {}, {
        m: index
      });
    }),
    O: common_vendor.o($options.closeCallBack),
    P: common_vendor.p({
      direction: "top",
      ["show-pop"]: $data.cartPopupVisible
    })
  }) : {}, {
    Q: $data.isDetail
  }, $data.isDetail ? {
    R: common_vendor.o($options.closeGoodDetailModal),
    S: common_vendor.p({
      productModel: $data.productModel
    })
  } : {}, {
    T: $data.loading
  }, $data.loading ? {
    U: common_assets._imports_1$1
  } : {}, {
    V: _ctx.theme(),
    W: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/product/list/takeaway.js.map
