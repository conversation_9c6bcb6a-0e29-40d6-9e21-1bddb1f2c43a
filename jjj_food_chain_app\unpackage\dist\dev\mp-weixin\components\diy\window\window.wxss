
.diy-window {
	overflow: hidden;
}
.diy-window .data-list {
	display: flex;
	flex-wrap: wrap;
}
.diy-window .images {
	width: 100%;
	height: 100%;
}
.diy-window .data-list.column__2 .item {
	width: 50%;
}
.diy-window .data-list.column__3 .item {
	width: 33.333333333333%;
}
.diy-window .data-list.column__4 .item {
	width: 25%;
}
.diy-window .data-list.column__2 .item {
	position: relative;
	padding-top: 50%;
}
.diy-window .data-list.column__3 .item {
	position: relative;
	padding-top: 33.3333333%;
}
.diy-window .data-list.column__4 .item {
	position: relative;
	padding-top: 25%;
}
.diy-window .data-list .item .item-image,
.diy-window .display .img-box {
	position: absolute;
	box-sizing: border-box;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
.diy-window .display {
	display: flex;
}
.diy-window .display .img-box-wrap-1 {
	position: relative;
	width: 50%;
	padding-top: 50%;
}
.diy-window .display .img-box-wrap-2 {
	position: relative;
	width: 100%;
	height: 50%;
}
.diy-window .display .percent-w50 {
	box-sizing: border-box;
	width: 50%;
}
.diy-window .display .img-box .item-image {
	height: 100%;
}
.diy-window .display .img-box-wrap-3 {
	width: 100%;
	height: 50%;
}
.diy-window .display .img-box-wrap-4 {
	position: relative;
	width: 50%;
	height: 100%;
}
