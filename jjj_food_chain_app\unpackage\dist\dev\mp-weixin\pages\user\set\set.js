"use strict";
const common_vendor = require("../../../common/vendor.js");
require("../../../env/config.js");
const Popup = () => "../../../components/uni-popup.js";
const Upload = () => "../../../components/upload/upload.js";
const _sfc_main = {
  components: {
    Popup,
    Upload
  },
  data() {
    return {
      userInfo: {},
      isBirthday: false,
      birthday: "",
      imageList: [],
      newName: "",
      type: "",
      isUpload: false,
      mobileModel: {
        mobile: "",
        code: ""
      },
      passwordModel: {
        mobile: "",
        code: "",
        password: "",
        repassword: ""
      },
      isPhone: false,
      isPassword: false,
      sms_open: false
    };
  },
  onShow() {
    this.getData();
  },
  methods: {
    clearStorage() {
      common_vendor.index.clearStorageSync();
    },
    maskPhone(phone) {
      if (!phone || phone.length !== 11)
        return phone;
      return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1***$2");
    },
    onChooseAvatar(e) {
      let self = this;
      self.uploadFile([e.detail.avatarUrl]);
    },
    /*获取数据*/
    getData() {
      let self = this;
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      self._get("user.index/setting", {}, function(res) {
        self.userInfo = res.data.userInfo;
        common_vendor.index.hideLoading();
      });
    },
    /* 修改头像 */
    changeAvatarUrl() {
      let self = this;
      self.isUpload = true;
    },
    /*上传图片*/
    uploadFile: function(tempList) {
      let self = this;
      self.imageList = [];
      let i = 0;
      let img_length = tempList.length;
      let params = {
        token: common_vendor.index.getStorageSync("token"),
        app_id: self.getAppId()
      };
      common_vendor.index.showLoading({
        title: "图片上传中",
        mask: true
      });
      tempList.forEach(function(filePath, fileKey) {
        common_vendor.index.uploadFile({
          url: self.websiteUrl + "/index.php?s=/api/file.upload/image",
          filePath,
          name: "iFile",
          formData: params,
          success: function(res) {
            let result = typeof res.data === "object" ? res.data : JSON.parse(res.data);
            if (result.code === 1) {
              self.imageList.push(result.data);
            } else {
              self.showError(result.msg);
            }
          },
          complete: function() {
            i++;
            if (img_length === i) {
              common_vendor.index.hideLoading();
              self.getImgsFunc(self.imageList);
            }
          }
        });
      });
    },
    /*获取上传的图片*/
    getImgsFunc(e) {
      if (e && typeof e != "undefined") {
        let self = this;
        self.userInfo.avatarUrl = e[0].file_path;
        self.update();
        self.isUpload = false;
      }
    },
    logout() {
      let self = this;
      self._post("/user.User/logOut", {}, (res) => {
        common_vendor.index.removeStorageSync("token");
        common_vendor.index.removeStorageSync("user_id");
        self.gotoPage("/pages/index/index");
      });
    },
    update() {
      let self = this;
      if (self.loading) {
        return;
      }
      common_vendor.index.showLoading({
        title: "加载中",
        mask: true
      });
      let params = self.userInfo;
      self.loading = true;
      self._post("user.user/updateInfo", params, function(res) {
        self.showSuccess(
          "修改成功",
          function() {
            self.loading = false;
            common_vendor.index.hideLoading();
            self.getData();
          },
          function(err) {
            common_vendor.index.hideLoading();
            self.loading = false;
          }
        );
      });
    }
  }
};
if (!Array) {
  const _component_Upload = common_vendor.resolveComponent("Upload");
  _component_Upload();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.userInfo.avatarUrl || "/static/default.png",
    b: common_vendor.o((...args) => $options.onChooseAvatar && $options.onChooseAvatar(...args)),
    c: common_vendor.t($data.userInfo.user_id),
    d: $data.userInfo.nickName,
    e: common_vendor.o(($event) => $data.userInfo.nickName = $event.detail.value),
    f: common_vendor.t($options.maskPhone($data.userInfo.mobile)),
    g: common_vendor.o((...args) => $options.update && $options.update(...args)),
    h: common_vendor.o(($event) => $options.logout()),
    i: $data.isUpload
  }, $data.isUpload ? {
    j: common_vendor.o($options.getImgsFunc),
    k: common_vendor.p({
      num: 1
    })
  } : {}, {
    l: _ctx.theme(),
    m: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/user/set/set.js.map
