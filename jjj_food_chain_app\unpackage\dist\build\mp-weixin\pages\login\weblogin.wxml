<view data-theme="{{v}}" class="{{['login-container', 'data-v-69128e3d', w]}}"><view class="skip data-v-69128e3d" bindtap="{{a}}">跳过→</view><view wx:if="{{b}}" class=" data-v-69128e3d"><view class="login_topbpx data-v-69128e3d"><view wx:if="{{c}}" class="login_tit data-v-69128e3d">快速注册</view><view wx:if="{{d}}" class="login_tit data-v-69128e3d">账号登录</view></view><view class="group-bd data-v-69128e3d"><view class="form-level d-s-c data-v-69128e3d"><input class="login-input data-v-69128e3d" adjust-position="{{false}}" type="text" placeholder="请填写手机号" value="{{e}}" bindinput="{{f}}"/></view><view class="form-level d-s-c data-v-69128e3d"><input class="login-input data-v-69128e3d" adjust-position="{{false}}" type="text" password="true" placeholder="请输入密码" value="{{g}}" bindinput="{{h}}"/></view></view></view><view bindtap="{{l}}" class="d-s-c gray6 mt30 data-v-69128e3d"><view class="{{['icon', 'iconfont', 'icon-tijiaochenggong', 'data-v-69128e3d', i]}}"></view> 我已阅读并接受<text class="theme-notice data-v-69128e3d" bindtap="{{j}}">《用户协议》</text>和<text class="theme-notice data-v-69128e3d" bindtap="{{k}}">《隐私政策》</text></view><button wx:if="{{m}}" class="theme-btn sub-btn data-v-69128e3d" bindtap="{{n}}">立即注册</button><button wx:if="{{o}}" class="theme-btn sub-btn data-v-69128e3d" bindtap="{{p}}">立即登录</button><view class="f30 gray6 d-c-c data-v-69128e3d"><view wx:if="{{q}}" class="data-v-69128e3d" bindtap="{{r}}">快速注册</view><view wx:if="{{s}}" class="data-v-69128e3d" bindtap="{{t}}">账号登录</view></view></view>