"use strict";const e=require("../../common/vendor.js"),a=require("../../common/pay.js"),r={data:()=>({balance:"",balanceType:!1,type:0,loading:!0,order_id:0,order_type:0,pay_type:0,checkedPay:[],payPrice:"",hasBanlance:!1}),computed:{},onLoad(e){this.order_id=e.order_id,e.order_type&&(this.order_type=e.order_type),this.getData()},methods:{getData(){let a=this;a.loading=!0,e.index.showLoading({title:"加载中"});let r="user.order/pay";30==a.order_type&&(r="user.UserCard/pay"),40==a.order_type&&(r="balance.plan/pay"),50==a.order_type&&(r="plus.package.Package/pay"),60==a.order_type&&(r="plus.driver.apply/pay"),70==a.order_type&&(r="plus.group.Order/pay");let t={order_id:a.order_id,pay_source:a.getPlatform()};a._get(r,t,(function(r){a.loading=!1,a.payPrice=r.data.payPrice,a.balance=r.data.balance||"",a.checkedPay=r.data.payTypes.payType,a.hasBanlance=r.data.payTypes.use_balance,a.checkedPay.length>0?a.pay_type=a.checkedPay[0]:a.pay_type=0,e.index.hideLoading()}))},switch2Change(e){this.balanceType=e.detail.value},submit(){let r=this;r.loading=!0,e.index.showLoading({title:"加载中"});let t="user.order/pay";30==r.order_type&&(t="user.UserCard/pay"),40==r.order_type&&(t="balance.plan/pay"),50==r.order_type&&(t="plus.package.Package/pay"),60==r.order_type&&(t="plus.driver.apply/pay"),70==r.order_type&&(t="plus.group.Order/pay");let d=1==r.balanceType?1:0;0==r.payPrice&&(d=1);let o=r.pay_type;10==o&&(o=0);let p={order_id:r.order_id,pay_source:r.getPlatform(),payType:o,use_balance:d};r._post(t,p,(function(t){r.loading=!1,e.index.hideLoading(),a.pay(t,r,r.paySuccess,r.payError)}))},paySuccess(a){let r=this;30==r.order_type||40==r.order_type||50==r.order_type?e.index.showModal({title:"提示",content:"支付成功",success(){e.index.navigateBack({delta:parseInt(1)})}}):60==r.order_type?e.index.showModal({title:"提示",content:"支付成功",success(){r.gotoPage("/pages/user/index/index")}}):70==r.order_type?r.gotoPage("/pages/order/group/pay-success?order_id="+a.data.order_id,"reLaunch"):r.gotoPage("/pages/order/pay-success/pay-success?order_id="+a.data.order_id,"reLaunch")},payError(a){let r=this;30==r.order_type||40==r.order_type||50==r.order_type?e.index.showModal({title:"提示",content:"支付失败",success(){e.index.navigateBack({delta:parseInt(1)})}}):60==r.order_type?e.index.showModal({title:"提示",content:"支付失败",success(){r.gotoPage("/pages/user/index/index")}}):70==r.order_type?r.gotoPage("/pages/order/group/detail?order_id="+a.data.order_id,"reLaunch"):r.gotoPage("/pages/order/order-detail?order_id="+a.data.order_id,"redirect")},payTypeFunc(e){this.pay_type=e}}};const t=e._export_sfc(r,[["render",function(a,r,t,d,o,p){return e.e({a:e.t(o.payPrice||""),b:e.f(o.checkedPay,((a,r,t)=>e.e({a:20==a},20==a?{b:r,c:e.n(20==o.pay_type?"item active":"item"),d:e.o((e=>p.payTypeFunc(20)),r)}:{}))),c:o.hasBanlance&&40!=o.order_type},o.hasBanlance&&40!=o.order_type?{d:e.t(o.balance),e:a.getThemeColor(),f:o.balanceType,g:e.o(((...e)=>p.switch2Change&&p.switch2Change(...e)))}:{},{h:e.o(((...e)=>p.submit&&p.submit(...e))),i:a.theme(),j:e.n(a.theme()||"")})}]]);wx.createPage(t);
