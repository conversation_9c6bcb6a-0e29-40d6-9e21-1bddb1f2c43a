"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      /*表单数据对象*/
      formData: {
        mobile: "",
        password: ""
      },
      is_login: 2,
      isRead: false
    };
  },
  methods: {
    xieyi(type) {
      this.gotoPage("/pages/webview/ue?type=" + type);
    },
    /*提交*/
    formSubmit() {
      let self = this;
      if (!self.isRead) {
        common_vendor.index.showToast({
          title: "请先阅读并接受用户协议及隐私政策",
          duration: 2e3,
          icon: "none"
        });
        return;
      }
      let formdata = {
        mobile: self.formData.mobile,
        password: self.formData.password
      };
      let url = "";
      if (!/^1(3|4|5|6|7|8|9)\d{9}$/.test(self.formData.mobile)) {
        common_vendor.index.showToast({
          title: "手机有误,请重填！",
          duration: 2e3,
          icon: "none"
        });
        return;
      }
      if (self.is_login == 2) {
        if (self.formData.password.length < 6) {
          common_vendor.index.showToast({
            title: "密码至少6位数！",
            duration: 2e3,
            icon: "none"
          });
          return;
        }
        url = "user.useropen/register";
      } else {
        if (self.formData.password == "") {
          common_vendor.index.showToast({
            title: "密码不能为空！",
            duration: 2e3,
            icon: "none"
          });
          return;
        }
        url = "user.useropen/phonelogin";
      }
      common_vendor.index.showLoading({
        title: "正在提交",
        mask: true
      });
      self._post(
        url,
        formdata,
        (result) => {
          common_vendor.index.setStorageSync("token", result.data.token);
          common_vendor.index.setStorageSync("user_id", result.data.user_id);
          let url2 = "/" + common_vendor.index.getStorageSync("currentPage");
          let pageOptions = common_vendor.index.getStorageSync("currentPageOptions");
          if (Object.keys(pageOptions).length > 0) {
            url2 += "?";
            for (let i in pageOptions) {
              url2 += i + "=" + pageOptions[i] + "&";
            }
            url2 = url2.substring(0, url2.length - 1);
          }
          self.gotoPage(url2, "redirect");
        },
        false,
        () => {
          common_vendor.index.hideLoading();
        }
      );
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o(($event) => _ctx.gotoPage("/pages/index/index")),
    b: $data.is_login != 0
  }, $data.is_login != 0 ? common_vendor.e({
    c: $data.is_login == 2
  }, $data.is_login == 2 ? {} : {}, {
    d: $data.is_login == 1
  }, $data.is_login == 1 ? {} : {}, {
    e: $data.formData.mobile,
    f: common_vendor.o(($event) => $data.formData.mobile = $event.detail.value),
    g: $data.formData.password,
    h: common_vendor.o(($event) => $data.formData.password = $event.detail.value)
  }) : {}, {
    i: common_vendor.n($data.isRead ? "active agreement" : "agreement"),
    j: common_vendor.o(($event) => $options.xieyi("service")),
    k: common_vendor.o(($event) => $options.xieyi("privacy")),
    l: common_vendor.o(($event) => $data.isRead = !$data.isRead),
    m: $data.is_login == 2
  }, $data.is_login == 2 ? {
    n: common_vendor.o((...args) => $options.formSubmit && $options.formSubmit(...args))
  } : {}, {
    o: $data.is_login == 1
  }, $data.is_login == 1 ? {
    p: common_vendor.o((...args) => $options.formSubmit && $options.formSubmit(...args))
  } : {}, {
    q: $data.is_login == 1
  }, $data.is_login == 1 ? {
    r: common_vendor.o(($event) => $data.is_login = 2)
  } : {}, {
    s: $data.is_login == 2
  }, $data.is_login == 2 ? {
    t: common_vendor.o(($event) => $data.is_login = 1)
  } : {}, {
    v: _ctx.theme(),
    w: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-761a79ba"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/weblogin.js.map
