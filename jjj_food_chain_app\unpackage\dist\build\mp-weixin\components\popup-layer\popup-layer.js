"use strict";const t=require("../../common/vendor.js"),e={name:"popup-layer",model:{prop:"showPop",event:"change"},props:{showPop:{type:Boolean,default:!1},direction:{type:String,default:"top"},autoClose:{type:Boolean,default:!0}},data:()=>({ifshow:!1,translateValue:-150,timer:null,iftoggle:!1}),computed:{_translate(){return{top:`transform:translateY(${-this.translateValue}%)`,bottom:`transform:translateY(${this.translateValue}%)`,left:`transform:translateX(${-this.translateValue}%)`,right:`transform:translateX(${this.translateValue}%)`}[this.direction]},_location(){return{top:"bottom:0;width:100%;",bottom:"top:0px;width:100%;",left:"right:0px;height:100%;",right:"left:0px;height:100%;"}[this.direction]+this._translate}},mounted(){this.showPop&&this.show()},watch:{showPop(t){t?this.show():this.close()}},methods:{stopMove(t){console.log(t)},show(t){this.ifshow=!0,setTimeout((()=>{this.translateValue=0}),100),setTimeout((()=>{this.iftoggle=!0}),300)},close(){null===this.timer&&this.iftoggle&&(this.translateValue=-150,this.timer=setTimeout((()=>{this.ifshow=!1,this.timer=null,this.iftoggle=!1,this.$emit("closeCallBack",null),this.$emit("change",!1)}),300))},ableClose(){this.autoClose&&this.close()},stopEvent(t){},doSome(){}}};const o=t._export_sfc(e,[["render",function(e,o,s,i,a,l){return t.e({a:a.ifshow},a.ifshow?{b:t.o(((...t)=>l.ableClose&&l.ableClose(...t))),c:t.o((()=>{}))}:{},{d:t.o(((...t)=>l.stopEvent&&l.stopEvent(...t))),e:t.s(l._location)})}]]);wx.createComponent(o);
