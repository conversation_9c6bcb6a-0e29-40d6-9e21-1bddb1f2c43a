<view catchtouchmove="{{s}}"><view style="{{'width:' + l + ';' + ('padding:' + m) + ';' + ('border-radius:' + n)}}" class="{{['modal-box', o, p]}}"><view wx:if="{{a}}"><slot></slot></view><view wx:else><view wx:if="{{b}}" class="modal-title">{{c}}</view><view class="{{['modal-content', d]}}" style="{{'color:' + e + ';' + ('font-size:' + f)}}"><slot></slot></view><view class="{{['modalBtn-box', k]}}"><block wx:for="{{g}}" wx:for-item="item" wx:key="g"><button class="{{['modal-btn', item.b, h, i, j, item.c]}}" hover-class="{{item.d}}" data-index="{{item.e}}" bindtap="{{item.f}}">{{item.a}}</button></block></view></view></view><view class="{{['modal-mask', q]}}" bindtap="{{r}}"></view></view>