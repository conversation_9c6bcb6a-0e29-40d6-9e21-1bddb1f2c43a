"use strict";const t=require("../../../common/vendor.js"),e=require("../../../common/assets.js"),i={components:{modal:()=>"../../../components/modal/modal.js",popupLayer:()=>"../../../components/popup-layer/popup-layer.js",prospec:()=>"./popup/spec.js"},data:()=>({loadingClock:!1,isDetail:!1,isLoading:!0,supplier:{name:"",business_status:0},shop_supplier_id:0,loading:!0,currentCateId:0,cateScrollTop:0,menuScrollIntoView:"",sizeCalcState:!1,productList:[],productModel:{},clock:!1,total_price:0,cart_total_num:0,cart_product_id:0,cartPopupVisible:!1,cart_list:[],cart_type:0,phoneHeight:0,scrollviewHigh:0,addclock:!1,address_detail:"",longitude:0,latitude:0,address_id:0,bag_type:1,total_bag_price:0,orderType:"",dinner_type:20,delivery_set:[],isFirst:!0,min_money:0,min_money_diff:0,line_price:0,scrollLast:0}),onLoad(e){let i=this;i.orderType=e.orderType,"takeout"==i.orderType?i.dinner_type=10:i.dinner_type=20,i.shop_supplier_id=t.index.getStorageSync("selectedId")?t.index.getStorageSync("selectedId"):0},onShow(){this.isDetail=!1,this.init()},methods:{stopClick:()=>!1,getUserInfo(){let t=this;t.loading=!0,t.isLogin=!1,t._get("index/userInfo",{},(function(e){e.data&&e.data.user&&(t.isLogin=!0),10==t.dinner_type&&e.data&&e.data.user&&e.data.user.address&&e.data.user.address_id?(t.latitude=e.data.user.address.latitude,t.longitude=e.data.user.address.longitude,t.address_detail=e.data.user.address.detail,t.getCategory(!0)):t.getcityData()}))},init(){this.addclock=!1,this.clock=!1,this.loadingClock=!1,this.loading=!0,this.isLoading=!0,this.productList=[],this.sizeCalcState=!1,this.getUserInfo()},scrollInit(){let e=this;e.scrollviewHigh||t.index.getSystemInfo({success(i){e.phoneHeight=i.windowHeight,t.index.createSelectorQuery().select(".nav").boundingClientRect((t=>{let i=e.phoneHeight-t.height;e.scrollviewHigh=i})).exec()}})},getCategory(e){let i=this;this.sizeCalcState=!1;let o="takeout"==i.orderType?10:20;i._get("product.category/index",{type:0,shop_supplier_id:i.shop_supplier_id||0,longitude:i.longitude,latitude:i.latitude,delivery:o,order_type:0,table_id:0,cart_type:i.cart_type||0},(function(o){if(i.supplier=o.data.supplier,i.min_money=(1*o.data.supplier.min_money).toFixed(2),i.productList=o.data.list,i.currentCateId||(i.currentCateId=i.productList[0].category_id),i.delivery_set=o.data.supplier.delivery_set,i.isFirst){if(i.orderType){let e="takeout"==i.orderType?"10":"20";if(-1==i.delivery_set.indexOf(e)){let o="";"20"==e?(o="当前门店不支持自取，已为您切换为配送",i.orderType="takeout"):(o="当前门店不支持配送，已为您切换为自取",i.orderType="takein"),t.index.showModal({title:"提示",content:o,showCancel:!1})}}i.isFirst=!1}i.address_id=o.data.address_id,i.bag_type=o.data.supplier.bag_type,i.loading=!1,i.isLoading=!1,i.$nextTick((function(){i.scrollInit()})),i.isLogin&&e&&i.getCart()}),(function(t){i.showError(t.msg,(()=>{i.gotoPage("/pages/index/index")}))}))},reCart(t){let e=this;e.loadingClock=!1,e.cart_total_num=t.data.cartInfo.cart_total_num,e.line_price=t.data.cartInfo.total_line_money,e.total_price=t.data.cartInfo.total_pay_price,e.total_bag_price=t.data.cartInfo.total_bag_price,e.min_money_diff=t.data.cartInfo.min_money_diff,e.cart_list&&""!=e.cart_list||(e.cartPopupVisible=!1)},addCart(e){let i=this;if(20==e.spec_types)return void i.gotoDetail(e);if(i.addclock)return;if(0!=e.limit_num&&e.limit_num<=e.cart_num)return void t.index.showToast({icon:"none",title:"超过限购数量"});if(e.product_stock<=0||e.product_stock<=e.cart_num)return void t.index.showToast({icon:"none",title:"没有更多库存了"});let o={product_id:e.product_id,product_num:1,product_sku_id:e.sku[0].product_sku_id,attr:"",feed:"",describe:"",price:e.sku[0].product_price,bag_price:e.sku[0].bag_price,shop_supplier_id:e.supplier.shop_supplier_id,cart_type:0,dinner_type:i.dinner_type,product_price:e.sku[0].line_price,delivery:"takeout"==i.orderType?10:20};i.addclock=!0,t.index.showLoading({title:"",mask:!0}),i._post("order.cart/add",o,(function(e){t.index.hideLoading(),i.reCart(e),i.addclock=!1,i.getCategory(!1)}),(function(e){t.index.hideLoading(),i.addclock=!1}))},reduceFunc(e){let i=this;if(i.addclock||e.cart_num<=0)return;if(20==e.spec_types)return void i.openCartPopup(e.product_id);let o=e.product_id;i.addclock=!0,t.index.showLoading({title:"",mask:!0}),i._post("order.cart/productSub",{product_id:o,type:"down",cart_type:0,dinner_type:i.dinner_type,shop_supplier_id:i.shop_supplier_id,delivery:"takeout"==i.orderType?10:20},(function(e){t.index.hideLoading(),i.reCart(e),i.addclock=!1,i.getCategory(!1)}),(function(){t.index.hideLoading(),i.addclock=!1}))},getCartLoading(){console.log("getCartLoading");let e=this;e._get("order.cart/lists",{shop_supplier_id:e.shop_supplier_id,cart_type:0,delivery:"takeout"==e.orderType?10:20,product_id:e.cart_product_id},(function(i){e.cart_list=i.data.productList,e.isLoading=!0,e.reCart(i),t.index.hideLoading()}),(e=>{t.index.hideLoading()}))},getCart(){let e=this;if(e.isLogin)return new Promise(((i,o)=>{t.index.showLoading({title:"",mask:!0}),e._get("order.cart/lists",{shop_supplier_id:e.shop_supplier_id,cart_type:0,delivery:"takeout"==e.orderType?10:20,product_id:e.cart_product_id},(function(o){t.index.hideLoading(),e.isLoading=!0,e.reCart(o),e.cart_list=o.data.productList,i(!0)}),(e=>{t.index.hideLoading()}))}))},cartAdd(e){let i=this;if(i.addclock)return;i.addclock=!0;let o=e.product_id;t.index.showLoading({title:"",mask:!0}),i._post("order.cart/sub",{product_id:o,total_num:1,cart_id:e.cart_id,type:"up",cart_type:0,dinner_type:i.dinner_type,shop_supplier_id:i.shop_supplier_id,delivery:"takeout"==i.orderType?10:20},(function(t){i.addclock=!1,i.getCategory(!1),i.getCartLoading()}),(function(){t.index.hideLoading(),i.addclock=!1}))},cartReduce(e){let i=this;if(i.addclock)return;i.addclock=!0;let o=e.product_id;t.index.showLoading({title:"",mask:!0}),i._post("order.cart/sub",{product_id:o,total_num:1,cart_id:e.cart_id,type:"down",cart_type:0,dinner_type:i.dinner_type,shop_supplier_id:i.shop_supplier_id,delivery:"takeout"==i.orderType?10:20},(function(t){i.addclock=!1,i.getCategory(!1),i.getCartLoading()}),(function(){t.index.hideLoading(),i.addclock=!1}))},takout(){"takeout"!=this.orderType&&(this.orderType="takeout",this.dinner_type=10,this.init())},takein(){"takein"!=this.orderType&&(this.orderType="takein",this.dinner_type=20,this.init())},handleMenuTap(t){let e=this;e.sizeCalcState||e.calcSize(),e.currentCateId=t,e.$nextTick((()=>{e.cateScrollTop=e.productList.find((e=>e.category_id==t)).top,e.cateScrollTop||0==e.cateScrollTop||setTimeout((function(){e.handleMenuTap(t)}),300)}))},handleGoodsScroll({detail:t}){this.sizeCalcState||this.calcSize(),this.scrollLast=t.scrollTop;const{scrollTop:e}=t;let i=this.productList.filter((t=>t.top-5<=e)).reverse();i.length>0&&(this.currentCateId=i[0].category_id)},calcSize(){let e=0;this.productList.forEach((i=>{t.index.createSelectorQuery().select(`#cate-${i.category_id}`).fields({size:!0},(t=>{i.top=e,null!=t&&(e+=t.height),i.bottom=e})).exec()})),this.sizeCalcState=!0},closeGoodDetailModal(t,e){this.isDetail=!1,this.clock=!1,t&&(this.reCart(e),this.getCategory(!1))},async openCartPopup(t){this.cart_total_num&&(this.cartPopupVisible||(this.cart_list=[],this.cart_product_id=t,await this.getCart()),this.cartPopupVisible=!this.cartPopupVisible)},closeCallBack(){this.cart_product_id=0,this.cart_list=[],this.cartPopupVisible=!1},handleCartClear(){let e=this;t.index.showModal({title:"提示",content:"确定清空购物车么",success(t){t.confirm?e.clearCart():t.cancel&&console.log("用户点击取消")}})},clearCart(){let t=this;t._post("order.cart/delete",{shop_supplier_id:t.shop_supplier_id,cart_type:0},(function(e){t.cartPopupVisible=!1,t.cart_list=[],t.init()}))},toPay(){let e=this;0!=e.address_id||"takeout"!=e.orderType?e.loadingClock||(e.loadingClock=!0,t.index.showLoading({title:"加载中",mask:!0}),e._get("order.cart/lists",{shop_supplier_id:e.shop_supplier_id,cart_type:0,delivery:"takeout"==e.orderType?10:20},(function(i){t.index.hideLoading(),e.reCart(i),e.cart_list=i.data.productList;let o=[];if(e.cart_list.forEach((t=>{o.push(t.cart_id)})),0==o.length)return t.index.showToast({title:"请选择商品",icon:"none"}),!1;let r="takeout"==e.orderType?10:20;t.index.navigateTo({url:"/pages/order/confirm-order?order_type=cart&cart_ids="+o.join(",")+"&delivery="+r+"&shop_supplier_id="+e.shop_supplier_id+"&cart_type=0&dinner_type="+r}),e.loadingClock=!1}),(()=>{t.index.hideLoading(),e.loadingClock=!1}))):t.index.showModal({title:"提示",content:"您还没选择收货地址,请先选择收货地址",success(){e.gotoPage("/pages/user/address/storeaddress?shop_supplier_id="+e.shop_supplier_id)}})},gotoDetail(t){let e=this,i="takeout"==this.orderType?10:20;e.productModel={product_id:t.product_id||0,delivery:i,bag_type:e.bag_type||0,dinner_type:e.dinner_type||0,cart_type:e.cart_type||0,table_id:0,shop_supplier_id:e.shop_supplier_id||0},e.isDetail=!0},getcityData(){this.getLocation()},getLocation(e){let i=this;t.index.getLocation({type:"wgs84",success(t){console.log("getLocation"),console.log(t),i.longitude=t.longitude,i.latitude=t.latitude,i.getCategory(!0)},fail(e){i.longitude=0,i.latitude=0,t.index.showToast({title:"获取定位失败，请点击右下角按钮打开定位权限",duration:2e3,icon:"none"}),i.getCategory(!0)}})},getWxLocation(t,e){}}};if(!Array){(t.resolveComponent("popup-layer")+t.resolveComponent("prospec"))()}Math;const o=t._export_sfc(i,[["render",function(i,o,r,d,a,c){return t.e({a:"takein"==a.orderType},"takein"==a.orderType?{b:t.t(a.supplier.name),c:t.t(a.supplier.distance)}:{},{d:"takeout"==a.orderType},"takeout"==a.orderType?t.e({e:t.t(a.supplier.name),f:a.address_detail},a.address_detail?{g:t.t(a.address_detail||"请选择收货地址"),h:t.o((t=>i.gotoPage("/pages/user/address/storeaddress?shop_supplier_id="+a.shop_supplier_id)))}:{i:t.o((t=>i.gotoPage("/pages/user/address/storeaddress?shop_supplier_id="+a.shop_supplier_id)))}):{},{j:t.f(a.delivery_set,((e,i,o)=>t.e({a:"10"==e},"10"==e?{b:"takeout"==a.orderType?1:"",c:t.o(((...t)=>c.takout&&c.takout(...t)),"a-"+i)}:{},{d:"a-"+i}))),k:t.f(a.delivery_set,((e,i,o)=>t.e({a:"20"==e},"20"==e?{b:"takein"==a.orderType?1:"",c:t.o(((...t)=>c.takein&&c.takein(...t)),"b-"+i)}:{},{d:"b-"+i}))),l:t.f(a.productList,((e,i,o)=>t.e({a:0!=e.products.length},0!=e.products.length?t.e({b:e.images},e.images?{c:e.images.file_path}:{},{d:t.t(e.name),e:e.product_num},e.product_num?{f:t.t(e.product_num)}:{},{g:`menu-${e.category_id}`,h:e.category_id===a.currentCateId?1:"",i:t.o((t=>c.handleMenuTap(e.category_id)),i)}):{},{j:i}))),m:t.s("height:"+a.scrollviewHigh+"px;"),n:a.menuScrollIntoView,o:t.f(a.productList,((o,r,d)=>t.e({a:0!=o.products.length},0!=o.products.length?{b:t.t(o.name),c:t.f(o.products,((o,r,d)=>t.e({a:o.product_stock<=0},(o.product_stock,{}),{b:o.product_image,c:t.t(o.product_name),d:t.t(o.selling_point),e:t.t(o.product_price),f:1*o.product_price!=1*o.line_price},1*o.product_price!=1*o.line_price?{g:t.t(1*o.line_price)}:{},{h:0!=o.cart_num},0!=o.cart_num?{i:t.o((t=>c.reduceFunc(o)),r),j:"/static/icon/cart/reduce-"+i.theme()+".png"}:{},{k:0!=o.cart_num},0!=o.cart_num?{l:t.t(o.cart_num)}:{},{m:o.product_stock>0},o.product_stock>0?{n:t.o((t=>c.addCart(o)),r),o:"/static/icon/cart/add-"+i.theme()+".png"}:{},{p:o.product_stock<=0},o.product_stock<=0?{q:e._imports_0$3}:{},{r:t.o((t=>c.gotoDetail(o)),r),s:r}))),d:`cate-${o.category_id}`}:{},{e:r}))),p:t.s("padding-bottom:"+(2*a.scrollviewHigh-238)+"rpx;"),q:t.s("height:"+a.scrollviewHigh+"px;"),r:a.cateScrollTop,s:t.o(((...t)=>c.handleGoodsScroll&&c.handleGoodsScroll(...t))),t:a.cart_total_num},a.cart_total_num?{v:t.t(a.cart_total_num)}:{},{w:a.cart_total_num},a.cart_total_num?t.e({x:t.t(a.total_price),y:t.t(a.line_price),z:0!=a.total_bag_price},0!=a.total_bag_price?{A:t.t(a.total_bag_price)}:{}):{},{B:a.min_money_diff<=0||"takeout"!=a.orderType},a.min_money_diff<=0||"takeout"!=a.orderType?{C:!a.cart_total_num,D:t.o(((...t)=>c.toPay&&c.toPay(...t)))}:{},{E:a.min_money_diff>0&&0==a.total_price&&"takeout"==a.orderType},a.min_money_diff>0&&0==a.total_price&&"takeout"==a.orderType?{F:t.t("￥"+a.min_money+"起送")}:{},{G:a.min_money_diff>0&&0!=a.total_price&&"takeout"==a.orderType},a.min_money_diff>0&&0!=a.total_price&&"takeout"==a.orderType?{H:t.t("还差￥"+a.min_money_diff+"起送")}:{},{I:t.o((t=>c.openCartPopup(0))),J:a.cart_total_num>0},a.cart_total_num>0?t.e({K:a.total_bag_price},a.total_bag_price?{L:t.t(a.total_bag_price)}:{},{M:t.o(((...t)=>c.handleCartClear&&c.handleCartClear(...t))),N:t.f(a.cart_list,((e,o,r)=>t.e({a:e.product_num>0},e.product_num>0?t.e({b:e.image.file_path,c:t.t(e.product.product_name),d:t.t(e.describe),e:t.t(e.price),f:1!=a.bag_type},1!=a.bag_type?{g:t.t(e.bag_price)}:{},{h:t.o((t=>c.cartReduce(e)),o),i:"/static/icon/cart/reduce-"+i.theme()+".png",j:t.t(e.product_num),k:t.o((t=>c.cartAdd(e)),o),l:"/static/icon/cart/add-"+i.theme()+".png"}):{},{m:o}))),O:t.o(c.closeCallBack),P:t.p({direction:"top","show-pop":a.cartPopupVisible})}):{},{Q:a.isDetail},a.isDetail?{R:t.o(c.closeGoodDetailModal),S:t.p({productModel:a.productModel})}:{},{T:a.loading},a.loading?{U:e._imports_1$1}:{},{V:i.theme(),W:t.n(i.theme()||"")})}]]);wx.createPage(o);
