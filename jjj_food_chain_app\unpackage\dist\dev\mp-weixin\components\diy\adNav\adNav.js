"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {};
  },
  props: ["itemData"],
  created() {
  },
  methods: {
    /*跳转页面*/
    gotoDetail(path) {
      let self = this;
      common_vendor.index.__f__("log", "at components/diy/adNav/adNav.vue:46", path);
      if (path.startsWith("scanQrcode")) {
        self[path]();
      } else {
        self.gotoPage(path);
      }
    },
    /*扫一扫核销*/
    scanQrcode: function() {
      this.$emit("scanQrcode");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.itemData.data, (navBar, index, i0) => {
      return {
        a: navBar.imgUrl,
        b: common_vendor.t(navBar.title),
        c: navBar.titlecolor,
        d: common_vendor.t(navBar.text),
        e: navBar.textcolor,
        f: index,
        g: common_vendor.o(($event) => $options.gotoDetail(navBar.linkUrl), index)
      };
    }),
    b: $props.itemData.style.topRadio * 2 + "rpx ",
    c: $props.itemData.style.topRadio * 2 + "rpx ",
    d: $props.itemData.style.bottomRadio * 2 + "rpx ",
    e: $props.itemData.style.bottomRadio * 2 + "rpx",
    f: "linear-gradient(to bottom, " + ($props.itemData.style.background1 || "#fff") + ", " + ($props.itemData.style.background2 || "#fff") + ")",
    g: $props.itemData.style.bgcolor,
    h: $props.itemData.style.paddingLeft * 2 + "rpx ",
    i: $props.itemData.style.paddingLeft * 2 + "rpx ",
    j: $props.itemData.style.paddingTop * 2 + "rpx ",
    k: $props.itemData.style.paddingBottom * 2 + "rpx"
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c37442b0"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/components/diy/adNav/adNav.js.map
