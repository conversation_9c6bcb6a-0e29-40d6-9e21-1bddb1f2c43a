"use strict";const t=require("../../../common/vendor.js"),e={data:()=>({listData:[],isLoading:!0,storeList:[],longitude:"",latitude:"",selectedId:-1}),onLoad(t){this.selectedId=t.store_id,this.getData()},methods:{onAuthorize(){let e=this;t.index.openSetting({success(t){t.authSetting["scope.userLocation"]&&(console.log("授权成功"),e.isAuthor=!0,setTimeout((()=>{e.getLocation((t=>{}))}),1e3))}})},getLocation(e){let i=this;t.index.getLocation({type:"wgs84",success(t){i.longitude=t.longitude,i.latitude=t.latitude,i.getData()},fail(){t.index.showToast({title:"获取定位失败，请点击右下角按钮打开定位权限",duration:2e3}),i.isAuthor=!1}})},getData(){let t=this;t.isLoading=!0,t._get("store.store/lists",{longitude:t.longitude,latitude:t.latitude},(function(e){t.isLoading=!1,t.storeList=e.data.list}))},onSelectedStore(e){if(this.selectedId=e,getCurrentPages().length<2)return!1;this.$fire.fire("selectStoreId",e),t.index.navigateBack()}}};const i=t._export_sfc(e,[["render",function(e,i,o,s,n,d){return t.e({a:t.f(n.storeList,((e,i,o)=>t.e({a:t.t(e.store_name),b:t.t(e.phone),c:t.t(e.region.province),d:t.t(e.region.city),e:t.t(e.region.region),f:t.t(e.address),g:t.t(e.distance_unit),h:e.store_id==n.selectedId},(e.store_id,n.selectedId,{}),{i:t.o((t=>d.onSelectedStore(e)),i),j:i}))),b:!n.isLoading&&!n.storeList.length},(n.isLoading||n.storeList.length,{}))}]]);wx.createPage(i);
