/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.pay-success {
  padding: 24rpx 24rpx 48rpx 24rpx;
}
.pay-success .success-icon {
  display: flex;
  padding: 48rpx 48rpx 68rpx 48rpx;
  background-color: #FFFFFF;
  border-radius: 6rpx;
  position: relative;
}
.pay-success .success-icon .icon-kongxinduigou {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 36rpx;
  color: #333333;
  margin-right: 10rpx;
}
.pay-success .success-icon .name {
  font-size: 38rpx;
}
.pay-success .order-info {
  background: #FFFFFF;
}
.pay-success .success-btns {
  margin-top: 50rpx;
  padding: 30rpx;
}
.pay-success .success-btns button.btn1 {
  font-size: 30rpx;
  color: #fff;
  width: 662rpx;
  height: 92rpx;
  border-radius: 200rpx;
  position: fixed;
  left: 0;
  right: 0;
  margin: auto;
  bottom: 48rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
[data-theme=theme0] .pay-success .success-btns button.btn1 {
  background-color: #fee238 !important;
}
[data-theme=theme1] .pay-success .success-btns button.btn1 {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .pay-success .success-btns button.btn1 {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .pay-success .success-btns button.btn1 {
  background-color: #00a348 !important;
}
[data-theme=theme4] .pay-success .success-btns button.btn1 {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .pay-success .success-btns button.btn1 {
  background-color: #b99970 !important;
}
[data-theme=theme6] .pay-success .success-btns button.btn1 {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .pay-success .success-btns button.btn1 {
  color: #111111 !important;
}
[data-theme=theme1] .pay-success .success-btns button.btn1 {
  color: #ffffff !important;
}
[data-theme=theme2] .pay-success .success-btns button.btn1 {
  color: #ffffff !important;
}
[data-theme=theme3] .pay-success .success-btns button.btn1 {
  color: #ffffff !important;
}
[data-theme=theme4] .pay-success .success-btns button.btn1 {
  color: #ffffff !important;
}
[data-theme=theme5] .pay-success .success-btns button.btn1 {
  color: #fff !important;
}
[data-theme=theme6] .pay-success .success-btns button.btn1 {
  color: #ffffff !important;
}
.callBox {
  width: 564rpx;
  height: 152rpx;
  border-radius: 4rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  padding: 32rpx 0;
  box-sizing: border-box;
  margin-top: 20rpx;
}