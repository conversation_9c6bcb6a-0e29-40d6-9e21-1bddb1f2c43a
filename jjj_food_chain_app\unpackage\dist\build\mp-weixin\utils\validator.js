"use strict";const e=require("../common/vendor.js"),t=require("../common/utils.js"),n=require("../env/config.js");exports.validator=function(i){i.config.globalProperties.getAppId=function(){return e.index.getStorageSync("app_id")||n.config.app_id||10001},i.config.globalProperties.compareVersion=function(e,t){e=e.split("."),t=t.split(".");const n=Math.max(e.length,t.length);for(;e.length<n;)e.push("0");for(;t.length<n;)t.push("0");for(let i=0;i<n;i++){const n=parseInt(e[i]),r=parseInt(t[i]);if(n>r)return 1;if(n<r)return-1}return 0},i.config.globalProperties.getVisitcode=function(){let t=e.index.getStorageSync("visitcode");return t||(t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})),t=t.replace(/-/g,""),e.index.setStorageSync("visitcode",t)),t},i.config.globalProperties.subMessage=function(t,n){const i=e.wx$1.getSystemInfoSync().SDKVersion;t&&0!=t.length&&this.compareVersion(i,"2.8.2")>=0?(e.index.hideLoading(),e.wx$1.requestSubscribeMessage({tmplIds:t,success(e){},fail(e){},complete(e){n()}})):n()},i.config.globalProperties.showError=function(t,n){e.index.showModal({title:"友情提示",content:t,showCancel:!1,success:function(e){n&&n()}})},i.config.globalProperties.showSuccess=function(t,n){e.index.showModal({title:"友情提示",content:t,showCancel:!1,success:function(e){n&&n()}})},i.config.globalProperties.getShareUrlParams=function(e){return t.utils.urlEncode(Object.assign({referee_id:this.getUserId(),app_id:this.getAppId()},e))},i.config.globalProperties.getUserId=function(){return e.index.getStorageSync("user_id")},i.config.globalProperties.ios=function(){const e=navigator.userAgent.toLowerCase();return!(e.indexOf("like mac os x")<0||"micromessenger"!=e.match(/MicroMessenger/i))},i.config.globalProperties.isWeixin=function(){return"micromessenger"==navigator.userAgent.toLowerCase().match(/MicroMessenger/i)},i.config.globalProperties.getPlatform=function(e){return"wx"},i.config.globalProperties.getNavHeight=function(){return 0==this.topBarHeight()?"":"height:"+this.topBarHeight()+"px;"},i.config.globalProperties.topBarTop=function(){return e.index.getMenuButtonBoundingClientRect().top},i.config.globalProperties.topBarHeight=function(){return e.index.getMenuButtonBoundingClientRect().height},i.config.globalProperties.subPrice=function(e,t){let n=String(e);if(1==t)return n.substring(0,n.indexOf("."));if(2==t){let e=n.indexOf(".");return n.slice(e+1,e+3)}},i.config.globalProperties.convertTwo=function(e){let t="";return t=e<10?"0"+e:e,t},i.config.globalProperties.yulan=function(t,n){let i=[];Array.isArray(t)?t[0].file_path?t.forEach(((e,t)=>{i.push(e.file_path)})):i=t:i=[t];let r=1*n;e.index.previewImage({urls:i,current:r})},i.config.globalProperties.subpoint=function(e,t){let n=String(e);if(1==t)return n.substring(0,n.indexOf("."));if(2==t){let e=n.indexOf(".");return n.slice(e+1,e+3)}}};
