<view class="mpvue-picker"><view class="{{[a && 'pickerMask']}}" bindtap="{{b}}" catchtouchmove="true"></view><view class="{{['mpvue-picker-content', k && 'mpvue-picker-view-show']}}"><view class="mpvue-picker__hd" catchtouchmove="true"><view class="mpvue-picker__action" bindtap="{{c}}">取消</view><view class="mpvue-picker__action" style="{{'color:' + d}}" bindtap="{{e}}">确定</view></view><block wx:if="{{r0}}"><picker-view indicator-style="height: 40px;" class="mpvue-picker-view" value="{{i}}" bindchange="{{j}}"><block u-s="{{['d']}}"><picker-view-column><view wx:for="{{f}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}</view></picker-view-column><picker-view-column><view wx:for="{{g}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}</view></picker-view-column><picker-view-column><view wx:for="{{h}}" wx:for-item="item" wx:key="b" class="picker-item">{{item.a}}</view></picker-view-column></block></picker-view></block></view></view>