<view data-theme="{{V}}" class="{{['container', W]}}"><view class="main"><view class="nav"><view class="header"><view wx:if="{{a}}" class="left"><view class="store-name"><text class="fb">{{b}}</text></view><view class="gray9 f24"><text class="icon iconfont icon-diliweizhi"></text>距离您{{c}}</view></view><view wx:if="{{d}}" class="left"><view class="store-name"><text class="fb">{{e}}</text></view><view wx:if="{{f}}" class="d-s-c f24" style="margin-right:90rpx"><view class="text-ellipsis gray9 d-s-c"><text class="icon iconfont icon-dizhi"></text> 配送地址：{{g}}</view><view class="theme-notice f-s-0" bindtap="{{h}}"> 更换地址 </view></view><view wx:else class="d-s-c f24" style="margin-right:90rpx" bindtap="{{i}}"><view class="text-ellipsis gray9 d-s-c"><text class="icon iconfont icon-dizhi"></text> 请选择收货地址 </view></view></view><view class="dinner-right"><block wx:for="{{j}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="{{['dinner_type', item.b && 'active']}}" bindtap="{{item.c}}"><text>配送</text></view></block><block wx:for="{{k}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="{{['dinner_type', item.b && 'active']}}" bindtap="{{item.c}}"><text>自取</text></view></block></view></view></view><view class="content"><scroll-view class="menus" style="{{m}}" scroll-into-view="{{n}}" scroll-y><view class="category-wrapper"><block wx:for="{{l}}" wx:for-item="item" wx:key="j"><view wx:if="{{item.a}}" id="{{item.g}}" class="{{['menu', 'd-s-c', 'pr', item.h && 'current']}}" bindtap="{{item.i}}"><image wx:if="{{item.b}}" class="f-s-0 menu-imgs" src="{{item.c}}" mode="aspectFill"></image><view class="text-ellipsis">{{item.d}}</view><view wx:if="{{item.e}}" class="menu-cartNum">{{item.f}}</view></view></block><view class="menu-bottom"></view></view></scroll-view><scroll-view class="goods pr" style="{{q}}" scroll-y scroll-top="{{r}}" bindscroll="{{s}}"><view class="goods-wrapper"><view class="list" style="{{p}}"><block wx:for="{{o}}" wx:for-item="item" wx:key="e"><view wx:if="{{item.a}}" class="category" id="{{item.d}}"><view class="title"><text>{{item.b}}</text></view><view class="goods-items"><view wx:for="{{item.c}}" wx:for-item="good" wx:key="s" class="good" bindtap="{{good.r}}"><view class="image-boxs"><view wx:if="{{good.a}}" class="sallsell-out"><view class="sallsell-out-btn">当前售罄</view></view><image src="{{good.b}}" class="image"></image></view><view class="product-info"><view class="ww100"><view class="name text-ellipsis">{{good.c}}</view><view class="tips text-ellipsis">{{good.d}}</view></view><view class="price_and_action"><view><text class="f24 theme-price">￥</text><text class="f34 theme-price">{{good.e}}</text><text wx:if="{{good.f}}" class="linprice">￥{{good.g}}</text></view><view class="btn-group"><image wx:if="{{good.h}}" catchtap="{{good.i}}" class="add-image" src="{{good.j}}" mode=""></image><view wx:if="{{good.k}}" class="number">{{good.l}}</view><image wx:if="{{good.m}}" catchtap="{{good.n}}" class="add-image" src="{{good.o}}" mode=""></image><image wx:if="{{good.p}}" class="add-image" src="{{good.q}}" mode=""></image></view></view></view></view></view></view></block></view></view></scroll-view></view><view class="cart-box" bindtap="{{I}}"><view class="mark"><view class="icon iconfont icon-gouwudai cart-view"></view><view wx:if="{{t}}" class="tag">{{v}}</view></view><view wx:if="{{w}}" class="price"><view><text class="f22">￥</text><text class="f36">{{x}}</text><text class="gray9 f22 text-d-line fn ml10">￥{{y}}</text></view><view wx:if="{{z}}" class="gray9 f22"><text class="">包含包装费￥</text> {{A}}</view></view><view wx:else class="flex-1 f32 white">未选购商品</view><button wx:if="{{B}}" class="pay-btn" disabled="{{C}}" catchtap="{{D}}">去结算</button><button wx:if="{{E}}" class="btn-gray" disabled>{{F}}</button><button wx:if="{{G}}" class="btn-gray" disabled>{{H}}</button></view></view><popup-layer wx:if="{{J}}" u-s="{{['content']}}" class="cart-popup" bindcloseCallBack="{{O}}" u-i="3fcecbb1-0" bind:__l="__l" u-p="{{P}}"><view class="cart-popup pr" slot="content"><view class="top d-b-c"><view class="f30 gray3 d-s-c"> 购物车 <view wx:if="{{K}}" class="f22 gray3">（打包费 <text class="theme-price">￥{{L}}</text>）</view></view><view bindtap="{{M}}" class="d-c-c"><text class="icon iconfont icon-shanchu1"></text>清空购物车</view></view><scroll-view class="cart-list" scroll-y><view class="wrapper"><block wx:for="{{N}}" wx:for-item="item" wx:key="m"><view wx:if="{{item.a}}" class="item"><view class="d-s-c ww100"><view class="cart-image"><image style="" src="{{item.b}}" mode="aspectFill"></image></view><view class="left"><view><view class="name text-ellipsis">{{item.c}}</view><view class="gray9">{{item.d}}</view></view><view class="center"><text>￥</text><text class="f34">{{item.e}}</text><text wx:if="{{item.f}}" class="f24 gray9 ml10">包装费￥{{item.g}}</text></view></view></view><view class="right"><image bindtap="{{item.h}}" class="btn-image" src="{{item.i}}" mode=""></image><view class="number">{{item.j}}</view><image bindtap="{{item.k}}" class="btn-image" src="{{item.l}}" mode=""></image></view></view></block></view></scroll-view></view></popup-layer><prospec wx:if="{{Q}}" bindclose="{{R}}" u-i="3fcecbb1-1" bind:__l="__l" u-p="{{S}}"></prospec><view wx:if="{{T}}" class="loading"><image src="{{U}}"></image></view></view>