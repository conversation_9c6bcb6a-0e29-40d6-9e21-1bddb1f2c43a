<view data-theme="{{r}}" class="{{['address-form', s]}}"><form bindsubmit="{{j}}" bindreset="{{k}}"><view class="bg-white p-0-30 f30"><view class="d-s-c border-b-d9"><text class="key-name">收货人</text><input class="ml20 flex-1 f32 p-30-0" name="name" type="text" placeholder-class="grary9" placeholder="请输入收货人姓名" value="{{a}}" bindinput="{{b}}"/></view><view class="d-s-c border-b-d9"><text class="key-name">联系方式</text><input class="ml20 flex-1 f32 p-30-0" name="phone" type="text" placeholder-class="grary9" placeholder="请输入收货人手机号" value="{{c}}" bindinput="{{d}}"/></view><view class="d-s-c border-b-d9" bindtap="{{g}}"><text class="key-name">详细地址</text><view class="input-box flex-1"><input class="ml20 f32 flex-1 p-30-0" name="detail" type="text" placeholder-class="grary9" placeholder="请选择地址" disabled value="{{e}}" bindinput="{{f}}"/></view></view><view class="d-s-c border-b-d9"><text class="key-name">门牌号</text><block wx:if="{{r0}}"><textarea class="ml20 flex-1 p-30-0 lh150" name="address" auto-height="{{true}}" placeholder-class="grary9" placeholder="请输入街道小区楼牌号等" value="{{h}}" bindinput="{{i}}"></textarea></block></view></view><view class="p30"><button type="primary" form-type="submit" class="theme-btn f32 mt60 addBtn">保存</button></view></form><mpvue-city-picker wx:if="{{n}}" class="r" u-r="mpvueCityPicker" bindonConfirm="{{m}}" u-i="bb945fce-0" bind:__l="__l" u-p="{{n}}"></mpvue-city-picker><web-view wx:if="{{o}}" src="{{p}}" bindonPostMessage="{{q}}"></web-view></view>