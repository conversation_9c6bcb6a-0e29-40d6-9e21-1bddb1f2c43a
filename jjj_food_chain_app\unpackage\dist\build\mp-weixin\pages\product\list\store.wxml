<view data-theme="{{K}}" class="{{['container', L]}}"><view class="main"><view class="nav"><view class="{{['header', e && 'tableHead']}}"><view class="left"><view class="store-name"><text class="fb">{{a}}</text></view></view><view wx:if="{{b}}" class="dinner-right"><block wx:for="{{c}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="{{['dinner_type', item.b && 'active']}}" bindtap="{{item.c}}"><text>打包</text></view></block><block wx:for="{{d}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="{{['dinner_type', item.b && 'active']}}" bindtap="{{item.c}}"><text>店内</text></view></block></view></view></view><view class="content"><scroll-view class="menus" style="{{g}}" scroll-into-view="{{h}}" scroll-with-animation="{{true}}" scroll-animation-duration="{{1}}" scroll-y><view class="category-wrapper"><block wx:for="{{f}}" wx:for-item="item" wx:key="j"><view wx:if="{{item.a}}" id="{{item.g}}" class="{{['menu', 'd-s-c', item.h && 'current']}}" bindtap="{{item.i}}"><image wx:if="{{item.b}}" src="{{item.c}}" mode="aspectFill" class="f-s-0 menu-imgs"></image><text>{{item.d}}</text><view wx:if="{{item.e}}" class="menu-cartNum">{{item.f}}</view></view></block><view class="menu-bottom"></view></view></scroll-view><scroll-view class="goods pr" style="{{k}}" scroll-y scroll-top="{{l}}" bindscroll="{{m}}"><view class="goods-wrapper"><view class="list" style="{{j}}"><block wx:for="{{i}}" wx:for-item="item" wx:key="e"><view wx:if="{{item.a}}" class="category" id="{{item.d}}"><view class="title"><text>{{item.b}}</text></view><view class="goods-items"><view wx:for="{{item.c}}" wx:for-item="good" wx:key="s" class="good" bindtap="{{good.r}}"><view class="image-boxs"><view wx:if="{{good.a}}" class="sallsell-out"><view class="sallsell-out-btn">当前售罄</view></view><image src="{{good.b}}" class="image"></image></view><view class="product-info"><view class="ww100"><view class="name text-ellipsis">{{good.c}}</view><view class="tips text-ellipsis">{{good.d}}</view></view><view class="price_and_action"><view><text class="f24 theme-price">￥</text><text class="f34 theme-price">{{good.e}}</text><text wx:if="{{good.f}}" class="linprice">￥{{good.g}}</text></view><view class="btn-group"><image wx:if="{{good.h}}" catchtap="{{good.i}}" class="add-image" src="{{good.j}}" mode=""></image><view wx:if="{{good.k}}" class="number">{{good.l}}</view><image wx:if="{{good.m}}" catchtap="{{good.n}}" class="add-image" src="{{good.o}}" mode=""></image><image wx:if="{{good.p}}" class="add-image" src="{{good.q}}" mode=""></image></view></view></view></view></view></view></block></view></view></scroll-view></view><view class="cart-box" catchtap="{{x}}"><view class="mark"><view class="icon iconfont icon-gouwudai cart-view"></view><view wx:if="{{n}}" class="tag">{{o}}</view></view><view wx:if="{{p}}" class="price"><view><text class="f22">￥</text><text class="f36">{{q}}</text><text class="gray9 f22 text-d-line fn ml10">￥{{r}}</text></view></view><view wx:else class="flex-1 f32 white">未选购商品</view><block wx:if="{{s}}"><button class="pay-btn" catchtap="{{t}}"><text>下单</text></button></block><block wx:else><button wx:if="{{v}}" class="pay-btn" catchtap="{{w}}"><text>去结算</text></button></block></view></view><popup-layer wx:if="{{y}}" u-s="{{['content']}}" class="cart-popup" bindcloseCallBack="{{D}}" u-i="f56f1456-0" bind:__l="__l" u-p="{{E}}"><view class="cart-popup pr" slot="content"><view class="top d-b-c"><view class="f30 gray3 d-s-c"> 购物车 <view wx:if="{{z}}" class="f22 gray3">（打包费 <text class="theme-price">￥{{A}}</text>）</view></view><view bindtap="{{B}}" class="d-c-c"><text class="icon iconfont icon-shanchu1"></text>清空购物车</view></view><scroll-view class="cart-list" scroll-y><view class="wrapper"><block wx:for="{{C}}" wx:for-item="item" wx:key="m"><view wx:if="{{item.a}}" class="item"><view class="d-s-c ww100"><view class="cart-image"><image style="" src="{{item.b}}" mode="aspectFill"></image></view><view class="left"><view><view class="name text-ellipsis">{{item.c}}</view><view class="gray9">{{item.d}}</view></view><view class="center"><text>￥</text><text class="f34">{{item.e}}</text><text wx:if="{{item.f}}" class="f24 gray9 ml10">包装费￥{{item.g}}</text></view></view></view><view class="right"><image bindtap="{{item.h}}" class="btn-image" src="{{item.i}}" mode=""></image><view class="number">{{item.j}}</view><image bindtap="{{item.k}}" class="btn-image" src="{{item.l}}" mode=""></image></view></view></block></view></scroll-view></view></popup-layer><prospec wx:if="{{F}}" bindclose="{{G}}" u-i="f56f1456-1" bind:__l="__l" u-p="{{H}}"></prospec><view wx:if="{{I}}" class="loading"><image src="{{J}}"></image></view></view>