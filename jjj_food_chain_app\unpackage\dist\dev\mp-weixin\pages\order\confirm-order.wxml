<view data-theme="{{T}}" class="{{U}}"><view wx:if="{{a}}" class="wrap"><view wx:if="{{b}}" class="right"><block wx:for="{{c}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="{{['takeout', 'd-c-c', item.b]}}" bindtap="{{item.c}}"><view class="d-c-c"><text class="icon-waimai1 icon iconfont top-icon"></text><text>配送</text></view></view></block><block wx:for="{{d}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="{{['dinein', 'd-c-c', item.b]}}" bindtap="{{item.c}}"><view class="d-c-c"><text class="icon-wodedianpu icon iconfont top-icon"></text><text>自取</text></view></view></block></view><view wx:if="{{e}}" class="right"><block wx:for="{{f}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="{{['takeout', 'd-c-c', item.b]}}" bindtap="{{item.c}}"><view class="d-c-c"><text class="icon-waimai icon iconfont top-icon"></text><text>打包</text></view></view></block><block wx:for="{{g}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="{{['dinein', 'd-c-c', item.b]}}" bindtap="{{item.c}}"><view class="d-c-c"><text class="icon-dianneijiucan icon iconfont top-icon"></text><text>堂食</text></view></view></block></view><view wx:if="{{h}}" class="header"><view class="headr_top"><view wx:if="{{i}}" class="flex-1" style="width:100%"><view class="left overflow-hidden"><view class="overflow-hidden w-b-a" style="width:600rpx" bindtap="{{n}}"><block wx:if="{{j}}"><view class="f32 fb gray3 mb20" style="word-break:break-all">{{k}}</view><view class="f28 gray3">{{l}}({{m}})</view></block><block wx:else> 请选择配送地址 </block></view></view></view><view wx:if="{{o}}" class="header_bottom d-b-c"><view class="shop-info" style="width:430rpx"><view class="f32 fb mb10">{{p}}</view><view class="gray9 shop-address">{{q}}</view></view><view class="d-c d-c-c"><view class="icon iconfont icon-address shop-pop-icon"></view></view></view></view><view wx:if="{{r}}" class="d-b-c meal_item"><view class="f28">预留电话</view><input type="text" class="header-input" placeholder="请输入" value="{{s}}" bindinput="{{t}}"/></view><view wx:if="{{v}}" class="d-b-c meal_item"><view class="f28">取餐时间</view><view class=""><text class="f32 theme-price">{{w}}</text></view></view><view wx:if="{{x}}" class="d-b-c meal_item" bindtap="{{z}}"><view class="f28">预计送达时间</view><view class=""><text class="theme-price f32">{{y}}</text><text class="icon iconfont icon-jiantou"></text></view></view></view><view class="vender"><view class="left d-s-c"><view class="store-name"><text>{{A}}</text></view></view><view class="list"><view wx:for="{{B}}" wx:for-item="item" wx:key="g" class="item d-b-c"><view class="info d-s-s"><view class="cover"><image src="{{item.a}}" mode="aspectFill"></image></view><view class="flex-1"><view class="f30 fb mb16">{{item.b}}</view><view class="num-wrap pl-30 gray9 f26">{{item.c}}</view><view class="f26 gray9">×{{item.d}}</view></view></view><view class="" style="height:148rpx;text-align:right"><view class="f32 order_item mb16">¥{{item.e}}</view><view class="f24 text-d-line gray9 mb16">¥{{item.f}}</view></view></view></view><view class="other_box"><view class="item"><text class="key">商品小计：</text><text class="f32">￥{{C}}</text></view><view class="item"><text class="key">包装费用：</text><text class="f32">￥{{D}}</text></view><view wx:if="{{E}}" class="item"><text class="key">配送费：</text><view><text wx:if="{{F}}" class="gray9 f32 text-d-line">{{G}}</text><text class="f32">{{H}}</text></view></view><view wx:if="{{I}}" class="item"><text class="key">桌号：</text><text class="f32">{{J}}</text></view></view><view class="total-box"> 共{{K}}件商品 小计 <text class="f32 fb ml15">￥{{L}}</text></view></view><view class="remarks"><view class="d-b-c item"><view class="mr20">备注:</view><input class="flex-1" type="text" placeholder="请填写您的其他要求" value="{{M}}" bindinput="{{N}}"/></view></view><view class="foot-pay-btns"><view wx:if="{{O}}" class="price"> ¥ <text class="num">{{P}}</text></view><button class="theme-btn" bindtap="{{Q}}">提交订单</button></view><timepicker wx:if="{{S}}" bindclose="{{R}}" u-i="4b708b4b-0" bind:__l="__l" u-p="{{S}}"></timepicker></view></view>