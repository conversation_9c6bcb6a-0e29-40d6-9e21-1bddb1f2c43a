"use strict";const e=require("../../common/vendor.js"),o=require("../../common/gotopage.js"),s={data:()=>({formData:{mobile:"",code:""},loging_password:"",register:{mobile:"",password:"",repassword:"",code:""},resetpassword:{mobile:"",password:"",repassword:"",code:""},is_send:!1,send_btn_txt:"获取验证码",second:60,ip:"",isShow:!0,is_login:1,is_code:!1,phoneHeight:0}),onShow(){this.init()},methods:{init(){let o=this;e.index.getSystemInfo({success(e){o.phoneHeight=e.windowHeight}})},formSubmit(){let s=this,i={mobile:s.formData.mobile},t="";if(/^1(3|4|5|6|7|8|9)\d{9}$/.test(s.formData.mobile)){if(s.is_code){if(""==s.formData.code)return void e.index.showToast({title:"验证码不能为空！",duration:2e3,icon:"none"});i.code=s.formData.code,t="user.useropen/smslogin"}else{if(""==s.loging_password)return void e.index.showToast({title:"密码不能为空！",duration:2e3,icon:"none"});i.password=s.loging_password,t="user.useropen/phonelogin"}e.index.showLoading({title:"正在提交"}),s._post(t,i,(s=>{e.index.setStorageSync("token",s.data.token),e.index.setStorageSync("user_id",s.data.user_id);let i=e.index.getStorageSync("currentPage"),t=e.index.getStorageSync("currentPageOptions");if(Object.keys(t).length>0){i+="?";for(let e in t)i+=e+"="+t[e]+"&";i=i.substring(0,i.length-1)}o.gotopage(i)}),!1,(()=>{e.index.hideLoading()}))}else e.index.showToast({title:"手机有误,请重填！",duration:2e3,icon:"none"})},registerSub(){let o=this;/^1(3|4|5|6|7|8|9)\d{9}$/.test(o.register.mobile)?""!=o.register.code?o.register.password.length<6?e.index.showToast({title:"密码至少6位数！",duration:2e3,icon:"none"}):o.register.password===o.register.repassword?(o.register.invitation_id=e.index.getStorageSync("invitation_id")||0,o.register.referee_id=e.index.getStorageSync("referee_id")||0,e.index.showLoading({title:"正在提交"}),o._post("user.useropen/register",o.register,(s=>{e.index.showToast({title:"注册成功",duration:3e3}),o.formData.mobile=o.register.mobile,o.register={mobile:"",password:"",repassword:"",code:""},o.second=0,o.changeMsg(),o.is_login=1}),!1,(()=>{e.index.hideLoading()}))):e.index.showToast({title:"两次密码输入不一致！",duration:2e3,icon:"none"}):e.index.showToast({title:"验证码不能为空！",duration:2e3,icon:"none"}):e.index.showToast({title:"手机有误,请重填！",duration:2e3,icon:"none"})},resetpasswordSub(){let o=this;/^1(3|4|5|6|7|8|9)\d{9}$/.test(o.resetpassword.mobile)?""!=o.resetpassword.code?o.resetpassword.password.length<6?e.index.showToast({title:"密码至少6位数！",duration:2e3,icon:"none"}):o.resetpassword.password===o.resetpassword.repassword?(e.index.showLoading({title:"正在提交"}),o._post("user.useropen/resetpassword",o.resetpassword,(s=>{e.index.showToast({title:"重置成功",duration:3e3}),o.formData.mobile=o.resetpassword.mobile,o.resetpassword={mobile:"",password:"",repassword:"",code:""},o.second=0,o.changeMsg(),o.is_login=1}),!1,(()=>{e.index.hideLoading()}))):e.index.showToast({title:"两次密码输入不一致！",duration:2e3,icon:"none"}):e.index.showToast({title:"验证码不能为空！",duration:2e3,icon:"none"}):e.index.showToast({title:"手机有误,请重填！",duration:2e3,icon:"none"})},isCode(){this.is_code?this.$set(this,"is_code",!1):this.$set(this,"is_code",!0)},sendCode(){let o=this;if(1==o.is_login){if(!/^1(3|4|5|6|7|8|9)\d{9}$/.test(o.formData.mobile))return void e.index.showToast({title:"手机有误,请重填！",duration:2e3,icon:"none"})}else if(2==o.is_login){if(!/^1(3|4|5|6|7|8|9)\d{9}$/.test(o.register.mobile))return void e.index.showToast({title:"手机有误,请重填！",duration:2e3,icon:"none"})}else if(0==o.is_login&&!/^1(3|4|5|6|7|8|9)\d{9}$/.test(o.resetpassword.mobile))return void e.index.showToast({title:"手机有误,请重填！",duration:2e3,icon:"none"});let s="register",i=o.register.mobile;1==o.is_login?(s="login",i=o.formData.mobile):0==o.is_login&&(s="login",i=o.resetpassword.mobile),o._post("user.useropen/sendCode",{mobile:i,type:s},(s=>{1==s.code&&(e.index.showToast({title:"发送成功"}),o.is_send=!0,o.changeMsg())}))},login(){let s=this;plus.oauth.getServices((function(i){let t=i[0];t.authResult?console.log("已经登陆认证"):t.authorize((function(i){e.index.showLoading({title:"登录中",mask:!0}),s._post("user.useropen/login",{code:i.code,source:"app"},(s=>{e.index.setStorageSync("token",s.data.token),e.index.setStorageSync("user_id",s.data.user_id);let i=e.index.getStorageSync("currentPage"),t=e.index.getStorageSync("currentPageOptions");if(Object.keys(t).length>0){i+="?";for(let e in t)i+=e+"="+t[e]+"&";i=i.substring(0,i.length-1)}console.log("url = "+i),o.gotopage(i)}),!1,(()=>{e.index.hideLoading()}))}),(function(o){console.log("登陆认证失败!"),e.index.showModal({title:"认证失败1",content:JSON.stringify(o)})}))}),(function(e){console.log("获取服务列表失败："+JSON.stringify(e))}))},changeMsg(){this.second>0?(this.send_btn_txt=this.second+"秒",this.second--,setTimeout(this.changeMsg,1e3)):(this.send_btn_txt="获取验证码",this.second=60,this.is_send=!1)}}};const i=e._export_sfc(s,[["render",function(o,s,i,t,n,r){return e.e({a:2==n.is_login},2==n.is_login?{b:e.o((e=>n.is_login=1)),c:n.is_send,d:n.register.mobile,e:e.o((e=>n.register.mobile=e.detail.value)),f:n.register.password,g:e.o((e=>n.register.password=e.detail.value)),h:n.register.repassword,i:e.o((e=>n.register.repassword=e.detail.value)),j:n.register.code,k:e.o((e=>n.register.code=e.detail.value)),l:e.t(n.send_btn_txt),m:e.o(((...e)=>r.sendCode&&r.sendCode(...e))),n:n.is_send}:{},{o:1==n.is_login},1==n.is_login?e.e({p:e.o((e=>n.is_login=2)),q:n.formData.mobile,r:e.o((e=>n.formData.mobile=e.detail.value)),s:!n.is_code},n.is_code?{}:{t:n.loging_password,v:e.o((e=>n.loging_password=e.detail.value))},{w:n.is_code},n.is_code?{x:n.formData.code,y:e.o((e=>n.formData.code=e.detail.value)),z:e.t(n.send_btn_txt),A:e.o(((...e)=>r.sendCode&&r.sendCode(...e))),B:n.is_send}:{}):{},{C:0==n.is_login},0==n.is_login?{D:e.o((e=>n.is_login=1)),E:n.is_send,F:n.resetpassword.mobile,G:e.o((e=>n.resetpassword.mobile=e.detail.value)),H:n.resetpassword.password,I:e.o((e=>n.resetpassword.password=e.detail.value)),J:n.resetpassword.repassword,K:e.o((e=>n.resetpassword.repassword=e.detail.value)),L:n.resetpassword.code,M:e.o((e=>n.resetpassword.code=e.detail.value)),N:e.t(n.send_btn_txt),O:e.o(((...e)=>r.sendCode&&r.sendCode(...e))),P:n.is_send}:{},{Q:1==n.is_login},1==n.is_login?e.e({R:!n.is_code},n.is_code?{}:{S:e.o((e=>n.is_login=0))},{T:e.t(n.is_code?"使用密码登录":"使用验证码登录"),U:e.o((e=>r.isCode())),V:e.n(n.is_code?"d-e-c":"d-b-c")}):{},{W:2==n.is_login},2==n.is_login?{X:e.o(((...e)=>r.registerSub&&r.registerSub(...e)))}:{},{Y:1==n.is_login},1==n.is_login?{Z:e.o(((...e)=>r.formSubmit&&r.formSubmit(...e)))}:{},{aa:0==n.is_login},0==n.is_login?{ab:e.o(((...e)=>r.resetpasswordSub&&r.resetpasswordSub(...e)))}:{},{ac:1==n.is_login},1==n.is_login?{ad:e.o(((...e)=>r.login&&r.login(...e)))}:{},{ae:e.s("height: "+n.phoneHeight+"px;")})}],["__scopeId","data-v-426aff41"]]);wx.createPage(i);
