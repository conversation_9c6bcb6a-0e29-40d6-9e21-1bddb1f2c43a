"use strict";const e=require("../../../common/vendor.js"),d=require("../../../common/assets.js"),t={data:()=>({loadding:!0,indicatorDots:!0,autoplay:!0,interval:2e3,duration:500,listData:[],default_id:"0",options:{}}),onLoad:function(e){this.options=e},onShow:function(){e.index.showLoading({title:"加载中"}),this.getData()},methods:{getData(){let d=this;d.dataType,d._get("user.address/list",{shop_supplier_id:d.options.shop_supplier_id},(function(t){d.listData=t.data.list,d.default_id=t.data.default_id+"",d.loadding=!1,e.index.hideLoading()}))},addAddress(){let d=1;"order"===this.options.source&&(d=2),e.index.navigateTo({url:"/pages/user/address/add/add?delta="+d})},radioChange(d){let t=this;return t.default_id=d,t._post("user.address/setDefault",{address_id:d},(function(d){t.$fire.fire("takeout",!0),e.index.navigateBack()})),!1},editAddress(d){e.index.navigateTo({url:"/pages/user/address/edit/edit?address_id="+d})},delAddress(d){let t=this;e.index.showModal({title:"提示",content:"您确定要移除当前收货地址吗?",success:function(s){s.confirm&&t._get("user.address/delete",{address_id:d},(function(d){1==d.code&&(e.index.showToast({title:"删除成功",duration:2e3}),t.getData())}))}})}}};const s=e._export_sfc(t,[["render",function(t,s,a,i,o,n){return e.e({a:!o.loadding},o.loadding?{}:e.e({b:o.listData.length>0},o.listData.length>0?{c:e.f(o.listData,((d,t,s)=>e.e({a:e.t(d.name),b:e.t(d.phone),c:e.t(d.detail),d:e.t(d.address),e:e.t(d.distance),f:0==d.status},(d.status,{}),{g:e.o((e=>n.delAddress(d.address_id)),t),h:e.o((e=>n.editAddress(d.address_id)),t),i:d.address_id+"",j:o.default_id==d.address_id+"",k:e.o((e=>n.radioChange(d.address_id)),t),l:t}))),d:d._imports_0$1,e:d._imports_1,f:t.getThemeColor(),g:e.o((e=>n.addAddress()))}:{h:d._imports_2,i:e.o((e=>n.addAddress())),j:e.o((e=>t.gotoPage("/pages/index/index")))},{k:t.theme(),l:e.n(t.theme()||"")}))}]]);wx.createPage(s);
