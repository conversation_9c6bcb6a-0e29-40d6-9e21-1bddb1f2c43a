/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.headr_top {
  min-height: 140rpx;
  padding-bottom: 20rpx;
  box-sizing: border-box;
}
.header {
  width: 100%;
  box-sizing: border-box;
  padding: 35rpx;
  padding-bottom: 0;
  background-color: #ffffff;
  border-radius: 30rpx;
  overflow: hidden;
  position: relative;
  z-index: 20;
}
.left {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.left .store-name {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 26rpx;
  margin-bottom: 30rpx;
}
.left .store-name .iconfont {
  margin-left: 10rpx;
  line-height: 100%;
}
.left .store-location {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #919293;
  font-size: 24rpx;
}
.left .store-location .iconfont {
  vertical-align: middle;
  display: table-cell;
  color: #f5a654;
  line-height: 100%;
}
.wrap {
  padding: 25rpx;
  padding-bottom: 140rpx;
}
.icon-box .icon-zhifubao {
  color: #1296db;
  font-size: 50rpx;
}
.order_item {
  width: 150rpx;
  text-align: right;
}
.other_box {
  border-radius: 0;
  box-shadow: 0;
}
.other_box .item {
  height: 88rpx;
  box-sizing: border-box;
  line-height: 88rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eeeeee;
}
.f-r {
  float: right;
}
.meal_item {
  height: 92rpx;
  line-height: 92rpx;
  border-top: 1px solid #eee;
}
.meal_item .icon-jiantou {
  font-size: 22rpx;
  margin-left: 26rpx;
  color: #666;
}
.remarks {
  margin: 26rpx 0;
  height: 96rpx;
  line-height: 96rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  padding-left: 42rpx;
  padding-right: 30rpx;
}
.remarks .icon-jiantou {
  font-size: 22rpx;
  margin-left: 26rpx;
  color: #666;
}
.pack_item {
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
}
[data-theme=theme0] .pack_item.active .icon-xuanze {
  color: #fee238 !important;
}
[data-theme=theme1] .pack_item.active .icon-xuanze {
  color: #b91d32 !important;
}
[data-theme=theme2] .pack_item.active .icon-xuanze {
  color: #09b4f1 !important;
}
[data-theme=theme3] .pack_item.active .icon-xuanze {
  color: #00a348 !important;
}
[data-theme=theme4] .pack_item.active .icon-xuanze {
  color: #fd8103 !important;
}
[data-theme=theme5] .pack_item.active .icon-xuanze {
  color: #b99970 !important;
}
[data-theme=theme6] .pack_item.active .icon-xuanze {
  color: #a4a4d6 !important;
}
.right {
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  font-size: 32rpx;
  color: #282828;
  height: 68rpx;
  width: 100%;
  position: relative;
  margin-top: 20rpx;
}
.right .dinein,
.right .takeout {
  position: relative;
  display: flex;
  align-items: flex-start;
  width: 344rpx;
  height: 150rpx;
  box-sizing: border-box;
  padding-top: 25rpx;
  position: absolute;
  z-index: 0;
  top: -18rpx;
  background-color: #ECECEC;
}
.right .dinein.active,
.right .takeout.active {
  z-index: 1;
  width: 490rpx;
  height: 150rpx;
  font-weight: bold;
  background-color: #ffffff !important;
  flex-shrink: 0;
}
.right .takeout {
  justify-content: flex-start;
  padding-left: 78rpx;
  left: 0;
  border-top-left-radius: 30rpx;
}
.right .takeout.active {
  padding-left: 153rpx;
  border-top-right-radius: 150rpx;
  border-top-left-radius: 30rpx;
}
.right .dinein {
  right: 0;
  justify-content: flex-end;
  padding-right: 65rpx;
  border-top-right-radius: 30rpx;
}
.right .dinein.active {
  padding-right: 150rpx;
  border-top-left-radius: 150rpx;
  border-top-right-radius: 30rpx;
}
.foot-pay-btns button {
  padding: 0 50rpx;
  height: 84rpx;
  line-height: 84rpx;
  border-radius: 50rpx;
}
.discount {
  display: inline-block;
  font-size: 24rpx;
  color: #fff;
  border-radius: 5rpx;
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  background: #fa301b;
  margin-right: 5rpx;
  font-weight: 400;
}
.discount.discount-s {
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  margin-right: 8rpx;
  font-size: 20rpx;
}
.right .active .top-icon {
  display: block;
}
.right .top-icon {
  display: none;
  font-size: 40rpx;
  color: #333;
  margin-right: 10rpx;
}
.header-input {
  font-size: 26rpx;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}
.shop-info {
  width: 430rpx;
  margin-right: 20rpx;
}
.shop-info .shop-address {
  line-height: 1.5;
  font-size: 24rpx;
}
.shop-pop-distance {
  white-space: nowrap;
  font-size: 20rpx;
  color: #333;
  height: 38rpx;
  line-height: 38rpx;
  padding: 0 18rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(0, 0, 0, 0.25);
  border-radius: 19rpx;
  margin-bottom: 20rpx;
}
.icon.shop-pop-icon {
  width: 58rpx;
  height: 58rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 3rpx 7rpx 0rpx rgba(4, 0, 0, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: #333;
  border-radius: 50%;
}
.sup_img {
  width: 44rpx;
  height: 44rpx;
  background: rgba(0, 0, 0, 0);
  opacity: 1;
  border-radius: 50%;
  margin-right: 10rpx;
  display: block;
}