<popup wx:if="{{i}}" class="data-v-b3983eb7" u-s="{{['d']}}" bindhidePopup="{{h}}" u-i="b3983eb7-0" bind:__l="__l" u-p="{{i}}"><view class="d-b-c time_picker data-v-b3983eb7"><view class=" data-v-b3983eb7" style="width:40%"><scroll-view class="data-v-b3983eb7" style="height:600rpx" scroll-y="true"><block wx:for="{{a}}" wx:for-item="item" wx:key="e"><view wx:if="{{item.a}}" class="{{['data-v-b3983eb7', item.c]}}" bindtap="{{item.d}}">{{item.b}}</view></block></scroll-view></view><view class="flex-1 data-v-b3983eb7"><scroll-view class="data-v-b3983eb7" style="height:600rpx" scroll-y="true"><block wx:for="{{b}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="data-v-b3983eb7" bindtap="{{item.c}}">{{item.b}}</view></block><block wx:for="{{c}}" wx:for-item="item" wx:key="c"><view wx:if="{{d}}" class="data-v-b3983eb7" bindtap="{{item.b}}">{{item.a}}</view></block><block wx:for="{{e}}" wx:for-item="item" wx:key="c"><view wx:if="{{f}}" class="data-v-b3983eb7" bindtap="{{item.b}}">{{item.a}}</view></block><block wx:for="{{g}}" wx:for-item="item" wx:key="d"><view wx:if="{{item.a}}" class="data-v-b3983eb7" bindtap="{{item.c}}">{{item.b}}</view></block></scroll-view></view></view></popup>