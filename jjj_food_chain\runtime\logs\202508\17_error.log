[2025-08-17T18:42:06+08:00][error] [0]缺少必要的参数：app_id
[2025-08-17T18:42:10+08:00][error] [0]缺少必要的参数：app_id
[2025-08-17T18:55:42+08:00][error] [0]请到 [后台-应用-小程序设置] 填写appid 和 appsecret
[2025-08-17T18:55:47+08:00][error] [0]请到 [后台-应用-小程序设置] 填写appid 和 appsecret
[2025-08-17T18:59:51+08:00][error] [0]fopen(): Unable to find the wrapper &quot;https&quot; - did you forget to enable it when you configured PHP?
[2025-08-17T18:59:51+08:00][error] fopen(): Unable to find the wrapper &quot;https&quot; - did you forget to enable it when you configured PHP?
[2025-08-17T18:59:51+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\symfony\http-client\Response\CommonResponseTrait.php(144): Symfony\Component\HttpClient\Chunk\ErrorChunk->isFirst()
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\symfony\http-client\Response\CommonResponseTrait.php(41): Symfony\Component\HttpClient\Response\NativeResponse::initialize(Object(Symfony\Component\HttpClient\Response\NativeResponse))
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\symfony\http-client\Response\CommonResponseTrait.php(77): Symfony\Component\HttpClient\Response\NativeResponse->getContent(false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\w7corp\easywechat\src\MiniApp\Utils.php(36): Symfony\Component\HttpClient\Response\NativeResponse->toArray(false)
#4 E:\code\diancan\jjjfood\jjj_food_chain\app\api\model\user\User.php(47): EasyWeChat\MiniApp\Utils->codeToSession('0f33dk00039iNU1...')
#5 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\user\User.php(20): app\api\model\user\User->login(Array)
#6 [internal function]: app\api\controller\user\User->login()
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\user\User), Array)
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\user\User), Object(ReflectionMethod), Array)
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#24 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#32 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#36 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#37 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#38 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#39 {main}
[2025-08-17T19:00:16+08:00][error] [-1]缺少必要的参数：token
[2025-08-17T19:00:17+08:00][error] [0]fopen(): Unable to find the wrapper &quot;https&quot; - did you forget to enable it when you configured PHP?
[2025-08-17T19:00:17+08:00][error] fopen(): Unable to find the wrapper &quot;https&quot; - did you forget to enable it when you configured PHP?
[2025-08-17T19:00:17+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\symfony\http-client\Response\CommonResponseTrait.php(144): Symfony\Component\HttpClient\Chunk\ErrorChunk->isFirst()
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\symfony\http-client\Response\CommonResponseTrait.php(41): Symfony\Component\HttpClient\Response\NativeResponse::initialize(Object(Symfony\Component\HttpClient\Response\NativeResponse))
#2 E:\code\diancan\jjjfood\jjj_food_chain\vendor\symfony\http-client\Response\CommonResponseTrait.php(77): Symfony\Component\HttpClient\Response\NativeResponse->getContent(false)
#3 E:\code\diancan\jjjfood\jjj_food_chain\vendor\w7corp\easywechat\src\MiniApp\Utils.php(36): Symfony\Component\HttpClient\Response\NativeResponse->toArray(false)
#4 E:\code\diancan\jjjfood\jjj_food_chain\app\api\model\user\User.php(47): EasyWeChat\MiniApp\Utils->codeToSession('0e3Sqq100tmoOU1...')
#5 E:\code\diancan\jjjfood\jjj_food_chain\app\api\controller\user\User.php(20): app\api\model\user\User->login(Array)
#6 [internal function]: app\api\controller\user\User->login()
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\api\controller\user\User), Array)
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\api\controller\user\User), Object(ReflectionMethod), Array)
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#23 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#24 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#27 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#28 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#31 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#32 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#36 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#37 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#38 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#39 {main}
[2025-08-17T19:01:35+08:00][error] [-1]缺少必要的参数：token
[2025-08-17T19:04:49+08:00][error] [0]fopen(https://*************/wxa/getwxacodeunlimit?access_token=95_3yEaONEnXWW_xrOJUHv8umRKsNIpIYCwguXQZGZbUQ3lXttq0IZNzBr6O1dhJZJNOQ50SXM4lejKnQENRF-xGGzz4vXIXRbTkixSKpC9zUTkEP4cVISV_U-8DlcFQUhACAMXB): Failed to open stream: HTTP request failed!
[2025-08-17T19:04:49+08:00][error] fopen(https://*************/wxa/getwxacodeunlimit?access_token=95_3yEaONEnXWW_xrOJUHv8umRKsNIpIYCwguXQZGZbUQ3lXttq0IZNzBr6O1dhJZJNOQ50SXM4lejKnQENRF-xGGzz4vXIXRbTkixSKpC9zUTkEP4cVISV_U-8DlcFQUhACAMXB): Failed to open stream: HTTP request failed!
[2025-08-17T19:04:49+08:00][error] #0 E:\code\diancan\jjjfood\jjj_food_chain\vendor\symfony\http-client\Response\CommonResponseTrait.php(41): Symfony\Component\HttpClient\Response\NativeResponse::initialize(Object(Symfony\Component\HttpClient\Response\NativeResponse))
#1 E:\code\diancan\jjjfood\jjj_food_chain\vendor\w7corp\easywechat\src\Kernel\HttpClient\Response.php(228): Symfony\Component\HttpClient\Response\NativeResponse->getContent(false)
#2 E:\code\diancan\jjjfood\jjj_food_chain\app\common\service\qrcode\Base.php(40): EasyWeChat\Kernel\HttpClient\Response->saveAs('E:\\code\\diancan...')
#3 E:\code\diancan\jjjfood\jjj_food_chain\app\common\service\qrcode\TableService.php(40): app\common\service\qrcode\Base->saveQrcodeToDir(10001, 'pages/product/l...', 'E:\\code\\diancan...', '2', 10001)
#4 E:\code\diancan\jjjfood\jjj_food_chain\app\shop\controller\store\table\Table.php(81): app\common\service\qrcode\TableService->getImage()
#5 [internal function]: app\shop\controller\store\table\Table->qrcode('2', 'wx')
#6 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Container.php(345): ReflectionMethod->invokeArgs(Object(app\shop\controller\store\table\Table), Array)
#7 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(110): think\Container->invokeReflectMethod(Object(app\shop\controller\store\table\Table), Object(ReflectionMethod), Array)
#8 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\route\dispatch\Controller->think\route\dispatch\{closure}(Object(app\Request))
#9 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#10 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\dispatch\Controller.php(84): think\Pipeline->then(Object(Closure))
#11 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\route\Dispatch.php(52): think\route\dispatch\Controller->exec()
#12 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(755): think\route\Dispatch->run()
#13 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Route->think\{closure}(Object(app\Request))
#14 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#15 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Route.php(754): think\Pipeline->then(Object(Closure))
#16 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(208): think\Route->dispatch(Object(app\Request), Object(Closure))
#17 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(198): think\Http->dispatchToRoute(Object(app\Request))
#18 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\Http->think\{closure}(Object(app\Request))
#19 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(51): think\Pipeline->think\{closure}(Object(app\Request))
#20 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(59): think\app\MultiApp->think\app\{closure}(Object(app\Request))
#21 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#22 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-multi-app\src\MultiApp.php(50): think\Pipeline->then(Object(Closure))
#23 [internal function]: think\app\MultiApp->handle(Object(app\Request), Object(Closure))
#24 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#25 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#26 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\middleware\SessionInit.php(58): think\Pipeline->think\{closure}(Object(app\Request))
#27 [internal function]: think\middleware\SessionInit->handle(Object(app\Request), Object(Closure))
#28 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#29 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#30 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\think-trace\src\TraceDebug.php(71): think\Pipeline->think\{closure}(Object(app\Request))
#31 [internal function]: think\trace\TraceDebug->handle(Object(app\Request), Object(Closure))
#32 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Middleware.php(134): call_user_func(Array, Object(app\Request), Object(Closure))
#33 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(85): think\Middleware->think\{closure}(Object(app\Request), Object(Closure))
#34 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Pipeline.php(66): think\Pipeline->think\{closure}(Object(app\Request))
#35 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(197): think\Pipeline->then(Object(Closure))
#36 E:\code\diancan\jjjfood\jjj_food_chain\vendor\topthink\framework\src\think\Http.php(162): think\Http->runWithRequest(Object(app\Request))
#37 E:\code\diancan\jjjfood\jjj_food_chain\public\index.php(20): think\Http->run()
#38 {main}
