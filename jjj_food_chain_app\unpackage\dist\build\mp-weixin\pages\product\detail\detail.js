"use strict";const e=require("../../../common/vendor.js"),t=require("../../../common/utils.js"),i=require("../../../common/assets.js"),s={components:{},data:()=>({detail:{product_attr:[],product_feed:[],sku:[]},Visible:!1,form:{attr:[],product_sku_id:[],feed:[],detail:{},show_sku:{sku_image:"",bag_price:""},shop_supplier_id:0},stock:0,selectSpec:"",isOpenSpec:!1,type:"",product_price:"",feed_price:0,space_name:"",attr_name:[],feed_name:[],product_lineprice:"",delivery:"",bag_type:1,dinner_type:20,cart_type:0,table_id:0,clock:!1,discount_price:0}),computed:{price:function(){return this.discount_price?((1*this.discount_price+1*this.product_price)*this.form.show_sku.sum).toFixed(2):((1*this.feed_price+1*this.product_price)*this.form.show_sku.sum).toFixed(2)},lineprice:function(){return((1*this.feed_price+1*this.product_lineprice)*this.form.show_sku.sum).toFixed(2)},isadd:function(){return this.form.show_sku.sum>=this.stock||this.form.show_sku.sum>=this.form.detail.limit_num},issub:function(){return this.form.show_sku.sum<=1}},watch:{},onLoad(e){this.product_id=e.product_id,this.delivery=e.delivery,this.bag_type=e.bag_type,this.dinner_type=e.dinner_type,this.cart_type=e.cart_type,this.table_id=e.table_id||0,this.shop_supplier_id=e.shop_supplier_id||0},onShow(){this.clock=!1,this.getData()},methods:{bag_price:function(){return(this.form.show_sku.bag_price*this.form.show_sku.sum).toFixed(2)},goback(){e.index.navigateBack()},getData(){let i=this;i._get("product.product/detail",{product_id:i.product_id,table_id:i.table_id||0,shop_supplier_id:i.shop_supplier_id||0},(s=>{s.data.detail?(s.data.detail.content=t.utils.format_content(s.data.detail.content),i.detail=s.data.detail,this.showGoodDetailModal()):i.showError("商品已下架",(()=>{e.index.navigateBack()}))}))},showGoodDetailModal(){let e=this;this.detail.sku.forEach(((e,t)=>{e.checked=!1}));let t={specData:this.detail.sku,detail:this.detail,shop_supplier_id:this.detail.shop_supplier_id,productSpecArr:null!=this.specData?new Array(this.specData.spec_attr.length):[],show_sku:{sku_image:"",seckill_price:0,attr:[],product_sku_id:[],feed:[],line_price:0,seckill_stock:0,seckill_product_sku_id:0,sum:1}};e.form=t,e.space_name="",e.attr_name=[],e.feed_name=[],e.isOpenSpec=!0,e.initShowSku(),0==e.form.detail.sku[0].checked&&e.selecedtSpec(e.form.detail.sku[0],0),""!=e.form.detail.product_attr&&e.form.detail.product_attr.forEach(((t,i)=>{e.form.show_sku.attr[i]||e.selectAttr(t.attribute_value[0],0,i)}))},describe:function(){let e=this.space_name;""!=e&&(e+=";");let t=this.attr_name.join(";");""!=t&&(t+=";");let i=this.feed_name.join(",");return""!=i&&(i+=";"),e+t+i},initShowSku(){this.form.show_sku.sku_image=this.form.detail.product_image,this.form.show_sku.product_price=this.form.detail.product_price,this.form.show_sku.bag_price=this.form.detail.bag_price,this.form.show_sku.product_sku_id=[],this.form.show_sku.attr=[],this.form.show_sku.feed=[],this.form.show_sku.feed.length=this.form.detail.product_feed.length,this.form.show_sku.line_price=this.form.detail.line_price,this.form.show_sku.stock_num=this.form.detail.product_stock,this.form.show_sku.sum=1,this.stock=this.form.detail.product_stock},selecedtSpec(e,t){let i=this;e.checked?(e.checked=!1,i.form.show_sku.product_sku_id[0]=null):(i.form.detail.sku.forEach(((e,t)=>{e.checked=!1})),e.checked=!0,i.form.show_sku.product_sku_id[0]=e.product_sku_id,i.space_name=e.spec_name,i.$set(i.form.show_sku,"product_price",e.product_price),i.$set(i.form.show_sku,"bag_price",e.bag_price),i.$set(i.form.show_sku,"line_price",e.line_price),i.$set(i.form.show_sku,"stock_num",e.stock_num)),null!=i.form.show_sku.product_sku_id[0]?i.updateSpecProduct():i.initShowSku()},selectAttr(e,t,i){let s=this;s.$set(s.form.show_sku.attr,i,t),s.attr_name[i]=e,s.updateSpecProduct()},selectFeed(e,t){let i=this;if(i.form.show_sku.feed[t]||0===i.form.show_sku.feed[t]){i.$set(i.form.show_sku.feed,t,null),i.feed_price-=1*e.price,e.discount_price?i.discount_price-=e.discount_price:i.discount_price-=0;let s=i.feed_name.indexOf(e.feed_name);s>-1&&i.feed_name.splice(s,1)}else i.$set(i.form.show_sku.feed,t,t),i.feed_price+=1*e.price,e.discount_price?i.discount_price+=e.discount_price:i.discount_price+=0,i.feed_name.push(e.feed_name);i.updateSpecProduct()},updateSpecProduct(){this.product_price=this.form.show_sku.product_price,console.log(this.product_lineprice),this.product_lineprice=this.form.show_sku.line_price},add(){this.stock<=0?e.index.showToast({title:"商品库存不足",icon:"none",duration:2e3}):this.form.show_sku.sum++},sub(){if(!(this.stock<=0))return this.form.show_sku.sum<2?(e.index.showToast({title:"商品数量至少为1",icon:"none",duration:2e3}),!1):void this.form.show_sku.sum--},confirmFunc(){if(!this.clock)if(null!=this.form.show_sku.product_sku_id[0]||20!=this.form.detail.spec_type){if(null!=this.form.detail.product_attr)for(let t=0;t<this.form.detail.product_attr.length;t++)if(null==this.form.show_sku.attr[t])return void e.index.showToast({title:"请选择属性",icon:"none",duration:2e3});this.form.show_sku.sum>this.form.show_sku.stock_num?e.index.showToast({title:"商品库存不足",icon:"none",duration:2e3}):this.addCart()}else e.index.showToast({title:"请选择规格",icon:"none",duration:2e3})},addCart(){let t=this,i=[];t.form.show_sku.feed.forEach(((e,t)=>{null!=e&&i.push(e)})),i=i.length<=0?"":i.join(",");let s="";s=t.discount_price?1*t.discount_price+1*t.product_price:1*t.feed_price+1*t.product_price;let o={product_id:t.form.detail.product_id,product_num:t.form.show_sku.sum,product_sku_id:t.form.show_sku.product_sku_id[0],attr:t.form.show_sku.attr.join(","),feed:i,describe:t.describe(),price:s,product_price:1*t.feed_price+1*t.form.show_sku.line_price,bag_price:t.form.show_sku.bag_price,shop_supplier_id:t.form.shop_supplier_id,cart_type:t.cart_type,dinner_type:t.dinner_type,delivery:t.delivery};t.clock=!0,t._post("order.cart/add",o,(function(i){t.clock=!1,e.index.navigateBack()}),(e=>{t.clock=!1}))}}};const o=e._export_sfc(s,[["render",function(t,s,o,r,c,d){return e.e({a:e.o(((...e)=>d.goback&&d.goback(...e))),b:i._imports_0,c:e.s(0==t.topBarHeight()?"":"height:"+t.topBarHeight()+"px;"),d:e.s(0==t.topBarHeight()?"":"height:"+t.topBarHeight()+"px;padding-top:"+t.topBarTop()+"px"),e:c.detail.product_image},c.detail.product_image?{f:c.detail.product_image}:{},{g:e.t(c.detail.product_name||""),h:e.t(c.detail.product_sales||""),i:e.t(c.detail.selling_point||""),j:20==c.detail.spec_type},20==c.detail.spec_type?{k:e.f(c.detail.sku,((t,i,s)=>({a:e.t(t.spec_name),b:e.o((e=>d.selecedtSpec(t,i)),i),c:i,d:t.checked?1:""})))}:{},{l:c.detail.product_attr.length>0},c.detail.product_attr.length>0?{m:e.f(c.detail.product_attr,((i,s,o)=>e.e({a:e.t(i.attribute_name||"")},""!=t.value?{b:e.f(i.attribute_value,((t,i,o)=>({a:e.t(t),b:e.o((e=>d.selectAttr(t,i,s)),i),c:i,d:c.form.show_sku.attr[s]==i?1:""})))}:{},{c:s}))),n:""!=t.value}:{},{o:c.detail.product_feed.length>0},c.detail.product_feed.length>0?{p:e.f(c.detail.product_feed,((t,i,s)=>({a:e.t(t.feed_name),b:e.o((e=>d.selectFeed(t,i)),i),c:i,d:null!=c.form.show_sku.feed[i]?1:""})))}:{},{q:e.t(d.price),r:e.t(d.lineprice),s:1!=c.bag_type},1!=c.bag_type?{t:e.t(d.bag_price())}:{},{v:e.t(d.describe()),w:e.o((e=>d.sub())),x:e.t(c.form.show_sku.sum),y:e.o((e=>d.add())),z:e.o(((...e)=>d.confirmFunc&&d.confirmFunc(...e))),A:c.detail.content,B:t.theme(),C:e.n(t.theme()||"")})}]]);wx.createPage(o);
