/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
page {
  background-color: #fff;
}
.login-container {
  padding: 30rpx;
}
.wechatapp {
  padding: 120rpx 0 48rpx;
  margin-bottom: 72rpx;
  text-align: center;
}
.wechatapp .logo-image {
  margin: 0 auto;
  margin-bottom: 20rpx;
  width: 145rpx;
  height: 145rpx;
  border-radius: 50%;
}
.m-0-a {
  margin: 0 auto;
}
.wechatapp .header {
  width: 190rpx;
  height: 190rpx;
  border: 2px solid #fff;
  margin: 0rpx auto 0;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 1px 0px 5px rgba(50, 50, 50, 0.3);
}
.auth-title {
  color: #333333;
  font-size: 30rpx;
  margin-top: 170rpx;
  margin-bottom: 85rpx;
  text-align: center;
}
.auth-subtitle {
  color: #777;
  margin-bottom: 20rpx;
  font-size: 22rpx;
  text-align: center;
}
.login-btn {
  padding: 0 20rpx;
}
.login-btn button {
  width: 592rpx;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  text-align: center;
  line-height: 88rpx;
}
.login-btn button.login-btn-ali {
  height: 88rpx;
  line-height: 88rpx;
  background: #1678ff;
  color: #fff;
  font-size: 30rpx;
  border-radius: 999rpx;
  text-align: center;
}
.no-login-btn {
  position: fixed;
  bottom: 220rpx;
  left: 0;
  width: 100%;
}
.agreement {
  border-radius: 50%;
  width: 28rpx;
  height: 28rpx;
  border: 1px solid #ccc;
  background: #fff;
  position: relative;
  margin-right: 10rpx;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18rpx;
  font-weight: bold;
}
[data-theme=theme0] .agreement.active {
  color: #111111 !important;
}
[data-theme=theme1] .agreement.active {
  color: #ffffff !important;
}
[data-theme=theme2] .agreement.active {
  color: #ffffff !important;
}
[data-theme=theme3] .agreement.active {
  color: #ffffff !important;
}
[data-theme=theme4] .agreement.active {
  color: #ffffff !important;
}
[data-theme=theme5] .agreement.active {
  color: #fff !important;
}
[data-theme=theme6] .agreement.active {
  color: #ffffff !important;
}
[data-theme=theme0] .agreement.active {
  background-color: #fee238 !important;
}
[data-theme=theme1] .agreement.active {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .agreement.active {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .agreement.active {
  background-color: #00a348 !important;
}
[data-theme=theme4] .agreement.active {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .agreement.active {
  background-color: #b99970 !important;
}
[data-theme=theme6] .agreement.active {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .agreement.active {
  border-color: #fee238 !important;
}
[data-theme=theme1] .agreement.active {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .agreement.active {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .agreement.active {
  border-color: #00a348 !important;
}
[data-theme=theme4] .agreement.active {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .agreement.active {
  border-color: #b99970 !important;
}
[data-theme=theme6] .agreement.active {
  border-color: #a4a4d6 !important;
}