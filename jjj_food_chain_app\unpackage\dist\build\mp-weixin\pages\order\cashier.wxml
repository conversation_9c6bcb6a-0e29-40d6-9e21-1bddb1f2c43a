<view data-theme="{{i}}" class="{{j}}"><view class="tc buy-checkout-top"><view class="f32 mb20">待支付</view><view class="redA8 f60 fb">￥{{a}}</view></view><view class="buy-checkout p-0-30"><block wx:for="{{b}}" wx:for-item="item"><view wx:if="{{item.a}}" key="{{item.b}}" class="{{item.c}}" bindtap="{{item.d}}"><view class="d-s-c"><view class="icon-box d-c-c mr10"><label class="icon iconfont icon-weixin"></label></view><text class="key">微信支付：</text></view><view class="icon-box d-c-c"><label class="icon iconfont icon-xuanze"></label></view></view></block><view wx:if="{{c}}" class="item"><view class="d-s-c"><view class="icon-box d-c-c mr10"><label class="icon iconfont icon-yue"></label></view><text class="key">余额抵扣：(剩余：{{d}})</text></view><switch color="{{e}}" style="transform:scale(0.7);margin-right:-20rpx" checked="{{f}}" bindchange="{{g}}"/></view></view><view class="bottom-btn theme-btn" bindtap="{{h}}">立即支付</view></view>