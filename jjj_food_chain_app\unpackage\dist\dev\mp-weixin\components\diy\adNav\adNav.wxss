/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.title1.data-v-c37442b0 {
  width: 128rpx;
  height: 40rpx;
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: bold;
  color: #3a3a3a;
  white-space: nowrap;
  margin-right: 90rpx;
}
.diy-navBar .list.data-v-c37442b0 {
  box-sizing: border-box;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
}
.diy-navBar .list .item.data-v-c37442b0 {
  padding: 20rpx;
  display: flex;
  box-sizing: border-box;
}
.diy-navBar .list.column-1 .item.data-v-c37442b0 {
  flex: 1;
  height: 150rpx;
  margin: 0 auto;
  justify-content: space-between;
  align-items: center;
}
.diy-navBar .list.column-2 .item.data-v-c37442b0 {
  flex: 1;
  height: 250rpx;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  flex-shrink: 0;
  overflow: hidden;
  position: relative;
}
.diy-navBar .list.column-2 .item .item-navimg.data-v-c37442b0 {
  margin-bottom: 12rpx;
}
.diy-navBar .list .item-image.data-v-c37442b0 {
  width: 60%;
}
.diy-navBar .list .item-image .images.data-v-c37442b0 {
  width: 100%;
}
.diy-navBar .list .item-text1.data-v-c37442b0 {
  width: 100%;
  font-size: 30rpx;
  font-weight: 600;
  color: #333333;
}
.diy-navBar .list .item-text2.data-v-c37442b0 {
  width: 100%;
  padding: 8rpx 0;
  font-weight: 400;
  color: #999999;
  font-size: 22rpx;
}
.item-navimg.data-v-c37442b0 {
  width: 100rpx;
  height: 100rpx;
}
.item-navimg .images.data-v-c37442b0 {
  width: 100rpx;
  height: 100rpx;
}
.item-navimg1.data-v-c37442b0 {
  width: 100rpx;
  height: 100rpx;
  margin-left: 260rpx;
}