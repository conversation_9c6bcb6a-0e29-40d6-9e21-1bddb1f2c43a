/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.head_top {
  width: 100%;
  height: var(--status-bar-height);
  background-color: #ffffff;
  height: 24rpx;
}
.nav .iconfont.icon-right_arrow {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}
.nav .navbar-box {
  padding: 0 28rpx 24rpx 30rpx;
  background-color: #fff;
}
.nav .search-box {
  height: 60rpx;
  background: #f7f7f7;
  border-radius: 200rpx;
  padding: 0 40rpx 0 34rpx;
  box-sizing: border-box;
}
.nav .search-box .icon-sousuo.icon {
  font-size: 28rpx;
  color: #999;
  margin-right: 20rpx;
}
.loading {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  right: 0;
  background: #fff;
}
.loading image {
  width: 260rpx;
  height: 260rpx;
  position: relative;
  margin-top: -200rpx;
}
.main {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}
.nav {
  width: 100%;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}
.nav .header.tableHead {
  align-items: center;
}
.nav .header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
  background-color: #ffffff;
}
.nav .header .left {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.nav .header .left .icon-dizhi {
  color: #999;
  font-size: 30rpx;
}
.nav .header .left .store-name {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 34rpx;
  margin-bottom: 10rpx;
}
.nav .header .left .store-name .icon-jiantou1 {
  margin-left: 10rpx;
  line-height: 100%;
  color: #333;
  font-size: 28rpx;
}
.nav .header .left .icon-diliweizhi.icon {
  line-height: 100%;
  color: #999;
  font-size: 30rpx;
  margin-right: 10rpx;
}
.nav .header .btn-tableorder {
  width: 152rpx;
  height: 52rpx;
  border-radius: 26rpx;
  font-size: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #111;
}
[data-theme=theme0] .nav .header .btn-tableorder {
  background-color: #fef9e3 !important;
}
[data-theme=theme1] .nav .header .btn-tableorder {
  background-color: #f9e8e9 !important;
}
[data-theme=theme2] .nav .header .btn-tableorder {
  background-color: #daf4fd !important;
}
[data-theme=theme3] .nav .header .btn-tableorder {
  background-color: #e5f6ec !important;
}
[data-theme=theme4] .nav .header .btn-tableorder {
  background-color: #fef9e3 !important;
}
[data-theme=theme5] .nav .header .btn-tableorder {
  background-color: #f5f0ea !important;
}
[data-theme=theme6] .nav .header .btn-tableorder {
  background-color: #f1f1f9 !important;
}
.nav .header .btn-tableorder .icon.iconfont {
  color: #111;
  font-size: 24rpx;
}
.nav .header .dinner-right {
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 4rpx 5rpx;
  background: #f6f6f6;
}
.nav .header .dinner-right .dinner_type {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 82rpx;
  height: 52rpx;
  border-radius: 25rpx;
  font-size: 24rpx;
  background: #f6f6f6;
  font-weight: bold;
  color: #9a9a9a;
  box-sizing: border-box;
}
.nav .header .dinner-right .dinner_type.active {
  color: #ffffff;
}
[data-theme=theme0] .nav .header .dinner-right .dinner_type.active {
  background-color: #fee238 !important;
}
[data-theme=theme1] .nav .header .dinner-right .dinner_type.active {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .nav .header .dinner-right .dinner_type.active {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .nav .header .dinner-right .dinner_type.active {
  background-color: #00a348 !important;
}
[data-theme=theme4] .nav .header .dinner-right .dinner_type.active {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .nav .header .dinner-right .dinner_type.active {
  background-color: #b99970 !important;
}
[data-theme=theme6] .nav .header .dinner-right .dinner_type.active {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .nav .header .dinner-right .dinner_type.active {
  color: #111111 !important;
}
[data-theme=theme1] .nav .header .dinner-right .dinner_type.active {
  color: #ffffff !important;
}
[data-theme=theme2] .nav .header .dinner-right .dinner_type.active {
  color: #ffffff !important;
}
[data-theme=theme3] .nav .header .dinner-right .dinner_type.active {
  color: #ffffff !important;
}
[data-theme=theme4] .nav .header .dinner-right .dinner_type.active {
  color: #ffffff !important;
}
[data-theme=theme5] .nav .header .dinner-right .dinner_type.active {
  color: #fff !important;
}
[data-theme=theme6] .nav .header .dinner-right .dinner_type.active {
  color: #ffffff !important;
}
.nav .reduce_list {
  display: flex;
  flex-wrap: wrap;
  box-sizing: border-box;
  flex: 1;
}
.nav .reduce_list .reduce_notice {
  padding: 0 14rpx;
  font-size: 22rpx;
  line-height: 30rpx;
  height: 30rpx;
  margin-right: 12rpx;
  color: #666;
}
.nav .reduce_list .reduce_notice .icon-gonggao2 {
  font-size: 22rpx;
  line-height: 1;
  margin-right: 10rpx;
}
[data-theme=theme0] .nav .reduce_list .reduce_notice .icon-gonggao2 {
  color: #fa301b !important;
}
[data-theme=theme1] .nav .reduce_list .reduce_notice .icon-gonggao2 {
  color: #b91d32 !important;
}
[data-theme=theme2] .nav .reduce_list .reduce_notice .icon-gonggao2 {
  color: #fb5032 !important;
}
[data-theme=theme3] .nav .reduce_list .reduce_notice .icon-gonggao2 {
  color: #e6444d !important;
}
[data-theme=theme4] .nav .reduce_list .reduce_notice .icon-gonggao2 {
  color: #fd8103 !important;
}
[data-theme=theme5] .nav .reduce_list .reduce_notice .icon-gonggao2 {
  color: #fe4b4e !important;
}
[data-theme=theme6] .nav .reduce_list .reduce_notice .icon-gonggao2 {
  color: #fb5032 !important;
}
.nav .reduce_list .reduce_item {
  padding: 8rpx 14rpx;
  border: 2rpx solid;
  font-size: 22rpx;
  border-radius: 5rpx;
  display: inline-block;
  margin-right: 12rpx;
  margin-bottom: 20rpx;
  line-height: 1;
}
[data-theme=theme0] .nav .reduce_list .reduce_item {
  border-color: #fa301b !important;
}
[data-theme=theme1] .nav .reduce_list .reduce_item {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .nav .reduce_list .reduce_item {
  border-color: #fb5032 !important;
}
[data-theme=theme3] .nav .reduce_list .reduce_item {
  border-color: #e6444d !important;
}
[data-theme=theme4] .nav .reduce_list .reduce_item {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .nav .reduce_list .reduce_item {
  border-color: #fe4b4e !important;
}
[data-theme=theme6] .nav .reduce_list .reduce_item {
  border-color: #fb5032 !important;
}
[data-theme=theme0] .nav .reduce_list .reduce_item {
  color: #fa301b !important;
}
[data-theme=theme1] .nav .reduce_list .reduce_item {
  color: #b91d32 !important;
}
[data-theme=theme2] .nav .reduce_list .reduce_item {
  color: #fb5032 !important;
}
[data-theme=theme3] .nav .reduce_list .reduce_item {
  color: #e6444d !important;
}
[data-theme=theme4] .nav .reduce_list .reduce_item {
  color: #fd8103 !important;
}
[data-theme=theme5] .nav .reduce_list .reduce_item {
  color: #fe4b4e !important;
}
[data-theme=theme6] .nav .reduce_list .reduce_item {
  color: #fb5032 !important;
}
.nav .active-list {
  padding: 0 20rpx;
  background-color: #ffffff;
  padding-top: 30rpx;
  padding-bottom: 20rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.nav .active-list .icon-jiantou1.icon {
  flex-shrink: 0;
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}
.content {
  flex: 1;
  overflow: hidden;
  width: 100%;
  display: flex;
}
.content .menus {
  width: 164rpx;
  height: 100%;
  overflow: hidden;
}
.content .menus .category-wrapper {
  width: 100%;
  height: 100%;
}
.content .menus .category-wrapper .menu {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 164rpx;
  padding: 38rpx 0 30rpx 0;
  font-size: 24rpx;
  color: #333;
  position: relative;
  flex-direction: column;
}
.content .menus .category-wrapper .menu.current {
  background-color: #ffffff;
  color: #333;
  font-weight: bold;
  position: relative;
}
.content .menus .category-wrapper .menu .menu-imgs {
  width: 42rpx;
  height: 42rpx;
  display: block;
  margin-bottom: 16rpx;
}
.content .menus .category-wrapper .menu .menu-cartNum {
  width: 32rpx;
  height: 32rpx;
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 5rpx;
  top: 14rpx;
  font-weight: normal;
}
[data-theme=theme0] .content .menus .category-wrapper .menu .menu-cartNum {
  background-color: #fa301b !important;
}
[data-theme=theme1] .content .menus .category-wrapper .menu .menu-cartNum {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .content .menus .category-wrapper .menu .menu-cartNum {
  background-color: #fb5032 !important;
}
[data-theme=theme3] .content .menus .category-wrapper .menu .menu-cartNum {
  background-color: #e6444d !important;
}
[data-theme=theme4] .content .menus .category-wrapper .menu .menu-cartNum {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .content .menus .category-wrapper .menu .menu-cartNum {
  background-color: #fe4b4e !important;
}
[data-theme=theme6] .content .menus .category-wrapper .menu .menu-cartNum {
  background-color: #fb5032 !important;
}
.content .menus .category-wrapper .menu-bottom {
  width: 100%;
  height: 250rpx;
}
.content .goods {
  flex: 1;
  overflow: hidden;
  background-color: #ffffff;
}
.content .goods .goods-wrapper {
  width: 100%;
  box-sizing: border-box;
}
.content .goods .goods-wrapper .list {
  width: 100%;
  font-size: 28rpx;
  padding-bottom: 50vh;
}
.content .goods .goods-wrapper .list .category {
  width: 100%;
  padding: 0 24rpx 0 18rpx;
  box-sizing: border-box;
}
.content .goods .goods-wrapper .list .category .title {
  display: flex;
  align-items: center;
  color: #333;
  font-weight: bold;
  font-size: 26rpx;
}
.content .goods .goods-wrapper .list .category .title .icon {
  width: 38rpx;
  height: 38rpx;
  margin-left: 10rpx;
}
.content .goods .goods-wrapper .list .goods-items {
  display: flex;
  flex-direction: column;
  padding-bottom: -30rpx;
}
.content .goods .goods-wrapper .list .goods-items .good:last-child {
  border: none;
}
.content .goods .goods-wrapper .list .goods-items .good {
  display: flex;
  align-items: center;
  padding: 18rpx 0 23rpx 0;
  border-bottom: 1px solid #eee;
  /* 售罄 */
}
.content .goods .goods-wrapper .list .goods-items .good .image-boxs {
  position: relative;
  margin-right: 20rpx;
}
.content .goods .goods-wrapper .list .goods-items .good .image-boxs .image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
}
.content .goods .goods-wrapper .list .goods-items .good .image-boxs .sallsell-out {
  position: absolute;
  left: 0;
  top: 0;
  width: 160rpx;
  height: 160rpx;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.35);
  border-radius: 20rpx;
}
.content .goods .goods-wrapper .list .goods-items .good .image-boxs .sallsell-out .sallsell-out-btn {
  width: 122rpx;
  height: 46rpx;
  background-color: rgba(0, 0, 0, 0.65);
  border-radius: 46rpx;
  font-size: 22rpx;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.content .goods .goods-wrapper .list .goods-items .good .product-info {
  flex: 1;
  min-height: 160rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  padding-top: 10rpx;
  box-sizing: border-box;
}
.content .goods .goods-wrapper .list .goods-items .good .product-info .discount {
  display: inline-block;
  font-size: 20rpx;
  color: #fff;
  border-radius: 5rpx;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  background: #fa301b;
  margin-right: 5rpx;
  font-weight: 400;
}
.content .goods .goods-wrapper .list .goods-items .good .product-info .name {
  font-size: 26rpx;
  font-weight: 800;
  color: #333;
  margin-bottom: 16rpx;
}
.content .goods .goods-wrapper .list .goods-items .good .product-info .tips {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 22rpx;
  color: #28282850;
}
.content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .price {
  font-size: 30rpx;
  font-weight: 600;
}
[data-theme=theme0] .content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .price {
  color: #fa301b !important;
}
[data-theme=theme1] .content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .price {
  color: #b91d32 !important;
}
[data-theme=theme2] .content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .price {
  color: #fb5032 !important;
}
[data-theme=theme3] .content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .price {
  color: #e6444d !important;
}
[data-theme=theme4] .content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .price {
  color: #111111 !important;
}
[data-theme=theme5] .content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .price {
  color: #111111 !important;
}
[data-theme=theme6] .content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .price {
  color: #111111 !important;
}
.content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .linprice {
  font-size: 22rpx;
  font-weight: 300;
  color: #999999;
  text-decoration: line-through;
  margin-left: 4rpx;
}
.content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .btn-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  /* 列表 */
}
.content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .btn-group .add-image {
  width: 48rpx;
  height: 48rpx;
  display: block;
}
.content .goods .goods-wrapper .list .goods-items .good .product-info .price_and_action .btn-group .number {
  font-size: 28rpx;
  width: 48rpx;
  height: 48rpx;
  text-align: center;
  line-height: 48rpx;
  font-size: 30rpx;
}
/* 购物车弹窗 */
.good-detail-modal {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.good-detail-modal .cover {
  width: 100%;
  height: 352rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  box-sizing: border-box;
}
.good-detail-modal .cover .image {
  width: 100%;
  height: 352rpx;
}
.good-detail-modal .cover .btn-group {
  position: absolute;
  right: 10rpx;
  top: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.good-detail-modal .cover .btn-group image {
  width: 80rpx;
  height: 80rpx;
}
.good-detail-modal .detail {
  width: 100%;
  min-height: 1vh;
  max-height: calc(93vh - 478rpx - 255rpx);
}
.good-detail-modal .detail .wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-top: 2rpx solid #eee;
}
.good-detail-modal .detail .wrapper .basic {
  padding: 0 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.good-detail-modal .detail .wrapper .basic .name {
  font-size: 28rpx;
  color: #5a5b5c;
  margin-bottom: 10rpx;
}
.good-detail-modal .detail .wrapper .basic .tips {
  font-size: 24rpx;
  color: #878889;
}
.cart-poptitle {
  position: fixed;
  z-index: 2;
  width: 702rpx;
  bottom: 45rpx;
  left: 0;
  right: 0;
  margin: auto;
  height: 114rpx;
  padding-bottom: 69rpx;
  box-sizing: border-box;
  border-radius: 25rpx 25rpx 1rpx 1rpx;
  color: #333;
  font-size: 22rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
[data-theme=theme0] .cart-poptitle {
  background-color: #fef9e3 !important;
}
[data-theme=theme1] .cart-poptitle {
  background-color: #f9e8e9 !important;
}
[data-theme=theme2] .cart-poptitle {
  background-color: #fefbea !important;
}
[data-theme=theme3] .cart-poptitle {
  background-color: #e1f5eb !important;
}
[data-theme=theme4] .cart-poptitle {
  background-color: #fef9e3 !important;
}
[data-theme=theme5] .cart-poptitle {
  background-color: #fefbea !important;
}
[data-theme=theme6] .cart-poptitle {
  background-color: #fefbea !important;
}
[data-theme=theme0] .cart-poptitle .full-price {
  color: #fa301b !important;
}
[data-theme=theme1] .cart-poptitle .full-price {
  color: #b91d32 !important;
}
[data-theme=theme2] .cart-poptitle .full-price {
  color: #fb5032 !important;
}
[data-theme=theme3] .cart-poptitle .full-price {
  color: #e6444d !important;
}
[data-theme=theme4] .cart-poptitle .full-price {
  color: #fa301b !important;
}
[data-theme=theme5] .cart-poptitle .full-price {
  color: #fe4b4e !important;
}
[data-theme=theme6] .cart-poptitle .full-price {
  color: #fb5032 !important;
}
.cart-box {
  position: fixed;
  width: 702rpx;
  bottom: 18rpx;
  left: 0;
  right: 0;
  margin: auto;
  height: 96rpx;
  border-radius: 48rpx;
  background-color: #212526;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  z-index: 990;
}
.cart-box .cart-view {
  width: 64rpx;
  height: 64rpx;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 64rpx;
}
.cart-box .pay-btn {
  width: 185rpx;
  height: 96rpx;
  font-size: 30rpx;
  border-radius: 0 44rpx 44rpx 0;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
}
[data-theme=theme0] .cart-box .pay-btn {
  color: #111111 !important;
}
[data-theme=theme1] .cart-box .pay-btn {
  color: #ffffff !important;
}
[data-theme=theme2] .cart-box .pay-btn {
  color: #ffffff !important;
}
[data-theme=theme3] .cart-box .pay-btn {
  color: #ffffff !important;
}
[data-theme=theme4] .cart-box .pay-btn {
  color: #ffffff !important;
}
[data-theme=theme5] .cart-box .pay-btn {
  color: #fff !important;
}
[data-theme=theme6] .cart-box .pay-btn {
  color: #ffffff !important;
}
[data-theme=theme0] .cart-box .pay-btn {
  background-color: #fee238 !important;
}
[data-theme=theme1] .cart-box .pay-btn {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .cart-box .pay-btn {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .cart-box .pay-btn {
  background-color: #00a348 !important;
}
[data-theme=theme4] .cart-box .pay-btn {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .cart-box .pay-btn {
  background-color: #b99970 !important;
}
[data-theme=theme6] .cart-box .pay-btn {
  background-color: #a4a4d6 !important;
}
.cart-box .pay-btn[disabled]:not([type]) {
  color: #999 !important;
  background: #212526 !important;
}
.cart-box .btn-gray {
  color: #999 !important;
  background: #212526 !important;
  background-color: #212526 !important;
  height: 96rpx;
  line-height: 96rpx;
}
.cart-box .mark {
  padding-left: 46rpx;
  margin-right: 30rpx;
  position: relative;
}
.cart-box .mark .tag {
  background-color: #ff0000;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 26rpx;
  position: absolute;
  right: -20rpx;
  top: 0;
  border-radius: 18rpx;
  min-width: 36rpx;
  height: 36rpx;
  box-sizing: border-box;
  opacity: 0.9;
}
.cart-box .price {
  flex: 1;
  font-size: 36rpx;
  color: #fff;
}
.iconsami-select {
  font-size: 20rpx;
}
.iconsami-select.icon-jian {
  font-size: 20rpx;
}
[data-theme=theme0] .iconsami-select.icon-jian {
  border-color: #fee238 !important;
}
[data-theme=theme1] .iconsami-select.icon-jian {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .iconsami-select.icon-jian {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .iconsami-select.icon-jian {
  border-color: #00a348 !important;
}
[data-theme=theme4] .iconsami-select.icon-jian {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .iconsami-select.icon-jian {
  border-color: #b99970 !important;
}
[data-theme=theme6] .iconsami-select.icon-jian {
  border-color: #a4a4d6 !important;
}
[data-theme=theme0] .iconsami-select.icon-jian {
  color: #fee238 !important;
}
[data-theme=theme1] .iconsami-select.icon-jian {
  color: #b91d32 !important;
}
[data-theme=theme2] .iconsami-select.icon-jian {
  color: #09b4f1 !important;
}
[data-theme=theme3] .iconsami-select.icon-jian {
  color: #00a348 !important;
}
[data-theme=theme4] .iconsami-select.icon-jian {
  color: #fd8103 !important;
}
[data-theme=theme5] .iconsami-select.icon-jian {
  color: #b99970 !important;
}
[data-theme=theme6] .iconsami-select.icon-jian {
  color: #a4a4d6 !important;
}
.iconsami-select.icon-jia {
  font-size: 20rpx;
}
[data-theme=theme0] .iconsami-select.icon-jia {
  border-color: #fee238 !important;
}
[data-theme=theme1] .iconsami-select.icon-jia {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .iconsami-select.icon-jia {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .iconsami-select.icon-jia {
  border-color: #00a348 !important;
}
[data-theme=theme4] .iconsami-select.icon-jia {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .iconsami-select.icon-jia {
  border-color: #b99970 !important;
}
[data-theme=theme6] .iconsami-select.icon-jia {
  border-color: #a4a4d6 !important;
}
[data-theme=theme0] .iconsami-select.icon-jia {
  background-color: #fee238 !important;
}
[data-theme=theme1] .iconsami-select.icon-jia {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .iconsami-select.icon-jia {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .iconsami-select.icon-jia {
  background-color: #00a348 !important;
}
[data-theme=theme4] .iconsami-select.icon-jia {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .iconsami-select.icon-jia {
  background-color: #b99970 !important;
}
[data-theme=theme6] .iconsami-select.icon-jia {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .iconsami-select.icon-jia {
  color: #111111 !important;
}
[data-theme=theme1] .iconsami-select.icon-jia {
  color: #ffffff !important;
}
[data-theme=theme2] .iconsami-select.icon-jia {
  color: #ffffff !important;
}
[data-theme=theme3] .iconsami-select.icon-jia {
  color: #ffffff !important;
}
[data-theme=theme4] .iconsami-select.icon-jia {
  color: #ffffff !important;
}
[data-theme=theme5] .iconsami-select.icon-jia {
  color: #fff !important;
}
[data-theme=theme6] .iconsami-select.icon-jia {
  color: #ffffff !important;
}
.container {
  padding: 0;
}
.off_business {
  position: fixed;
  width: 550rpx;
  opacity: 0.8;
  color: #fff;
  line-height: 50rpx;
  text-align: center;
  z-index: 98;
}
[data-theme=theme0] .off_business {
  background-color: #fee238 !important;
}
[data-theme=theme1] .off_business {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .off_business {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .off_business {
  background-color: #00a348 !important;
}
[data-theme=theme4] .off_business {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .off_business {
  background-color: #b99970 !important;
}
[data-theme=theme6] .off_business {
  background-color: #a4a4d6 !important;
}
.good_basic {
  padding: 0 20rpx 30rpx;
  display: flex;
  flex-direction: column;
}
.good_basic .name {
  margin-top: 40rpx;
  font-size: 35rpx;
  color: #282828;
  font-weight: 800;
  margin-bottom: 10rpx;
}
.good_basic .selling_point {
  width: 589rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #28282850;
  line-height: 30rpx;
  word-break: break-all;
}
.good_basic .tips {
  font-size: 24rpx;
  color: #878889;
}
.add-to-cart-btn {
  width: 701rpx;
  height: 83rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  border-radius: 29rpx;
  margin: 0 auto;
  margin-bottom: 35rpx;
}
[data-theme=theme0] .add-to-cart-btn {
  background-color: #fee238 !important;
}
[data-theme=theme1] .add-to-cart-btn {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .add-to-cart-btn {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .add-to-cart-btn {
  background-color: #00a348 !important;
}
[data-theme=theme4] .add-to-cart-btn {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .add-to-cart-btn {
  background-color: #b99970 !important;
}
[data-theme=theme6] .add-to-cart-btn {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .add-to-cart-btn {
  color: #ffffff !important;
}
[data-theme=theme1] .add-to-cart-btn {
  color: #ffffff !important;
}
[data-theme=theme2] .add-to-cart-btn {
  color: #ffffff !important;
}
[data-theme=theme3] .add-to-cart-btn {
  color: #ffffff !important;
}
[data-theme=theme4] .add-to-cart-btn {
  color: #ffffff !important;
}
[data-theme=theme5] .add-to-cart-btn {
  color: #ffffff !important;
}
[data-theme=theme6] .add-to-cart-btn {
  color: #ffffff !important;
}
.cart-popup .top {
  color: #28282880;
  padding: 28rpx 30rpx 10rpx 30rpx;
  font-size: 22rpx;
  text-align: right;
}
.cart-popup .top .icon.icon-shanchu1 {
  font-size: 30rpx;
  color: #999;
  margin-right: 10rpx;
}
.cart-popup .cart-list {
  background-color: #ffffff;
  width: 100%;
  overflow: hidden;
  min-height: 1vh;
  max-height: 60vh;
}
.cart-popup .cart-list .wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0 30rpx;
  margin-bottom: 0;
  box-sizing: border-box;
}
.cart-popup .cart-list .wrapper .item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  position: relative;
}
.cart-popup .cart-list .wrapper .item::after {
  content: " ";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #eeeeee;
  height: 2rpx;
  transform: scaleY(0.6);
}
.cart-popup .cart-list .wrapper .item .cart-image {
  width: 136rpx;
  height: 136rpx;
  border-radius: 20rpx;
  margin-right: 20rpx;
}
.cart-popup .cart-list .wrapper .item .cart-image image {
  width: 136rpx;
  height: 136rpx;
  border-radius: 20rpx;
}
.cart-popup .cart-list .wrapper .item .left {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  justify-content: space-between;
  margin-right: 30rpx;
}
.cart-popup .cart-list .wrapper .item .left .name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
}
.cart-popup .cart-list .wrapper .item .left .props {
  color: rgba(40, 40, 40, 0.5);
  font-size: 22rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.cart-popup .cart-list .wrapper .item .center {
  font-size: 24rpx;
}
[data-theme=theme0] .cart-popup .cart-list .wrapper .item .center {
  color: #fa301b !important;
}
[data-theme=theme1] .cart-popup .cart-list .wrapper .item .center {
  color: #b91d32 !important;
}
[data-theme=theme2] .cart-popup .cart-list .wrapper .item .center {
  color: #fb5032 !important;
}
[data-theme=theme3] .cart-popup .cart-list .wrapper .item .center {
  color: #e6444d !important;
}
[data-theme=theme4] .cart-popup .cart-list .wrapper .item .center {
  color: #111111 !important;
}
[data-theme=theme5] .cart-popup .cart-list .wrapper .item .center {
  color: #111111 !important;
}
[data-theme=theme6] .cart-popup .cart-list .wrapper .item .center {
  color: #111111 !important;
}
.cart-popup .cart-list .wrapper .item .right {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* 购物车 */
}
.cart-popup .cart-list .wrapper .item .right .btn-image {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: block;
}
.cart-popup .cart-list .wrapper .item .right .number {
  font-size: 30rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  min-width: 48rpx;
  height: 48rpx;
  text-align: center;
  line-height: 48rpx;
}
.top-title {
  position: absolute;
  top: -60rpx;
  width: 100%;
  border-radius: 20rpx 20rpx 1rpx 1rpx;
  height: 60rpx;
  line-height: 60rpx;
  color: #282828;
  font-size: 24rpx;
}
[data-theme=theme0] .top-title {
  background-color: #fef9e3 !important;
}
[data-theme=theme1] .top-title {
  background-color: #f9e8e9 !important;
}
[data-theme=theme2] .top-title {
  background-color: #fefbea !important;
}
[data-theme=theme3] .top-title {
  background-color: #e1f5eb !important;
}
[data-theme=theme4] .top-title {
  background-color: #fef9e3 !important;
}
[data-theme=theme5] .top-title {
  background-color: #fefbea !important;
}
[data-theme=theme6] .top-title {
  background-color: #fefbea !important;
}
[data-theme=theme0] .top-title .full-price {
  color: #fa301b !important;
}
[data-theme=theme1] .top-title .full-price {
  color: #b91d32 !important;
}
[data-theme=theme2] .top-title .full-price {
  color: #fb5032 !important;
}
[data-theme=theme3] .top-title .full-price {
  color: #e6444d !important;
}
[data-theme=theme4] .top-title .full-price {
  color: #fa301b !important;
}
[data-theme=theme5] .top-title .full-price {
  color: #fe4b4e !important;
}
[data-theme=theme6] .top-title .full-price {
  color: #fb5032 !important;
}
.top-title .mj {
  width: 100%;
  text-align: center;
}
.pop-isclose {
  position: fixed;
  z-index: 994;
  width: 100%;
  height: 95rpx;
  font-size: 28rpx;
  color: #fff;
  left: 0;
  bottom: 0;
  background: #212526;
  display: flex;
  justify-content: center;
  align-items: center;
}
.stop-click {
  position: fixed;
  z-index: 10001;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
}