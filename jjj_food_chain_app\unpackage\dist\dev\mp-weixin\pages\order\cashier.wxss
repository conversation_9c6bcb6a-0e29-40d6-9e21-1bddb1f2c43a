/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.icon-box .icon.iconfont.icon-xuanze {
  font-size: 36rpx;
}
.buy-checkout-top {
  padding: 100rpx 0;
}
.buy-checkout {
  background-color: #FFFFFF;
  margin: 0 24rpx;
  border-radius: 6rpx;
}
.buy-checkout .item {
  border-bottom: 1px solid #eee;
  padding-left: 0;
  padding-right: 0;
  margin: 0 20rpx;
}
.buy-checkout .item:last-child {
  border: none;
}
.bottom-btn {
  position: fixed;
  bottom: 0;
  width: 710rpx;
  margin: 20rpx;
  box-sizing: border-box;
  font-size: 30rpx;
  height: 96rpx;
  border-radius: 72rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
[data-theme=theme0] .bottom-btn {
  background-color: #fee238 !important;
}
[data-theme=theme1] .bottom-btn {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .bottom-btn {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .bottom-btn {
  background-color: #00a348 !important;
}
[data-theme=theme4] .bottom-btn {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .bottom-btn {
  background-color: #b99970 !important;
}
[data-theme=theme6] .bottom-btn {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .bottom-btn {
  color: !important;
}
[data-theme=theme1] .bottom-btn {
  color: !important;
}
[data-theme=theme2] .bottom-btn {
  color: !important;
}
[data-theme=theme3] .bottom-btn {
  color: !important;
}
[data-theme=theme4] .bottom-btn {
  color: !important;
}
[data-theme=theme5] .bottom-btn {
  color: !important;
}
[data-theme=theme6] .bottom-btn {
  color: !important;
}