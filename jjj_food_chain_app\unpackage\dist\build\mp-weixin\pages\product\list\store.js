"use strict";const t=require("../../../common/vendor.js"),e=require("../../../common/utils.js"),i=require("../../../common/assets.js"),o={components:{popupLayer:()=>"../../../components/popup-layer/popup-layer.js",prospec:()=>"./popup/spec.js"},data:()=>({loadingClock:!1,isSearch:!1,isLogin:!1,isDetail:!1,isLoading:!0,goods:[],supplier:{name:"",business_status:0},loading:!0,currentCateId:0,cateScrollTop:0,menuScrollIntoView:"",cart:[],category:{},cartPopupVisible:!1,sizeCalcState:!1,listData:[],productList:[],productModel:{},clock:!1,cart_total_num:0,cart_product_id:0,total_price:0,total_bag_price:0,cart_list:[],orderType:"takein",takeout_address:{},phoneHeight:0,scrollviewHigh:0,delivery_time:["00:00","00:00"],store_time:["00:00","00:00"],officeTime:{now:0,delivery_start:0,delivery_end:0,store_start:0,store_end:0},addclock:!1,longitude:0,latitude:0,bag_type:1,shop_supplier_id:0,dinner_type:30,cart_type:1,table_id:0,order_id:0,addorder_id:0,discountInfo:[],reduce:{},reduce_diff_value:0,line_price:0,isFirst:!0,store_set:[],num:1,table_detail:null,options:{},settle_type:0,status:"",meal_num:"",isShopDetail:!1,supplierDetail:null}),onLoad(i){let o=this,a=e.utils.getSceneData(i);o.options=i,o.shop_supplier_id=i.shop_supplier_id?i.shop_supplier_id:a.sid,o.table_id=i.table_id?i.table_id:a.tid,o.table_id||(o.table_id=0),0==o.table_id&&(o.shop_supplier_id=t.index.getStorageSync("selectedId")?t.index.getStorageSync("selectedId"):0),o.num=i.num||0,o.meal_num=i.meal_num||0,o.status=i.status,o.addorder_id=i.order_id||0,t.index.setNavigationBarTitle({title:0==o.table_id?"快餐模式":"堂食点餐"})},onShow(){this.isDetail=!1,this.getUserInfo()},computed:{menuCartNum(){return t=>this.cart.reduce(((e,i)=>i.cate_id===t?e+i.number:e),0)}},methods:{getUserInfo(){let t=this;t.loading=!0,t.isLogin=!1,t._get("index/userInfo",{},(function(e){e.data&&e.data.user&&(t.isLogin=!0),t.init()}))},init(){this.addclock=!1,this.category={},this.clock=!1,this.loading=!0,this.isLoading=!0,this.productList=[],this.sizeCalcState=!1,this.getCategory(!0)},goBack(){this.table_id>0&&1==this.status||t.index.navigateBack({delta:1})},scrollInit(){let e=this;t.index.getSystemInfo({success(i){e.phoneHeight=i.windowHeight,t.index.createSelectorQuery().select(".nav").boundingClientRect((t=>{let i=e.phoneHeight-t.height;e.scrollviewHigh=i})).exec()}})},takout(){"takeout"!=this.orderType&&(this.orderType="takeout",this.dinner_type=10,this.init())},takein(){"takein"!=this.orderType&&(this.orderType="takein",this.dinner_type=20,this.init())},getCategory(t){let e=this;this.sizeCalcState=!1,e._get("product.category/index",{type:1,shop_supplier_id:e.shop_supplier_id,longitude:0,latitude:0,table_id:e.table_id,delivery:"takeout"==e.orderType?30:40,order_type:0==e.table_id?1:2,cart_type:e.cart_type||1},(function(i){e.supplier=i.data.supplier,e.discountInfo=i.data.discountInfo,e.productList=i.data.list,e.currentCateId||(e.currentCateId=e.productList[0].category_id),e.store_set=i.data.supplier.store_set,0!=e.table_id&&(e.orderType="takein"),e.shop_supplier_id=i.data.supplier.shop_supplier_id,e.bag_type=i.data.supplier.storebag_type,"takein"==e.orderType&&(e.bag_type=1),e.loading=!1,e.isLoading=!1,e.$nextTick((function(){e.scrollInit()})),e.isLogin&&t&&e.getCart()}),(function(){e.gotoPage("/pages/index/index")}))},reCart(t){let e=this;e.loadingClock=!1,e.cart_total_num=t.data.cartInfo.cart_total_num,e.total_price=t.data.cartInfo.total_pay_price,e.line_price=t.data.cartInfo.total_line_money,e.total_bag_price=t.data.cartInfo.total_bag_price,e.reduce=t.data.cartInfo.reduce,e.reduce_diff_value=t.data.cartInfo.reduce_diff_value,e.cart_list&&""!=e.cart_list||(e.cartPopupVisible=!1)},addCart(e){let i=this;if(20==e.spec_types)return void i.gotoDetail(e);if(i.addclock)return;if(0!=e.limit_num&&e.limit_num<=e.cart_num)return void t.index.showToast({icon:"none",title:"超过限购数量"});if(e.product_stock<=0||e.product_stock<=e.cart_num)return void t.index.showToast({icon:"none",title:"没有更多库存了"});let o={product_id:e.product_id,product_num:1,product_sku_id:e.sku[0].product_sku_id,attr:"",feed:"",describe:"",price:e.sku[0].product_price,bag_price:e.sku[0].bag_price,shop_supplier_id:i.shop_supplier_id,table_id:i.table_id,cart_type:1,dinner_type:i.dinner_type,product_price:e.sku[0].line_price,delivery:"takeout"==i.orderType?30:40};i.addclock=!0;t.index.showLoading({title:"",mask:!0}),i._post("order.cart/add",o,(function(e){i.reCart(e),t.index.hideLoading(),i.addclock=!1,i.getCategory(!1)}),(function(e){t.index.hideLoading(),i.addclock=!1}))},reduceFunc(e){let i=this;if(i.addclock||e.cart_num<=0)return;if(20==e.spec_types)return void i.openCartPopup(e.product_id);let o=e.product_id;i.addclock=!0;t.index.showLoading({title:"",mask:!0}),i._post("order.cart/productSub",{product_id:o,type:"down",cart_type:1,shop_supplier_id:i.shop_supplier_id,table_id:i.table_id,dinner_type:i.dinner_type,delivery:"takeout"==i.orderType?30:40},(function(e){t.index.hideLoading(),i.reCart(e),i.addclock=!1,i.getCategory(!1)}),(function(){t.index.hideLoading(),i.addclock=!1}))},getCartLoading(){let e=this,i={shop_supplier_id:e.shop_supplier_id,cart_type:1,delivery:"takeout"==e.orderType?30:40,product_id:e.cart_product_id,table_id:e.table_id||0};e._get("order.cart/lists",i,(function(i){!function(i){t.index.hideLoading(),e.isLoading=!0,e.reCart(i),e.cart_list=i.data.productList}(i)}),(e=>{t.index.hideLoading()}))},getCart(){let e=this;if(e.isLogin)return new Promise(((i,o)=>{let a={shop_supplier_id:e.shop_supplier_id,cart_type:1,delivery:"takeout"==e.orderType?30:40,product_id:e.cart_product_id,table_id:e.table_id||0};t.index.showLoading({title:"",mask:!0}),e._get("order.cart/lists",a,(function(o){!function(i){t.index.hideLoading(),e.isLoading=!0,e.reCart(i),e.cart_list=i.data.productList}(o),i(!0)}),(e=>{t.index.hideLoading()}))}))},cartAdd(e){let i=this;if(i.addclock)return;i.addclock=!0;let o=e.product_id;t.index.showLoading({title:"",mask:!0}),i._post("order.cart/sub",{product_id:o,total_num:1,cart_id:e.cart_id,type:"up",cart_type:1,delivery:"takeout"==i.orderType?30:40,dinner_type:i.dinner_type,shop_supplier_id:i.shop_supplier_id,table_id:i.table_id},(function(t){i.addclock=!1,i.getCategory(!1),i.getCartLoading()}),(function(){t.index.hideLoading(),i.addclock=!1}))},cartReduce(e){let i=this;if(i.addclock)return;i.addclock=!0;let o=e.product_id;t.index.showLoading({title:"",mask:!0}),i._post("order.cart/sub",{product_id:o,total_num:1,cart_id:e.cart_id,type:"down",cart_type:1,dinner_type:i.dinner_type,shop_supplier_id:i.shop_supplier_id,table_id:i.table_id,delivery:"takeout"==i.orderType?30:40},(function(t){i.addclock=!1,i.getCategory(!1),i.getCartLoading()}),(function(){t.index.hideLoading(),i.addclock=!1}))},handleCartClear(){let e=this;t.index.showModal({title:"提示",content:"确定清空购物车么",success(t){t.confirm?e.clearCart():t.cancel&&console.log("用户点击取消")}})},clearCart(){let t=this,e={shop_supplier_id:t.shop_supplier_id,cart_type:1,table_id:t.table_id||0};t._post("order.cart/delete",e,(function(e){t.cartPopupVisible=!1,t.cart_list=[],t.init()}))},toPay(){this.fastToPay()},fastToPay(){let e=this;e.loadingClock=!0,t.index.showLoading({title:"加载中",mask:!0});let i="takeout"==e.orderType?30:40;e._get("order.cart/lists",{shop_supplier_id:e.shop_supplier_id,cart_type:1,delivery:i,table_id:e.table_id||0},(function(o){e.reCart(o),e.cart_list=o.data.productList;let a=[];if(e.cart_list.forEach((t=>{a.push(t.cart_id)})),0==a.length)return t.index.showToast({title:"请选择商品",icon:"none"}),!1;t.index.hideLoading(),t.index.navigateTo({url:"/pages/order/confirm-order?order_type=cart&cart_ids="+a.join(",")+"&delivery="+i+"&shop_supplier_id="+e.shop_supplier_id+"&cart_type=1&dinner_type=30&table_id="+e.table_id})}),(t=>{e.loadingClock=!1}))},addpay(){t.index.showLoading({title:"加载中",mask:!0}),this.addpayFunc()},addpayFunc(){let e=this;e._get("order.cart/lists",{shop_supplier_id:e.shop_supplier_id,cart_type:1,delivery:"takeout"==e.orderType?30:40,table_id:e.table_id||0},(function(i){e.cart_total_num=i.data.cartInfo.cart_total_num,e.total_price=i.data.cartInfo.total_price,e.cart_list=i.data.productList;let o=[];if(e.cart_list.forEach((t=>{o.push(t.cart_id)})),0==o.length)return t.index.showToast({title:"请选择商品",icon:"none"}),!1;t.index.hideLoading();let a="/pages/order/addorder?order_type=cart&cart_ids="+o.join(",")+"&delivery=40&shop_supplier_id="+e.shop_supplier_id+"&cart_type=1&dinner_type=30&table_id="+e.table_id+"&order_id="+e.addorder_id;e.gotoPage(a)}))},gotoDetail(t){let e=this,i="takeout"==e.orderType?30:40;e.productModel={product_id:t.product_id||0,delivery:i,bag_type:e.bag_type||0,dinner_type:e.dinner_type||0,cart_type:e.cart_type||0,table_id:e.table_id||0,shop_supplier_id:e.shop_supplier_id||0},e.isDetail=!0},handleMenuTap(t){let e=this;e.sizeCalcState||e.calcSize(),e.currentCateId=t,e.$nextTick((()=>{e.cateScrollTop=e.productList.find((e=>e.category_id==t)).top,e.cateScrollTop||0==e.cateScrollTop||setTimeout((function(){e.handleMenuTap(t)}),200)}))},handleGoodsScroll({detail:t}){this.sizeCalcState||this.calcSize();const{scrollTop:e}=t;let i=this.productList.filter((t=>t.top-5<=e)).reverse();i.length>0&&(this.currentCateId=i[0].category_id)},calcSize(){let e=0;this.productList.forEach((i=>{t.index.createSelectorQuery().select(`#cate-${i.category_id}`).fields({size:!0},(t=>{i.top=e,null!=t&&(e+=t.height),i.bottom=e})).exec()})),this.sizeCalcState=!0},closeGoodDetailModal(t,e){this.isDetail=!1,this.clock=!1,t&&(this.reCart(e),this.getCategory(!1))},async openCartPopup(t){this.cartPopupVisible||(this.cart_list=[],this.cart_product_id=t,await this.getCart()),this.cartPopupVisible=!this.cartPopupVisible},closeCallBack(){this.cart_product_id=0,this.cart_list=[],this.cartPopupVisible=!1}}};if(!Array){(t.resolveComponent("popup-layer")+t.resolveComponent("prospec"))()}Math;const a=t._export_sfc(o,[["render",function(e,o,a,r,d,c){return t.e({a:t.t(d.supplier.name),b:0==d.table_id},0==d.table_id?{c:t.f(d.store_set,((e,i,o)=>t.e({a:"30"==e},"30"==e?{b:"takeout"==d.orderType?1:"",c:t.o(((...t)=>c.takout&&c.takout(...t)),e)}:{},{d:e}))),d:t.f(d.store_set,((e,i,o)=>t.e({a:"40"==e},"40"==e?{b:"takein"==d.orderType?1:"",c:t.o(((...t)=>c.takein&&c.takein(...t)),e)}:{},{d:e})))}:{},{e:0!=d.table_id&&d.table_detail?1:"",f:t.f(d.productList,((e,i,o)=>t.e({a:0!=e.products.length},0!=e.products.length?t.e({b:e.images},e.images?{c:e.images.file_path}:{},{d:t.t(e.name),e:e.product_num},e.product_num?{f:t.t(e.product_num)}:{},{g:`menu-${e.category_id}`,h:e.category_id===d.currentCateId?1:"",i:t.o((t=>c.handleMenuTap(e.category_id)),i)}):{},{j:i}))),g:t.s("height:"+d.scrollviewHigh+"px;"),h:d.menuScrollIntoView,i:t.f(d.productList,((o,a,r)=>t.e({a:0!=o.products.length},0!=o.products.length?{b:t.t(o.name),c:t.f(o.products,((o,a,r)=>t.e({a:o.product_stock<=0},(o.product_stock,{}),{b:o.product_image,c:t.t(o.product_name),d:t.t(o.selling_point),e:t.t(o.product_price),f:1*o.product_price!=1*o.line_price},1*o.product_price!=1*o.line_price?{g:t.t(1*o.line_price)}:{},{h:0!=o.cart_num},0!=o.cart_num?{i:t.o((t=>c.reduceFunc(o)),a),j:"/static/icon/cart/reduce-"+e.theme()+".png"}:{},{k:0!=o.cart_num},0!=o.cart_num?{l:t.t(o.cart_num)}:{},{m:o.product_stock>0},o.product_stock>0?{n:t.o((t=>c.addCart(o)),a),o:"/static/icon/cart/add-"+e.theme()+".png"}:{},{p:o.product_stock<=0},o.product_stock<=0?{q:i._imports_0$3}:{},{r:t.o((t=>c.gotoDetail(o)),a),s:a}))),d:`cate-${o.category_id}`}:{},{e:a}))),j:t.s("padding-bottom:"+(2*d.scrollviewHigh-238)+"rpx;"),k:t.s("height:"+d.scrollviewHigh+"px;"),l:d.cateScrollTop,m:t.o(((...t)=>c.handleGoodsScroll&&c.handleGoodsScroll(...t))),n:d.cart_total_num},d.cart_total_num?{o:t.t(d.cart_total_num)}:{},{p:d.cart_total_num},d.cart_total_num?{q:t.t(d.total_price),r:t.t(d.line_price)}:{},{s:0!=d.table_id},0!=d.table_id?{t:t.o(((...t)=>c.toPay&&c.toPay(...t)))}:t.e({v:0==d.addorder_id},0==d.addorder_id?{w:t.o(((...t)=>c.toPay&&c.toPay(...t)))}:{}),{x:t.o((t=>c.openCartPopup(0))),y:d.cart_total_num>0},d.cart_total_num>0?t.e({z:d.total_bag_price},d.total_bag_price?{A:t.t(d.total_bag_price)}:{},{B:t.o(((...t)=>c.handleCartClear&&c.handleCartClear(...t))),C:t.f(d.cart_list,((i,o,a)=>t.e({a:i.product_num>0},i.product_num>0?t.e({b:i.image.file_path,c:t.t(i.product.product_name),d:t.t(i.describe),e:t.t(i.price),f:1!=d.bag_type},1!=d.bag_type?{g:t.t(i.bag_price)}:{},{h:t.o((t=>c.cartReduce(i)),o),i:"/static/icon/cart/reduce-"+e.theme()+".png",j:t.t(i.product_num),k:t.o((t=>c.cartAdd(i)),o),l:"/static/icon/cart/add-"+e.theme()+".png"}):{},{m:o}))),D:t.o(c.closeCallBack),E:t.p({direction:"top","show-pop":d.cartPopupVisible})}):{},{F:d.isDetail},d.isDetail?{G:t.o(c.closeGoodDetailModal),H:t.p({productModel:d.productModel})}:{},{I:d.loading},d.loading?{J:i._imports_1$1}:{},{K:e.theme(),L:t.n(e.theme()||"")})}]]);wx.createPage(a);
