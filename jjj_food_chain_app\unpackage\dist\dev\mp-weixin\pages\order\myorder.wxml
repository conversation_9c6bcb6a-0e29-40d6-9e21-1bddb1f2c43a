<view data-theme="{{j}}" class="{{k}}"><view class="top-tabbar d-a-c"><view class="{{a}}" bindtap="{{b}}">当前订单</view><view class="{{c}}" bindtap="{{d}}">历史订单</view></view><view class="order-list"><view wx:for="{{e}}" wx:for-item="item" wx:key="p" class="item" catchtap="{{item.q}}"><view class="d-b-c pb14"><text class="order-tips">{{item.a}}</text><view class="item-dianpu flex-1"><view class="item-d-l mr10"><text wx:if="{{item.b}}" class="gray3 f28 fb text-ellipsis">{{item.c}}</text></view></view><view class="state"><text class="gray9 f24">{{item.d}}</text></view></view><view class="product-list pr d-b-c"><view class="o-a flex-1"><view class="list d-s-c pr100"><block wx:for="{{item.e}}" wx:for-item="img"><view wx:if="{{img.a}}" class="cover mr20" key="{{img.d}}"><image src="{{img.b}}" mode="aspectFit"></image><view class="mt10 tc f24 text-ellipsis">{{img.c}}</view></view></block></view></view><view><view class="theme-price f30">￥<text class="f36">{{item.f}}</text></view><view class="f20 gray6 tr">共{{item.g}}件</view></view></view><text class="shop-name flex-1">下单时间：{{item.h}}</text><view wx:if="{{item.i}}" class="order-bts"><button wx:if="{{item.j}}" class="default" catchtap="{{item.k}}" type="default">取消订单</button><block wx:if="{{item.l}}"><button class="theme-btn fb" catchtap="{{item.m}}">立即支付</button></block><block wx:if="{{item.n}}"><button class="theme-btn fb" catchtap="{{item.o}}">立即支付</button></block></view></view><view wx:if="{{f}}" class="d-c-c d-c p30"><image style="width:268rpx;height:263rpx;margin-top:123rpx" src="{{g}}" mode="aspectFill"></image><view class="f26 gray9">暂无记录</view><view><button class="btn-normal theme-btn" bindtap="{{h}}">立即点单</button></view></view><uni-load-more wx:else u-i="2b3e936c-0" bind:__l="__l" u-p="{{i||''}}"></uni-load-more></view></view>