"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const e=require("./common/vendor.js"),t=require("./common/utils.js"),o=require("./env/config.js"),n=require("./common/onfire.js"),a=require("./common/gotopage.js"),i=require("./utils/request.js"),r=require("./utils/validator.js"),s=require("./store/index.js");Math;const c={onLaunch:function(t){console.log("App Launch"),this.updateManager(),e.wx$1.login(),this.onStartupScene(t.query),this.getTabBarLinks()},onShow:function(){},onHide:function(){},methods:{updateManager:function(){const t=e.index.getUpdateManager();t.onCheckForUpdate((function(o){o.hasUpdate&&t.onUpdateReady((function(o){e.index.showModal({title:"更新提示",content:"新版本已经准备好，即将重启应用",showCancel:!1,success(e){e.confirm&&t.applyUpdate()}})}))})),t.onUpdateFailed((function(t){e.index.showModal({title:"更新提示",content:"检查到有新版本，但下载失败，请检查网络设置",showCancel:!1})}))},onStartupScene(o){let n=t.utils.getSceneData(o),a=o&&o.referee_id;a>0&&(e.index.getStorageSync("referee_id")||(e.index.setStorageSync("referee_id",a),console.log("refereeId="+a)));let i=n.uid;i>0&&(e.index.getStorageSync("referee_id")||e.index.setStorageSync("referee_id",i))},getTabBarLinks(){e.index.request({url:this.config.app_url+"/index.php/api/index/nav",data:{app_id:this.config.app_id},success:t=>{this.$store.commit("changeTheme",2),this.$store.commit("changeMapkey",t.data.data.key);e.index.setTabBarStyle({color:"#333333",selectedColor:"#09B4F1"}),e.index.setTabBarItem({index:0,text:"首页",iconPath:"/static/tab/home.png",selectedIconPath:"/static/tab/home_2.png"}),e.index.setTabBarItem({index:1,text:"订单",iconPath:"/static/tab/order.png",selectedIconPath:"/static/tab/order_2.png"}),e.index.setTabBarItem({index:2,text:"我的",iconPath:"/static/tab/user.png",selectedIconPath:"/static/tab/user_2.png"}),e.index.setStorageSync("theme",2)}})}}},g=()=>"./components/header.js",l=()=>"./components/tabbar/footTabbar.js";function p(){const t=e.createSSRApp(c);return t.component("header-bar",g),t.component("tabBar",l),t.use(s.store),t.config.globalProperties.$store=s.store,t.config.globalProperties.footTabberData={active:"home"},t.config.globalProperties.$fire=new n.OnFire,t.config.globalProperties.config=o.config,t.config.globalProperties.websiteUrl=o.config.app_url,t.config.globalProperties.app_id=o.config.app_id,t.config.globalProperties.h5_addr=o.config.h5_addr,t.config.globalProperties.gotoPage=a.gotopage,t.config.globalProperties.font_url=o.config.font_url,i.requestFun(t),r.validator(t),t.config.globalProperties.theme=function(){return"theme"+this.$store.state.theme||""},t.config.globalProperties.callPhone=function(t){e.index.makePhoneCall({phoneNumber:t})},t.config.globalProperties.openmap=function(t,o){e.index.openLocation({longitude:Number(o),latitude:Number(t)})},t.config.globalProperties.footTab=function(){return this.$store.state.footTab||""},t.config.globalProperties.getTabBarLinks=function(){let t=e.index.getStorageSync("TabBar"),o=e.index.getStorageSync("theme");null!=t&&""!=t?this.setTabBarLinks(t,o):e.index.request({url:this.config.app_url+"/index.php/api/index/nav",data:{app_id:this.config.app_id},success:t=>{let o=t.data.data.vars.data,n=t.data.data.theme.theme;this.$store.commit("changeTheme",n),e.index.setStorageSync("TabBar",o),e.index.setStorageSync("theme",n),this.setTabBarLinks(o,n)}})},t.config.globalProperties.getThemeColor=function(){return["#FEE238","#b91d32","#09B4F1","#00A348","#FD8103","#B99970","#A4A4D6"][this.$store.state.theme]},t.config.globalProperties.getTimeData=function(e){"string"==typeof e&&(e=Number(e)),"number"!=typeof e&&alert("输入参数无法识别为时间戳");let t=new Date(e);return t.getFullYear()+"-"+((t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-")+(t.getDate()+" ")+(t.getHours()+":")+(t.getMinutes()+":")+t.getSeconds()},{app:t}}p().app.mount("#app"),exports.createApp=p;
