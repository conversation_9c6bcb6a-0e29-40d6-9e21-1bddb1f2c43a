[2025-08-17T19:07:56+08:00][info] \EasyWeChat\Kernel\HttpClient\Response::__set_state(array(
   'response' => 
  \Symfony\Component\HttpClient\Response\NativeResponse::__set_state(array(
     'context' => NULL,
     'url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=95_3yEaONEnXWW_xrOJUHv8umRKsNIpIYCwguXQZGZbUQ3lXttq0IZNzBr6O1dhJZJNOQ50SXM4lejKnQENRF-xGGzz4vXIXRbTkixSKpC9zUTkEP4cVISV_U-8DlcFQUhACAMXB',
     'resolver' => 
    \Closure::__set_state(array(
    )),
     'onProgress' => NULL,
     'remaining' => NULL,
     'buffer' => NULL,
     'multi' => 
    \Symfony\Component\HttpClient\Internal\NativeClientState::__set_state(array(
       'handlesActivity' => 
      array (
      ),
       'openHandles' => 
      array (
      ),
       'lastTimeout' => NULL,
       'id' => 8125371727326578812,
       'maxHostConnections' => 6,
       'responseCount' => 1,
       'dnsCache' => 
      array (
      ),
       'sleep' => false,
       'hosts' => 
      array (
      ),
    )),
     'pauseExpiry' => 0.0,
     'initializer' => 
    \Closure::__set_state(array(
    )),
     'shouldBuffer' => true,
     'content' => NULL,
     'offset' => 0,
     'jsonData' => NULL,
     'canary' => 
    \Symfony\Component\HttpClient\Internal\Canary::__set_state(array(
       'canceller' => 
      \Closure::__set_state(array(
      )),
    )),
     'headers' => 
    array (
    ),
     'info' => 
    array (
      'response_headers' => 
      array (
      ),
      'url' => 
      array (
        'scheme' => 'https:',
        'authority' => '//api.weixin.qq.com',
        'path' => '/cgi-bin/message/subscribe/send',
        'query' => '?access_token=95_3yEaONEnXWW_xrOJUHv8umRKsNIpIYCwguXQZGZbUQ3lXttq0IZNzBr6O1dhJZJNOQ50SXM4lejKnQENRF-xGGzz4vXIXRbTkixSKpC9zUTkEP4cVISV_U-8DlcFQUhACAMXB',
        'fragment' => NULL,
      ),
      'error' => NULL,
      'canceled' => false,
      'http_method' => 'POST',
      'http_code' => 0,
      'redirect_count' => 0,
      'start_time' => 0.0,
      'connect_time' => 0.0,
      'redirect_time' => 0.0,
      'pretransfer_time' => 0.0,
      'starttransfer_time' => 0.0,
      'total_time' => 0.0,
      'namelookup_time' => 0.0,
      'size_upload' => 0,
      'size_download' => 0,
      'size_body' => 352,
      'primary_ip' => '',
      'primary_port' => 443,
      'debug' => '* Enable the curl extension for better performance
',
      'original_url' => 'https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=95_3yEaONEnXWW_xrOJUHv8umRKsNIpIYCwguXQZGZbUQ3lXttq0IZNzBr6O1dhJZJNOQ50SXM4lejKnQENRF-xGGzz4vXIXRbTkixSKpC9zUTkEP4cVISV_U-8DlcFQUhACAMXB',
      'user_data' => NULL,
      'max_duration' => 0.0,
      'pause_handler' => 
      \Closure::__set_state(array(
      )),
    ),
     'handle' => NULL,
     'id' => 305,
     'timeout' => 60.0,
     'inflate' => true,
     'finalInfo' => NULL,
     'logger' => NULL,
  )),
   'failureJudge' => 
  \Closure::__set_state(array(
  )),
   'throw' => true,
))
