<view data-theme="{{l}}" class="{{['address-form', 'o-h', m]}}"><view class="avatar-box"><button class="info-image" open-type="chooseAvatar" bindchooseavatar="{{b}}"><image class="ava-image" src="{{a}}" mode=""></image><view class="icon iconfont icon-shangchuantupian_f"></view></button></view><view class="user-info bg-white f26"><view class="d-b-c"><text class="key-name">会员ID</text><view class="userinfo-text border-b">{{c}}</view></view><view class="d-b-c"><text class="key-name">昵称</text><input class="userinfo-input border-b" placeholder="请输入昵称" placeholder-class="gray9" type="text" value="{{d}}" bindinput="{{e}}"/></view><view class="d-b-c"><text class="key-name">手机</text><view class="d-s-c border-b flex-1"><text class="userinfo-text">{{f}}</text></view></view></view><view class="sub-btn theme-btn" bindtap="{{g}}">保存</view><view class="setup-btn" bindtap="{{h}}">退出登录</view><upload wx:if="{{i}}" bindgetImgs="{{j}}" u-i="0d200cb6-0" bind:__l="__l" u-p="{{k}}"></upload></view>