"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      isLogin: false,
      isloadding: true,
      /*是否加载完成*/
      loadding: true,
      /*菜单*/
      menus: [],
      detail: {
        balance: 0
      },
      urldata: ""
    };
  },
  onPullDownRefresh() {
    let self = this;
    self.getData();
  },
  onLoad(e) {
  },
  onShow() {
    this.getData();
  },
  methods: {
    /*获取数据*/
    getData() {
      let self = this;
      self.isloadding = true;
      self._get("user.index/detail", {
        url: self.urldata
      }, function(res) {
        if (res.data.userInfo) {
          self.isLogin = true;
        }
        self.detail = res.data.userInfo;
        self.menus = res.data.menus;
        self.loadding = false;
        common_vendor.index.stopPullDownRefresh();
        self.isloadding = false;
      });
    },
    /*跳转页面*/
    jumpPage(path) {
      let self = this;
      if (self.isloadding) {
        return;
      }
      if (path.startsWith("/")) {
        self.gotoPage(path);
      } else {
        self[path]();
      }
    },
    /*扫一扫核销*/
    scanQrcode: function() {
      this.gotoPage("/pages/user/scan/scan");
    },
    /*扫一扫核销*/
    receipt: function() {
      let self = this;
      common_vendor.index.scanCode({
        onlyFromCamera: true,
        success: function(res) {
          let ulr = res.path;
          common_vendor.index.__f__("log", "at pages/user/index/index.vue:155", res);
          common_vendor.index.__f__("log", "at pages/user/index/index.vue:156", ulr);
          if (res.errMsg == "scanCode:ok") {
            self.gotoPage(ulr);
          } else {
            common_vendor.index.showToast({
              title: "扫码失败，请重试"
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.s(_ctx.topBarTop() ? "height:" + _ctx.topBarTop() + "px;" : ""),
    b: common_vendor.s(_ctx.getNavHeight()),
    c: common_vendor.s(_ctx.topBarTop() ? "height:" + _ctx.topBarTop() + "px;" : ""),
    d: common_vendor.s(_ctx.getNavHeight()),
    e: $data.detail.avatarUrl || "/static/login-default.png",
    f: common_vendor.o(($event) => _ctx.gotoPage("/pages/user/set/set")),
    g: $data.isLogin
  }, $data.isLogin ? {
    h: common_vendor.t($data.detail.nickName)
  } : {
    i: common_vendor.o(($event) => _ctx.doLogin())
  }, {
    j: common_vendor.t($data.isLogin ? $data.detail.balance : "--"),
    k: common_vendor.o(($event) => _ctx.gotoPage("/pages/user/my-wallet/my-wallet")),
    l: common_vendor.f($data.menus, (item, index, i0) => {
      return common_vendor.e({
        a: item.status == 1
      }, item.status == 1 ? {
        b: item.image_url,
        c: common_vendor.t(item.title),
        d: common_vendor.o(($event) => $options.jumpPage(item.link_url), index)
      } : {}, {
        e: index
      });
    }),
    m: _ctx.theme(),
    n: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/user/index/index.js.map
