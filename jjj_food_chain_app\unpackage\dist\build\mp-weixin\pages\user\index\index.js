"use strict";const e=require("../../../common/vendor.js"),t={data:()=>({isLogin:!1,isloadding:!0,loadding:!0,menus:[],detail:{balance:0},urldata:""}),onPullDownRefresh(){this.getData()},onLoad(e){},onShow(){this.getData()},methods:{getData(){let t=this;t.isloadding=!0,t._get("user.index/detail",{url:t.urldata},(function(a){a.data.userInfo&&(t.isLogin=!0),t.detail=a.data.userInfo,t.menus=a.data.menus,t.loadding=!1,e.index.stopPullDownRefresh(),t.isloadding=!1}))},jumpPage(e){let t=this;t.isloadding||(e.startsWith("/")?t.gotoPage(e):t[e]())},scanQrcode:function(){this.gotoPage("/pages/user/scan/scan")},receipt:function(){let t=this;e.index.scanCode({onlyFromCamera:!0,success:function(a){let o=a.path;console.log(a),console.log(o),"scanCode:ok"==a.errMsg?t.gotoPage(o):e.index.showToast({title:"扫码失败，请重试"})}})}}};const a=e._export_sfc(t,[["render",function(t,a,o,s,n,i){return e.e({a:e.s(t.topBarTop()?"height:"+t.topBarTop()+"px;":""),b:e.s(t.getNavHeight()),c:e.s(t.topBarTop()?"height:"+t.topBarTop()+"px;":""),d:e.s(t.getNavHeight()),e:n.detail.avatarUrl||"/static/login-default.png",f:e.o((e=>t.gotoPage("/pages/user/set/set"))),g:n.isLogin},n.isLogin?{h:e.t(n.detail.nickName)}:{i:e.o((e=>t.doLogin()))},{j:e.t(n.isLogin?n.detail.balance:"--"),k:e.o((e=>t.gotoPage("/pages/user/my-wallet/my-wallet"))),l:e.f(n.menus,((t,a,o)=>e.e({a:1==t.status},1==t.status?{b:t.image_url,c:e.t(t.title),d:e.o((e=>i.jumpPage(t.link_url)),a)}:{},{e:a}))),m:t.theme(),n:e.n(t.theme()||"")})}]]);wx.createPage(a);
