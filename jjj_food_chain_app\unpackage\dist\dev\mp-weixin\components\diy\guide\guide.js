"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {};
  },
  props: ["itemData"],
  methods: {}
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $props.itemData.style.lineHeight + "px",
    b: $props.itemData.style.lineColor,
    c: $props.itemData.style.lineStyle,
    d: $props.itemData.style.background,
    e: $props.itemData.style.paddingLeft + "px",
    f: $props.itemData.style.paddingLeft + "px",
    g: $props.itemData.style.paddingTop + "px",
    h: $props.itemData.style.paddingBottom + "px"
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/components/diy/guide/guide.js.map
