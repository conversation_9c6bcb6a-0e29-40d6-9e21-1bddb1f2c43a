"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      /*是否加载完成*/
      loadding: true,
      indicatorDots: true,
      autoplay: true,
      interval: 2e3,
      duration: 500,
      /*订单id*/
      order_id: 0,
      /*订单详情*/
      detail: {
        order_status: [],
        address: {
          region: []
        },
        product: [],
        pay_type: [],
        delivery_type: [],
        pay_status: []
      },
      codeImage: ""
    };
  },
  onLoad(e) {
    this.order_id = e.order_id;
  },
  mounted() {
    common_vendor.index.showLoading({
      title: "加载中",
      mask: true
    });
    this.getData();
  },
  methods: {
    /*获取订单详情*/
    getData() {
      let _this = this;
      let order_id = _this.order_id;
      _this._get(
        "user.order/paySuccess",
        {
          order_id
        },
        function(res) {
          _this.detail = res.data.order;
          _this.codeImage = res.data.order.qrcode;
          _this.loadding = false;
          common_vendor.index.hideLoading();
        }
      );
    },
    /*返回首页*/
    goHome() {
      this.gotoPage("/pages/index/index");
    },
    /*返回我的订单*/
    goMyorder() {
      this.gotoPage("/pages/order/myorder");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.loadding
  }, !$data.loadding ? common_vendor.e({
    b: common_vendor.t($data.detail.pay_price),
    c: $data.detail.callNo
  }, $data.detail.callNo ? {
    d: common_vendor.t($data.detail.callNo)
  } : {}, {
    e: common_vendor.o((...args) => $options.goMyorder && $options.goMyorder(...args))
  }) : {}, {
    f: _ctx.theme(),
    g: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/order/pay-success/pay-success.js.map
