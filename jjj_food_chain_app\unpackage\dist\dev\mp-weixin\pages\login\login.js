"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      wx_phone: false,
      loading: true,
      background: "",
      listData: [],
      invitation_id: 0,
      user_id: "",
      mobile: true,
      isRead: false,
      setting: {
        login_desc: "",
        login_logo: "",
        name: ""
      }
    };
  },
  onShow() {
  },
  onLoad(e) {
    this.invitation_id = common_vendor.index.getStorageSync("invitation_id") || 0;
    let self = this;
    this.getCodeType();
    common_vendor.index.getUserInfo({
      success: function(res) {
        const userInfo = res.userInfo;
        common_vendor.index.__f__("log", "at pages/login/login.vue:67", userInfo);
        common_vendor.index.login({
          provider: "weixin",
          success: (res2) => {
            common_vendor.index.__f__("log", "at pages/login/login.vue:71", "res-login", res2);
            self.code = res2.code;
            if (res2.errMsg == "login:ok") {
              ({
                code: self.code,
                nickname: userInfo.nickName,
                avatar: userInfo.avatarUrl
              });
              self._post("user.user/login", {
                code: self.code,
                source: "wx",
                invitation_id: self.invitation_id,
                referee_id: common_vendor.index.getStorageSync("referee_id")
              }, (result) => {
                self.user_id = result.data.user_id;
                self.mobile = result.data.mobile;
              }, false, () => {
                self.loading = false;
              });
            }
          }
        });
      }
    });
  },
  methods: {
    xieyi(type) {
      this.gotoPage("/pages/webview/ue?type=" + type);
    },
    getCodeType() {
      let self = this;
      self._post(
        "index/loginSetting",
        {},
        (res) => {
          self.setting = res.data.setting;
          self.wx_phone = res.data.setting.wx_phone;
        }
      );
    },
    onNotLogin: function() {
      this.gotoPage("/pages/index/index");
    },
    UserLogin() {
      let self = this;
      if (self.loading) {
        return;
      }
      if (!self.isRead) {
        common_vendor.index.showToast({
          title: "请勾选并同意《隐私政策》和《用户协议》",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "正在处理",
        mask: true
      });
      common_vendor.wx$1.login({
        success(res_login) {
          let url = "user.user/userLogin";
          let params = {
            code: res_login.code
          };
          self._post(url, params, (result) => {
            common_vendor.index.setStorageSync("token", result.data.token);
            common_vendor.index.setStorageSync("user_id", result.data.user_id);
            common_vendor.index.navigateBack();
          }, false, () => {
            common_vendor.index.hideLoading();
          });
        }
      });
    },
    getUserInfo: function(e) {
      let self = this;
      if (self.loading) {
        return;
      }
      if (!self.isRead) {
        common_vendor.index.showToast({
          title: "请勾选并同意《隐私政策》和《用户协议》",
          icon: "none"
        });
        return;
      }
      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        return false;
      }
      common_vendor.index.showLoading({
        title: "正在处理",
        mask: true
      });
      common_vendor.wx$1.login({
        success(res_login) {
          let url = "user.user/bindMobile";
          let params = {
            code: res_login.code,
            user_id: self.user_id,
            encrypted_data: e.detail.encryptedData,
            iv: e.detail.iv
          };
          self._post(url, params, (result) => {
            common_vendor.index.setStorageSync("token", result.data.token);
            common_vendor.index.setStorageSync("user_id", result.data.user_id);
            common_vendor.index.navigateBack();
          }, false, () => {
            common_vendor.index.hideLoading();
          });
        }
      });
    },
    onGetAuthorize(res) {
      common_vendor.index.__f__("log", "at pages/login/login.vue:198", "开始授权");
      let self = this;
      common_vendor.index.login({
        provider: "alipay",
        success: function(loginRes) {
          common_vendor.index.__f__("log", "at pages/login/login.vue:203", "sucss");
          common_vendor.index.getUserInfo({
            provider: "alipay",
            success: function(infoRes) {
              self.aliPayLogin(loginRes, infoRes);
            }
          });
        },
        fail(err) {
          common_vendor.index.__f__("log", "at pages/login/login.vue:220", err);
        }
      });
    },
    aliPayLogin(loginRes, infoRes) {
      let self = this;
      common_vendor.index.__f__("log", "at pages/login/login.vue:226", loginRes);
      common_vendor.index.__f__("log", "at pages/login/login.vue:227", infoRes);
      common_vendor.index.showLoading({
        title: "登录中",
        mask: true
      });
      self._post("user.useralipay/login", {
        code: loginRes.code,
        avatar: infoRes.avatar,
        nickName: infoRes.nickName
      }, (result) => {
        common_vendor.index.__f__("log", "at pages/login/login.vue:238", result.data.token);
        common_vendor.index.setStorageSync("token", result.data.token);
        common_vendor.index.setStorageSync("user_id", result.data.user_id);
        self.gotoPage("/pages/index/index", "redirect");
      }, false, () => {
        self.gotoPage("/pages/index/index", "redirect");
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.setting.login_logo || "/static/login-default.png",
    b: common_vendor.t($data.setting.name),
    c: common_vendor.t($data.setting.login_desc),
    d: !$data.mobile
  }, !$data.mobile ? common_vendor.e({
    e: $data.wx_phone
  }, $data.wx_phone ? {
    f: common_vendor.o((...args) => $options.getUserInfo && $options.getUserInfo(...args))
  } : {
    g: common_vendor.o(($event) => $options.UserLogin())
  }) : {
    h: common_vendor.o((...args) => $options.UserLogin && $options.UserLogin(...args))
  }, {
    i: common_vendor.n($data.isRead ? "active agreement" : "agreement"),
    j: common_vendor.o(($event) => $options.xieyi("service")),
    k: common_vendor.o(($event) => $options.xieyi("privacy")),
    l: common_vendor.o(($event) => $data.isRead = !$data.isRead),
    m: common_vendor.o((...args) => $options.onNotLogin && $options.onNotLogin(...args)),
    n: _ctx.theme(),
    o: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
