/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
page.data-v-761a79ba {
  background-color: #ffffff;
}
.login-container.data-v-761a79ba {
  position: relative;
  min-height: 100vh;
  padding: 0 68rpx;
}
.login_topbpx.data-v-761a79ba {
  padding: 148rpx 0 52rpx 0;
}
.login_tit.data-v-761a79ba {
  font-weight: bold;
  font-size: 38rpx;
  margin-bottom: 28rpx;
  color: #000;
}
.login_top.data-v-761a79ba {
  font-size: 26rpx;
  color: #000;
}
.login-container .form-level.data-v-761a79ba {
  padding-top: 19rpx;
  height: 99rpx;
  line-height: 99rpx;
  border-bottom: 1px solid #eee;
}
.login-container .form-level .login-input.data-v-761a79ba {
  flex: 1;
  height: 99rpx;
  line-height: 99rpx;
  font-size: 26rpx;
}
.get-code-btn.data-v-761a79ba {
  min-width: 182rpx;
  height: 58rpx;
  line-height: 58rpx;
  padding: 0rpx 30rpx;
  border-radius: 50rpx;
  white-space: nowrap;
  background-color: #ffffff;
  font-size: 26rpx;
  box-sizing: border-box;
}
[data-theme=theme0] .get-code-btn.data-v-761a79ba {
  color: #fa301b !important;
}
[data-theme=theme1] .get-code-btn.data-v-761a79ba {
  color: #b91d32 !important;
}
[data-theme=theme2] .get-code-btn.data-v-761a79ba {
  color: #fb5032 !important;
}
[data-theme=theme3] .get-code-btn.data-v-761a79ba {
  color: #e6444d !important;
}
[data-theme=theme4] .get-code-btn.data-v-761a79ba {
  color: #fd8103 !important;
}
[data-theme=theme5] .get-code-btn.data-v-761a79ba {
  color: #fe4b4e !important;
}
[data-theme=theme6] .get-code-btn.data-v-761a79ba {
  color: #fb5032 !important;
}
.get-code-btn[disabled=true].data-v-761a79ba {
  background: #f7f7f7;
}
.sub-btn.data-v-761a79ba {
  width: 596rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 45rpx;
  margin: 0 auto;
  margin-top: 30rpx;
  margin-bottom: 28rpx;
}
.bottom_nav.data-v-761a79ba {
  width: 100%;
  position: absolute;
  bottom: 100rpx;
}
.bottom-box.data-v-761a79ba {
  width: 70%;
  margin: 0 auto;
}
.other_tit.data-v-761a79ba {
  height: 1rpx;
  background-color: #cacaca;
  width: 100%;
  line-height: 1rpx;
  text-align: center;
}
.weixin_box.data-v-761a79ba {
  background-color: #04be01;
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
}
.weixin_box .icon-weixin.data-v-761a79ba {
  font-size: 40rpx;
  color: #ffffff;
}
.skip.data-v-761a79ba {
  position: absolute;
  top: 70rpx;
  right: 66rpx;
  font-size: 26rpx;
  color: #999999;
}
.xieyi-box.data-v-761a79ba {
  position: fixed;
  bottom: 62rpx;
  left: 0;
  width: 100%;
}
.agreement.data-v-761a79ba {
  border-radius: 50%;
  width: 28rpx;
  height: 28rpx;
  border: 1px solid #ccc;
  background: #fff;
  position: relative;
  margin-right: 10rpx;
  box-sizing: border-box;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18rpx;
  font-weight: bold;
}
[data-theme=theme0] .agreement.active.data-v-761a79ba {
  color: #111111 !important;
}
[data-theme=theme1] .agreement.active.data-v-761a79ba {
  color: #ffffff !important;
}
[data-theme=theme2] .agreement.active.data-v-761a79ba {
  color: #ffffff !important;
}
[data-theme=theme3] .agreement.active.data-v-761a79ba {
  color: #ffffff !important;
}
[data-theme=theme4] .agreement.active.data-v-761a79ba {
  color: #ffffff !important;
}
[data-theme=theme5] .agreement.active.data-v-761a79ba {
  color: #fff !important;
}
[data-theme=theme6] .agreement.active.data-v-761a79ba {
  color: #ffffff !important;
}
[data-theme=theme0] .agreement.active.data-v-761a79ba {
  background-color: #fee238 !important;
}
[data-theme=theme1] .agreement.active.data-v-761a79ba {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .agreement.active.data-v-761a79ba {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .agreement.active.data-v-761a79ba {
  background-color: #00a348 !important;
}
[data-theme=theme4] .agreement.active.data-v-761a79ba {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .agreement.active.data-v-761a79ba {
  background-color: #b99970 !important;
}
[data-theme=theme6] .agreement.active.data-v-761a79ba {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .agreement.active.data-v-761a79ba {
  border-color: #fee238 !important;
}
[data-theme=theme1] .agreement.active.data-v-761a79ba {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .agreement.active.data-v-761a79ba {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .agreement.active.data-v-761a79ba {
  border-color: #00a348 !important;
}
[data-theme=theme4] .agreement.active.data-v-761a79ba {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .agreement.active.data-v-761a79ba {
  border-color: #b99970 !important;
}
[data-theme=theme6] .agreement.active.data-v-761a79ba {
  border-color: #a4a4d6 !important;
}