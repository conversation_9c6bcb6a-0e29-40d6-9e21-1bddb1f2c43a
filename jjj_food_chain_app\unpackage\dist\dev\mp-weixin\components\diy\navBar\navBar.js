"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      //单个宽度
      item_width: "",
      item_height: ""
    };
  },
  props: ["itemData"],
  created() {
    this.item_width = this.itemData.style.rowsNum == 1 ? "690rpx" : "330rpx";
    this.item_height = this.itemData.style.rowsNum == 1 ? "150rpx" : "254rpx";
  },
  methods: {
    /*跳转页面*/
    gotoDetail(path) {
      let self = this;
      common_vendor.index.__f__("log", "at components/diy/navBar/navBar.vue:77", path);
      if (path.startsWith("scanQrcode")) {
        self[path]();
      } else {
        self.gotoPage(path);
      }
    },
    /*扫一扫核销*/
    scanQrcode: function() {
      this.$emit("scanQrcode");
    }
    /*跳转页面*/
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.itemData.style.rowsNum == 1
  }, $props.itemData.style.rowsNum == 1 ? {
    b: common_vendor.f($props.itemData.data, (navBar, index, i0) => {
      return {
        a: common_vendor.t(navBar.title),
        b: navBar.titlecolor,
        c: common_vendor.t(navBar.text),
        d: navBar.textcolor,
        e: navBar.imageUrl,
        f: index,
        g: common_vendor.o(($event) => $options.gotoDetail(navBar.linkUrl), index)
      };
    }),
    c: $props.itemData.style.topRadio * 2 + "rpx",
    d: $props.itemData.style.topRadio * 2 + "rpx",
    e: $props.itemData.style.bottomRadio * 2 + "rpx",
    f: $props.itemData.style.bottomRadio * 2 + "rpx",
    g: "linear-gradient(to bottom, " + ($props.itemData.style.background1 || "#fff") + ", " + ($props.itemData.style.background2 || "#fff") + ")",
    h: common_vendor.n("column-" + $props.itemData.style.rowsNum)
  } : {}, {
    i: $props.itemData.style.rowsNum == 2
  }, $props.itemData.style.rowsNum == 2 ? {
    j: common_vendor.f($props.itemData.data, (navBar, index, i0) => {
      return {
        a: navBar.imageUrl,
        b: common_vendor.t(navBar.title),
        c: navBar.titlecolor,
        d: common_vendor.t(navBar.text),
        e: navBar.textcolor,
        f: index,
        g: common_vendor.o(($event) => $options.gotoDetail(navBar.linkUrl), index)
      };
    }),
    k: $props.itemData.style.topRadio * 2 + "rpx",
    l: $props.itemData.style.topRadio * 2 + "rpx",
    m: $props.itemData.style.bottomRadio * 2 + "rpx",
    n: $props.itemData.style.bottomRadio * 2 + "rpx",
    o: "linear-gradient(to bottom, " + ($props.itemData.style.background1 || "#fff") + ", " + ($props.itemData.style.background2 || "#fff") + ")",
    p: common_vendor.n("column-" + $props.itemData.style.rowsNum)
  } : {}, {
    q: $props.itemData.style.bgcolor,
    r: $props.itemData.style.paddingLeft * 2 + "rpx",
    s: $props.itemData.style.paddingLeft * 2 + "rpx",
    t: $props.itemData.style.paddingTop * 2 + "rpx",
    v: $props.itemData.style.paddingBottom * 2 + "rpx"
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-635d3a30"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/components/diy/navBar/navBar.js.map
