"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      type: "",
      content: ""
    };
  },
  onLoad(e) {
    this.type = e.type;
    let title = "";
    if (this.type == "service") {
      title = "用户协议";
    } else {
      title = "隐私协议";
    }
    common_vendor.index.setNavigationBarTitle({
      title
    });
    this.getData();
  },
  methods: {
    getData() {
      let self = this;
      self._post("index/loginSetting", {}, function(res) {
        if (self.type == "service") {
          self.content = res.data.setting.service;
        } else {
          self.content = res.data.setting.privacy;
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: $data.content
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/webview/ue.js.map
