"use strict";const t=require("../../common/vendor.js"),i={components:{Popup:()=>"../uni-popup.js"},data:()=>({width:750,height:600,dataModel:{},minute:["10","25","40","55"],hours:[{start:"00",end:"02"},{start:"02",end:"04"},{start:"04",end:"06"},{start:"06",end:"08"},{start:"08",end:"10"},{start:"10",end:"12"},{start:"12",end:"14"},{start:"14",end:"16"},{start:"16",end:"18"},{start:"18",end:"20"},{start:"20",end:"22"},{start:"22",end:"24"}],myhours:"",myminute:"",pickhours:{start:"",end:""},mealtime:""}),props:["isTimer"],onShow(){},watch:{isTimer:function(t,i){t!=i&&this.getData()}},methods:{getData(){let t=this,i=new Date;t.myhours=i.getHours(),t.myminute=i.getMinutes(),t.pickH(t.hours[t.myhours]),t.$nextTick((function(){t.hours.forEach(((i,e)=>{i.start<=t.myhours&&t.myhours<i.end&&t.pickH(i)}))}))},pickH(t){this.pickhours=t},hidePopupFunc(t){this.$emit("close","")},picktime(t){this.mealtime=t,this.$emit("close",this.mealtime)},inittime(t){let i=t;return i=1*t-1,t<=10?"0"+i:i},copyQQ(i){var e=document.createElement("input");e.value=i,document.body.appendChild(e),e.select(),e.setSelectionRange(0,e.value.length),document.execCommand("Copy"),document.body.removeChild(e),t.index.showToast({title:"复制成功",icon:"success",mask:!0,duration:2e3})}}};if(!Array){t.resolveComponent("Popup")()}const e=t._export_sfc(i,[["render",function(i,e,s,o,r,n){return{a:t.f(r.hours,((i,e,s)=>t.e({a:r.myhours<=i.end},r.myhours<=i.end?{b:t.t(i.start+":00~"+i.end+":00"),c:t.n(r.pickhours&&i.start!=r.pickhours.start?"hours":"hours-active"),d:t.o((t=>n.pickH(i)),e)}:{},{e:e}))),b:t.f(r.minute,((i,e,s)=>t.e({a:r.pickhours&&r.myhours>=r.pickhours.start&&r.myminute<=i&&r.myhours!=n.inittime(r.pickhours.end)},r.pickhours&&r.myhours>=r.pickhours.start&&r.myminute<=i&&r.myhours!=n.inittime(r.pickhours.end)?{b:t.t(r.pickhours.start+":"+i),c:t.o((t=>n.picktime(r.pickhours.start+":"+i)),e)}:{},{d:e}))),c:t.f(r.minute,((i,e,s)=>t.e(r.pickhours&&r.myhours<=r.pickhours.start&&r.myhours!=n.inittime(r.pickhours.end)?{a:t.t(r.pickhours.start+":"+i),b:t.o((t=>n.picktime(r.pickhours.start+":"+i)),e)}:{},{c:e}))),d:r.pickhours&&r.myhours<=r.pickhours.start&&r.myhours!=n.inittime(r.pickhours.end),e:t.f(r.minute,((i,e,s)=>t.e(r.pickhours&&r.myhours!=n.inittime(r.pickhours.end)?{a:t.t(n.inittime(r.pickhours.end)+":"+i),b:t.o((t=>n.picktime(n.inittime(r.pickhours.end)+":"+i)),e)}:{},{c:e}))),f:r.pickhours&&r.myhours!=n.inittime(r.pickhours.end),g:t.f(r.minute,((i,e,s)=>t.e({a:r.pickhours&&r.myminute<=i&&r.myhours==n.inittime(r.pickhours.end)},r.pickhours&&r.myminute<=i&&r.myhours==n.inittime(r.pickhours.end)?{b:t.t(n.inittime(r.pickhours.end)+":"+i),c:t.o((t=>n.picktime(n.inittime(r.pickhours.end)+":"+i)),e)}:{},{d:e}))),h:t.o(n.hidePopupFunc),i:t.p({show:s.isTimer,width:r.width,height:r.height,padding:0,type:"bottom"})}}],["__scopeId","data-v-8915680b"]]);wx.createComponent(e);
