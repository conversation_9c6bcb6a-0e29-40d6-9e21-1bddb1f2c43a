/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.foot-bottom {
  width: 100%;
  height: 98rpx;
  padding-bottom: env(safe-area-inset-bottom);
}
.foot-tavbar-container {
  box-shadow: 0 0 6rpx 0 rgba(0, 0, 0, 0.3);
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  height: 98rpx;
  background: #FFFFFF;
  z-index: 90;
  padding-bottom: env(safe-area-inset-bottom);
}
.foot-tavbar-container .item {
  flex: 1;
  height: 98rpx;
}
.foot-tavbar-container .item.add-btn .inner {
  margin-bottom: 70rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: #ffcc00;
  box-shadow: 0 0 10rpx 0 rgba(232, 38, 100, 0.6);
}
.foot-tavbar-container .images {
  width: 50rpx;
  height: 50rpx;
}
.foot-tavbar-container .text-name {
  font-size: 24rpx;
  color: #666666;
}
.foot-tavbar-container .item.active .text-name {
  color: #f8c341;
}