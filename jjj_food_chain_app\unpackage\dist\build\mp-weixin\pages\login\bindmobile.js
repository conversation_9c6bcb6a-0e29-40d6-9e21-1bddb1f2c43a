"use strict";const e=require("../../common/vendor.js"),o={data:()=>({background:"",listData:[],sessionKey:""}),onLoad(){let o=this;e.wx$1.login({success(e){o._post("user.user/getSession",{code:e.code},(e=>{o.sessionKey=e.data.session_key}))}})},methods:{onNotLogin:function(){this.gotoPage("/pages/index/index")},getPhoneNumber(o){var n=this;if("getPhoneNumber:ok"!==o.detail.errMsg)return!1;e.index.showLoading({title:"正在处理",mask:!0}),e.wx$1.login({success(s){n._post("user.user/bindMobile",{session_key:n.sessionKey,encrypted_data:o.detail.encryptedData,iv:o.detail.iv},(o=>{e.index.hideLoading(),e.index.navigateBack()}),!1,(()=>{e.index.hideLoading()}))}})}}};const n=e._export_sfc(o,[["render",function(o,n,s,i,t,a){return{a:e.o(((...e)=>a.getPhoneNumber&&a.getPhoneNumber(...e))),b:e.o(((...e)=>a.onNotLogin&&a.onNotLogin(...e)))}}]]);wx.createPage(n);
