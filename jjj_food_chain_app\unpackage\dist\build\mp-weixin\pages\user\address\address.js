"use strict";const d=require("../../../common/vendor.js"),e=require("../../../common/assets.js"),t={data:()=>({loadding:!0,indicatorDots:!0,autoplay:!0,interval:2e3,duration:500,listData:[],default_id:"0",options:{}}),onLoad:function(d){this.options=d},onShow:function(){d.index.showLoading({title:"加载中"}),this.getData()},methods:{getData(){let e=this;e.dataType,e._get("user.address/lists",{},(function(t){e.listData=t.data.list,e.default_id=t.data.default_id+"",e.loadding=!1,d.index.hideLoading()}))},addAddress(){let e=1;"order"===this.options.source&&(e=2),d.index.navigateTo({url:"/pages/user/address/add/add?delta="+e})},radioChange(e){let t=this;return t.default_id=e,t._post("user.address/setDefault",{address_id:e},(function(e){"order"===t.options.source&&d.index.navigateBack()})),!1},editAddress(e){d.index.navigateTo({url:"/pages/user/address/edit/edit?address_id="+e})},delAddress(e){let t=this;d.index.showModal({title:"提示",content:"您确定要移除当前收货地址吗?",success:function(s){s.confirm&&t._get("user.address/delete",{address_id:e},(function(e){1==e.code&&(d.index.showToast({title:"删除成功",duration:2e3}),t.getData())}))}})}}};const s=d._export_sfc(t,[["render",function(t,s,a,i,o,r){return d.e({a:!o.loadding},o.loadding?{}:d.e({b:o.listData.length>0},o.listData.length>0?{c:d.f(o.listData,((e,t,s)=>({a:d.t(e.name),b:d.t(e.phone),c:d.t(e.detail),d:d.t(e.address),e:e.address_id+"",f:o.default_id==e.address_id+"",g:d.o((d=>r.radioChange(e.address_id)),t),h:d.o((d=>r.delAddress(e.address_id)),t),i:d.o((d=>r.editAddress(e.address_id)),t),j:t}))),d:t.getThemeColor(),e:e._imports_0$1,f:e._imports_1,g:d.o((d=>r.addAddress()))}:{h:e._imports_2,i:d.o((d=>r.addAddress()))},{j:t.theme(),k:d.n(t.theme()||"")}))}]]);wx.createPage(s);
