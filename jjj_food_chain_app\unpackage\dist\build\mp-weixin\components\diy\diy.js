"use strict";const a=require("../../common/vendor.js"),e={components:{banner:()=>"./banner/banner.js",windows:()=>"./window/window.js",navBar:()=>"./navBar/navBar.js",blank:()=>"./blank/blank.js",guide:()=>"./guide/guide.js",adNav:()=>"./adNav/adNav.js"},data:()=>({}),props:["diyItems"],created(){},methods:{scanQrcode(){this.$emit("scanQrcode")}}};if(!Array){(a.resolveComponent("banner")+a.resolveComponent("windows")+a.resolveComponent("navBar")+a.resolveComponent("blank")+a.resolveComponent("guide")+a.resolveComponent("adNav"))()}const n=a._export_sfc(e,[["render",function(e,n,t,d,o,r){return{a:a.f(t.diyItems,((e,n,t)=>a.e({a:"banner"===e.type&&null!=e.data},"banner"===e.type&&null!=e.data?{b:"2f230dfa-0-"+t,c:a.p({itemData:e})}:{},{d:"window"==e.type&&null!=e.data},"window"==e.type&&null!=e.data?{e:"2f230dfa-1-"+t,f:a.p({itemData:e})}:{},{g:"navBar"===e.type&&null!=e.data},"navBar"===e.type&&null!=e.data?{h:a.o(r.scanQrcode,n),i:"2f230dfa-2-"+t,j:a.p({itemData:e})}:{},{k:"blank"==e.type},"blank"==e.type?{l:"2f230dfa-3-"+t,m:a.p({itemData:e})}:{},{n:"guide"==e.type},"guide"==e.type?{o:"2f230dfa-4-"+t,p:a.p({itemData:e})}:{},{q:"adNav"==e.type&&null!=e.data},"adNav"==e.type&&null!=e.data?{r:a.o(r.scanQrcode,n),s:"2f230dfa-5-"+t,t:a.p({itemData:e})}:{},{v:n})))}}]]);wx.createComponent(n);
