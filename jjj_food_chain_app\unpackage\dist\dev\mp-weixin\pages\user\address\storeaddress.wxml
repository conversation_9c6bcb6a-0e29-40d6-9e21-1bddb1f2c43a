<view wx:if="{{a}}" data-theme="{{k}}" class="{{l}}"><view wx:if="{{b}}" class="pbenv"><view class="address-list bg-white"><view wx:for="{{c}}" wx:for-item="item" wx:key="l" class="address p-30-0 border-b-d"><view class="info flex-1 mb10"><view class="user f32 d-b-c"><text>{{item.a}}</text><text class="ml20 gray9 f26">{{item.b}}</text></view><view class="pt20 f26 gray3">{{item.c}}{{item.d}}</view></view><view class="d-b-c"><view class="gray9 d-b-c"><text>距离：{{item.e}}km</text><text wx:if="{{item.f}}" class="red">超出配送距离</text></view><view class="d-s-c"><view class="icon-box plus d-c-c ml30" bindtap="{{item.g}}"><image class="add_icon_img" src="{{d}}" mode="aspectFill"></image><text class="gray9 ml10">删除</text></view><view class="none_line ml30"></view><view class="icon-box plus d-c-c ml30 mr40" bindtap="{{item.h}}"><image class="add_icon_img" src="{{e}}" mode="aspectFill"></image><text class="gray9 ml10">编辑</text></view></view></view><view class="radio d-s-c"><radio style="transform:scale(0.6)" color="{{f}}" value="{{item.i}}" checked="{{item.j}}" bindtap="{{item.k}}"/><text class="">默认地址</text></view></view></view><view class="add_add-btn theme-btn" bindtap="{{g}}">新增收货地址</view></view><view wx:else><view class="none_add"><image class="no_add" src="{{h}}" mode="aspectFill"></image></view><view class="no_add_add red-btn" bindtap="{{i}}">新增收货地址</view><view class="no_add_add gray-btn" bindtap="{{j}}">返回首页</view></view></view>