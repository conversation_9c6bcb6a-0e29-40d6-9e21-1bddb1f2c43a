"use strict";const e=require("./city-data/province.js"),t=require("./city-data/city.js"),a=require("./city-data/area.js"),i=require("../../common/vendor.js"),r={data:()=>({pickerValue:[0,0,0],provinceDataList:[],cityDataList:[],areaDataList:[],showPicker:!1}),created(){this.init()},props:{pickerValueDefault:{type:Array,default:()=>[0,0,0]},themeColor:String},watch:{pickerValueDefault(){this.init()}},methods:{init(){this.handPickValueDefault(),this.provinceDataList=e.provinceData,this.cityDataList=t.cityData[this.pickerValueDefault[0]],this.areaDataList=a.areaData[this.pickerValueDefault[0]][this.pickerValueDefault[1]],this.pickerValue=this.pickerValueDefault},show(){setTimeout((()=>{this.showPicker=!0}),0)},maskClick(){},pickerCancel(){this.showPicker=!1,this._$emit("onCancel")},pickerConfirm(e){this.showPicker=!1,this._$emit("onConfirm")},showPickerView(){this.showPicker=!0},handPickValueDefault(){this.pickerValueDefault!==[0,0,0]&&(this.pickerValueDefault[0]>e.provinceData.length-1&&(this.pickerValueDefault[0]=e.provinceData.length-1),this.pickerValueDefault[1]>t.cityData[this.pickerValueDefault[0]].length-1&&(this.pickerValueDefault[1]=t.cityData[this.pickerValueDefault[0]].length-1),this.pickerValueDefault[2]>a.areaData[this.pickerValueDefault[0]][this.pickerValueDefault[1]].length-1&&(this.pickerValueDefault[2]=a.areaData[this.pickerValueDefault[0]][this.pickerValueDefault[1]].length-1))},pickerChange(e){let i=e.mp.detail.value;this.pickerValue[0]!==i[0]?(this.cityDataList=t.cityData[i[0]],this.areaDataList=a.areaData[i[0]][0],i[1]=0,i[2]=0):this.pickerValue[1]!==i[1]&&(this.areaDataList=a.areaData[i[0]][i[1]],i[2]=0),this.pickerValue=i,this._$emit("onChange")},_$emit(e){let t={label:this._getLabel(),value:this.pickerValue,cityCode:this._getCityCode()};this.$emit(e,t)},_getLabel(){return this.provinceDataList[this.pickerValue[0]].label+","+this.cityDataList[this.pickerValue[1]].label+","+this.areaDataList[this.pickerValue[2]].label},_getCityCode(){let e=[0,0,0];return e[0]=this.provinceDataList[this.pickerValue[0]].value,e[1]=this.cityDataList[this.pickerValue[1]].value,e[2]=this.areaDataList[this.pickerValue[2]].value,e}}};if(!Array){i.resolveComponent("template")()}const l=i._export_sfc(r,[["render",function(e,t,a,r,l,s){return{a:l.showPicker?1:"",b:i.o(((...e)=>s.maskClick&&s.maskClick(...e))),c:i.o(((...e)=>s.pickerCancel&&s.pickerCancel(...e))),d:a.themeColor,e:i.o(((...e)=>s.pickerConfirm&&s.pickerConfirm(...e))),f:i.f(l.provinceDataList,((e,t,a)=>({a:i.t(e.label),b:t}))),g:i.f(l.cityDataList,((e,t,a)=>({a:i.t(e.label),b:t}))),h:i.f(l.areaDataList,((e,t,a)=>({a:i.t(e.label),b:t}))),i:l.pickerValue,j:i.o(((...e)=>s.pickerChange&&s.pickerChange(...e))),k:l.showPicker?1:""}}]]);wx.createComponent(l);
