"use strict";const t=require("../../../common/vendor.js"),e={data:()=>({item_width:"",item_height:""}),props:["itemData"],created(){this.item_width=1==this.itemData.style.rowsNum?"690rpx":"330rpx",this.item_height=1==this.itemData.style.rowsNum?"150rpx":"254rpx"},methods:{gotoDetail(t){let e=this;console.log(t),t.startsWith("scanQrcode")?e[t]():e.gotoPage(t)},scanQrcode:function(){this.$emit("scanQrcode")}}};const a=t._export_sfc(e,[["render",function(e,a,o,i,r,m){return t.e({a:1==o.itemData.style.rowsNum},1==o.itemData.style.rowsNum?{b:t.f(o.itemData.data,((e,a,o)=>({a:t.t(e.title),b:e.titlecolor,c:t.t(e.text),d:e.textcolor,e:e.imageUrl,f:a,g:t.o((t=>m.gotoDetail(e.linkUrl)),a)}))),c:2*o.itemData.style.topRadio+"rpx",d:2*o.itemData.style.topRadio+"rpx",e:2*o.itemData.style.bottomRadio+"rpx",f:2*o.itemData.style.bottomRadio+"rpx",g:"linear-gradient(to bottom, "+(o.itemData.style.background1||"#fff")+", "+(o.itemData.style.background2||"#fff")+")",h:t.n("column-"+o.itemData.style.rowsNum)}:{},{i:2==o.itemData.style.rowsNum},2==o.itemData.style.rowsNum?{j:t.f(o.itemData.data,((e,a,o)=>({a:e.imageUrl,b:t.t(e.title),c:e.titlecolor,d:t.t(e.text),e:e.textcolor,f:a,g:t.o((t=>m.gotoDetail(e.linkUrl)),a)}))),k:2*o.itemData.style.topRadio+"rpx",l:2*o.itemData.style.topRadio+"rpx",m:2*o.itemData.style.bottomRadio+"rpx",n:2*o.itemData.style.bottomRadio+"rpx",o:"linear-gradient(to bottom, "+(o.itemData.style.background1||"#fff")+", "+(o.itemData.style.background2||"#fff")+")",p:t.n("column-"+o.itemData.style.rowsNum)}:{},{q:o.itemData.style.bgcolor,r:2*o.itemData.style.paddingLeft+"rpx",s:2*o.itemData.style.paddingLeft+"rpx",t:2*o.itemData.style.paddingTop+"rpx",v:2*o.itemData.style.paddingBottom+"rpx"})}],["__scopeId","data-v-091e8706"]]);wx.createComponent(a);
