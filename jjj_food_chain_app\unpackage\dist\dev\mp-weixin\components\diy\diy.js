"use strict";
const common_vendor = require("../../common/vendor.js");
const banner = () => "./banner/banner.js";
const windows = () => "./window/window.js";
const navBar = () => "./navBar/navBar.js";
const blank = () => "./blank/blank.js";
const guide = () => "./guide/guide.js";
const adNav = () => "./adNav/adNav.js";
const _sfc_main = {
  components: {
    banner,
    windows,
    navBar,
    blank,
    guide,
    adNav
  },
  data() {
    return {};
  },
  props: ["diyItems"],
  created() {
  },
  methods: {
    scanQrcode() {
      this.$emit("scanQrcode");
    }
  }
};
if (!Array) {
  const _component_banner = common_vendor.resolveComponent("banner");
  const _component_windows = common_vendor.resolveComponent("windows");
  const _component_navBar = common_vendor.resolveComponent("navBar");
  const _component_blank = common_vendor.resolveComponent("blank");
  const _component_guide = common_vendor.resolveComponent("guide");
  const _component_adNav = common_vendor.resolveComponent("adNav");
  (_component_banner + _component_windows + _component_navBar + _component_blank + _component_guide + _component_adNav)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($props.diyItems, (item, index, i0) => {
      return common_vendor.e({
        a: item.type === "banner" && item.data != null
      }, item.type === "banner" && item.data != null ? {
        b: "2f230dfa-0-" + i0,
        c: common_vendor.p({
          itemData: item
        })
      } : {}, {
        d: item.type == "window" && item.data != null
      }, item.type == "window" && item.data != null ? {
        e: "2f230dfa-1-" + i0,
        f: common_vendor.p({
          itemData: item
        })
      } : {}, {
        g: item.type === "navBar" && item.data != null
      }, item.type === "navBar" && item.data != null ? {
        h: common_vendor.o($options.scanQrcode, index),
        i: "2f230dfa-2-" + i0,
        j: common_vendor.p({
          itemData: item
        })
      } : {}, {
        k: item.type == "blank"
      }, item.type == "blank" ? {
        l: "2f230dfa-3-" + i0,
        m: common_vendor.p({
          itemData: item
        })
      } : {}, {
        n: item.type == "guide"
      }, item.type == "guide" ? {
        o: "2f230dfa-4-" + i0,
        p: common_vendor.p({
          itemData: item
        })
      } : {}, {
        q: item.type == "adNav" && item.data != null
      }, item.type == "adNav" && item.data != null ? {
        r: common_vendor.o($options.scanQrcode, index),
        s: "2f230dfa-5-" + i0,
        t: common_vendor.p({
          itemData: item
        })
      } : {}, {
        v: index
      });
    })
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/diy/diy.js.map
