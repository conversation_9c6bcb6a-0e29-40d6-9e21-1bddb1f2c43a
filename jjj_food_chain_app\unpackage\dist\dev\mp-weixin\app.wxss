/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
/*自定义iconfont文件，请将你的iconfont.css内容复制拷贝到这里就可以直接在项目里直接引用*/
/*参考上面的iconfont.css内容和使用方法*/
@font-face {
  font-family: "iconfont";
  /* Project id 4197023 */
  src: url("https://at.alicdn.com/t/c/font_4197023_w5cx2xv0lmq.woff2?t=1723108275498") format("woff2"), url("https://at.alicdn.com/t/c/font_4197023_w5cx2xv0lmq.woff?t=1723108275498") format("woff"), url("https://at.alicdn.com/t/c/font_4197023_w5cx2xv0lmq.ttf?t=1723108275498") format("truetype");
}
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-haibao:before {
  content: "\e716";
}
.icon-kongxinduigou:before {
  content: "\ed1b";
}
.icon-shuzhixiajiang:before {
  content: "\e8f9";
}
.icon-shuzhishangsheng:before {
  content: "\e8fa";
}
.icon-gonggao2:before {
  content: "\e715";
}
.icon-shangpin:before {
  content: "\e714";
}
.icon-daohang:before {
  content: "\e712";
}
.icon-dianhua:before {
  content: "\e862";
}
.icon-fuliquan:before {
  content: "\e70e";
}
.icon-jinbi1:before {
  content: "\e70f";
}
.icon-qianbao1:before {
  content: "\e710";
}
.icon-dingdan2:before {
  content: "\e70a";
}
.icon-wodedianpu:before {
  content: "\e70c";
}
.icon-tongjibaobiao:before {
  content: "\e70d";
}
.icon-kefu3:before {
  content: "\e88f";
}
.icon-zhibo2:before {
  content: "\e70b";
}
.icon-waimai1:before {
  content: "\e705";
}
.icon-yunhangchenggong:before {
  content: "\e707";
}
.icon-yanzhengma1:before {
  content: "\e708";
}
.icon-bianji:before {
  content: "\e709";
}
.icon-gouwudai:before {
  content: "\e704";
}
.icon-shijian:before {
  content: "\e703";
}
.icon-chakan1:before {
  content: "\e702";
}
.icon-he_15zhengzhaoguanli:before {
  content: "\e706";
}
.icon-dianpufill:before {
  content: "\e735";
}
.icon-shangchuantupian_f:before {
  content: "\e701";
}
.icon-dizhi2:before {
  content: "\e6e7";
}
.icon-jifenduihuan-xianxing:before {
  content: "\e6ed";
}
.icon-huobi:before {
  content: "\e6e6";
}
.icon-22222_huaban1:before {
  content: "\e678";
}
.icon-launchproduct:before {
  content: "\e673";
}
.icon-payStoreQr:before {
  content: "\e6d1";
}
.icon-baoguofahuo:before {
  content: "\e884";
}
.icon-baoguo:before {
  content: "\eb74";
}
.icon-shanchu1:before {
  content: "\e718";
}
.icon-icozhuanhuan:before {
  content: "\e66c";
}
.icon-gouwuchefill:before {
  content: "\e73c";
}
.icon-wodedingdan:before {
  content: "\e75e";
}
.icon-jibenxinxi:before {
  content: "\e75f";
}
.icon-hongbao1:before {
  content: "\e760";
}
.icon-tupian11:before {
  content: "\e761";
}
.icon-stores:before {
  content: "\e762";
}
.icon-kongbaiye:before {
  content: "\e764";
}
.icon-biaoti:before {
  content: "\e765";
}
.icon-shipinbofang:before {
  content: "\e766";
}
.icon-tupian111:before {
  content: "\e767";
}
.icon-shipin:before {
  content: "\e76b";
}
.icon-gonggao:before {
  content: "\e76c";
}
.icon-gonggao1:before {
  content: "\e76d";
}
.icon-wenzhangguanli:before {
  content: "\e76e";
}
.icon-fuzhuxian:before {
  content: "\e76f";
}
.icon-miaosha11:before {
  content: "\e770";
}
.icon-lunbotu:before {
  content: "\e771";
}
.icon-mulu:before {
  content: "\e773";
}
.icon-zaixiankefu:before {
  content: "\e774";
}
.icon-fuwenben:before {
  content: "\e775";
}
.icon-htmal5icon24:before {
  content: "\e6d2";
}
.icon-dianpu1:before {
  content: "\e6d3";
}
.icon-dianpu11:before {
  content: "\e6d4";
}
.icon-qiye:before {
  content: "\e6d5";
}
.icon-ziyuan1:before {
  content: "\e6d6";
}
.icon-yanzhengma:before {
  content: "\e6d7";
}
.icon-start:before {
  content: "\e6d8";
}
.icon-start1:before {
  content: "\e6d9";
}
.icon-zhanghumingcheng:before {
  content: "\e6da";
}
.icon-mima:before {
  content: "\e6db";
}
.icon-qq:before {
  content: "\e6dc";
}
.icon-chongzhi:before {
  content: "\e6dd";
}
.icon-jifen1:before {
  content: "\e6de";
}
.icon-duihuan1:before {
  content: "\e6df";
}
.icon-share1:before {
  content: "\e6e1";
}
.icon-huatong:before {
  content: "\e6e3";
}
.icon-caidan:before {
  content: "\e6e8";
}
.icon-gouwu:before {
  content: "\e86e";
}
.icon-biaoqing:before {
  content: "\e6e4";
}
.icon-fasong:before {
  content: "\e743";
}
.icon-jinbi:before {
  content: "\e782";
}
.icon-saoyisao:before {
  content: "\e6e5";
}
.icon-zhifubao11:before {
  content: "\e6e9";
}
.icon-yuyue11:before {
  content: "\e6ec";
}
.icon-right_arrow:before {
  content: "\e6ee";
}
.icon-lianhefenhong:before {
  content: "\e6ef";
}
.icon-zhuanruyue:before {
  content: "\e6f0";
}
.icon-ziyuan2:before {
  content: "\e6f1";
}
.icon-daojishi:before {
  content: "\e6f2";
}
.icon-gouwuche1:before {
  content: "\e6f3";
}
.icon-kefu1:before {
  content: "\e6f4";
}
.icon-shouye:before {
  content: "\e6f5";
}
.icon-daoda1:before {
  content: "\e6f6";
}
.icon-fenxiang2:before {
  content: "\e6f7";
}
.icon-shouye1:before {
  content: "\e6f8";
}
.icon-yinhangka:before {
  content: "\e6f9";
}
.icon-address-none:before {
  content: "\e6fa";
}
.icon-remarks:before {
  content: "\e6fb";
}
.icon-shoucang1:before {
  content: "\e8b9";
}
.icon-shoucang2:before {
  content: "\e8c7";
}
.icon-fenxiang3:before {
  content: "\e8b0";
}
.icon-jiantouxia-copy:before {
  content: "\e9cf";
}
.icon-lizhirenyuan:before {
  content: "\e6fc";
}
.icon-guanghua:before {
  content: "\e6fd";
}
.icon-Vlian:before {
  content: "\e6fe";
}
.icon-jinyan-audiostatic:before {
  content: "\e6ff";
}
.icon-liangdu_o:before {
  content: "\eb66";
}
.icon-qingxidu:before {
  content: "\e700";
}
.icon-sanjiao2:before {
  content: "\e64f";
}
.icon-sanjiao1:before {
  content: "\e650";
}
.icon-jiantou1:before {
  content: "\e651";
}
.icon-jiantoushang:before {
  content: "\e657";
}
.icon-kefu2:before {
  content: "\e658";
}
.icon-gouwuche:before {
  content: "\e659";
}
.icon-sousuo1:before {
  content: "\e65d";
}
.icon-icon_xianshi-xian:before {
  content: "\e65e";
}
.icon-tuikuan:before {
  content: "\e65f";
}
.icon-dizhi:before {
  content: "\e661";
}
.icon-lajitong:before {
  content: "\e662";
}
.icon-jian:before {
  content: "\e666";
}
.icon-quan:before {
  content: "\e667";
}
.icon-icon:before {
  content: "\e668";
}
.icon-fenxiao1:before {
  content: "\e669";
}
.icon-jia:before {
  content: "\e66a";
}
.icon-bangzhu:before {
  content: "\e77c";
}
.icon-pintuangou:before {
  content: "\e66d";
}
.icon-diliweizhi:before {
  content: "\e6c2";
}
.icon-yue11:before {
  content: "\e66e";
}
.icon-qianbao:before {
  content: "\e66f";
}
.icon-daishouhuo:before {
  content: "\e670";
}
.icon-integral:before {
  content: "\e671";
}
.icon-kanjia1:before {
  content: "\e672";
}
.icon-youhuiquan1:before {
  content: "\e674";
}
.icon-guanbi1:before {
  content: "\e675";
}
.icon-quanbudingdan:before {
  content: "\e677";
}
.icon-pintuan:before {
  content: "\e76a";
}
.icon-gantanhao1:before {
  content: "\e686";
}
.icon-dizhi1:before {
  content: "\e734";
}
.icon-edit:before {
  content: "\e679";
}
.icon-pingjiachaping:before {
  content: "\e67a";
}
.icon-pingjiazhongping:before {
  content: "\e67b";
}
.icon-pingjiahaoping:before {
  content: "\e67c";
}
.icon-xiangji:before {
  content: "\e67f";
}
.icon-shaixuanpaixu:before {
  content: "\e680";
}
.icon-shaixuan:before {
  content: "\e681";
}
.icon-fenxiang:before {
  content: "\e682";
}
.icon-guanzhu:before {
  content: "\e683";
}
.icon-chakan:before {
  content: "\e68a";
}
.icon-002dianhua:before {
  content: "\e684";
}
.icon-phone1:before {
  content: "\e687";
}
.icon-erweima:before {
  content: "\e688";
}
.icon-tuandui:before {
  content: "\e689";
}
.icon-fenxiaodingdan:before {
  content: "\e68b";
}
.icon-wait:before {
  content: "\e68d";
}
.icon-zijinmingxi:before {
  content: "\e68f";
}
.icon-share:before {
  content: "\e690";
}
.icon-jingmeihaibao:before {
  content: "\e691";
}
.icon-shoucang:before {
  content: "\e692";
}
.icon-dianzan:before {
  content: "\e697";
}
.icon-hongbao:before {
  content: "\e693";
}
.icon-xiaoxi3:before {
  content: "\e69e";
}
.icon-libao1:before {
  content: "\e694";
}
.icon-ic_check:before {
  content: "\e698";
}
.icon-tijiaochenggong:before {
  content: "\e699";
}
.icon-queren:before {
  content: "\e69a";
}
.icon-Homehomepagemenu:before {
  content: "\e9ce";
}
.icon-wenjian:before {
  content: "\e69b";
}
.icon-step:before {
  content: "\e69c";
}
.icon-laba:before {
  content: "\e69f";
}
.icon-youhuiquan11:before {
  content: "\e6a0";
}
.icon-fenxiao11:before {
  content: "\e6a2";
}
.icon-youhuiquan-:before {
  content: "\e6a3";
}
.icon-bofang:before {
  content: "\e6a4";
}
.icon-ziyuan:before {
  content: "\e6a5";
}
.icon-xingzhuang1:before {
  content: "\e6a6";
}
.icon-daipingjia:before {
  content: "\e772";
}
.icon-guanzhu1:before {
  content: "\e6a7";
}
.icon-31guanzhu1xuanzhong:before {
  content: "\e6a8";
}
.icon-tubiaozhizuo-:before {
  content: "\e6ba";
}
.icon-fenxiang1:before {
  content: "\e72f";
}
.icon-shipinwenjianhuise:before {
  content: "\e6be";
}
.icon-shezhi2:before {
  content: "\e781";
}
.icon-shezhi12:before {
  content: "\e6bf";
}
.icon-paixing:before {
  content: "\e6c0";
}
.icon-huodongtuiguang:before {
  content: "\e6c3";
}
.icon-01:before {
  content: "\e6c4";
}
.icon-shangchuan:before {
  content: "\e6c5";
}
.icon-zhuanshutequan:before {
  content: "\e6c6";
}
.icon-zhibo1:before {
  content: "\e8c1";
}
.icon-paihangbang:before {
  content: "\e6c8";
}
.icon-dingdan1:before {
  content: "\e6c9";
}
.icon-dilanxianxingiconyihuifu_huabanfuben:before {
  content: "\e6ca";
}
.icon-gengduo:before {
  content: "\e6cb";
}
.icon-meiyan:before {
  content: "\e6cd";
}
.icon-meibai1:before {
  content: "\e6ce";
}
.icon-iconset0243:before {
  content: "\e6cf";
}
.icon-zhuangxiushangjia-:before {
  content: "\e6d0";
}
.icon-dianneijiucan:before {
  content: "\e6a9";
}
.icon-waimai:before {
  content: "\e6aa";
}
.icon-icon111:before {
  content: "\e6ab";
}
.icon-quanping:before {
  content: "\e6e2";
}
.icon-shouyin:before {
  content: "\e6ac";
}
.icon-huiyuan21:before {
  content: "\e6ad";
}
.icon-yemianpeizhi:before {
  content: "\e6ae";
}
.icon-huiyuanqiaguanli:before {
  content: "\e6af";
}
.icon-shezhi11:before {
  content: "\e6b0";
}
.icon-caiwuguanli:before {
  content: "\e6b1";
}
.icon-quanxian:before {
  content: "\e6b2";
}
.icon-icon-waimai:before {
  content: "\e6b3";
}
.icon-tongji21:before {
  content: "\e6b4";
}
.icon-shangpinguanli:before {
  content: "\e7cb";
}
.icon-tableware-full:before {
  content: "\e8c6";
}
.icon-mendian11:before {
  content: "\e951";
}
.icon-menu_cjgl:before {
  content: "\e6b5";
}
.icon-yingyong:before {
  content: "\e6b6";
}
.icon-zhekou-shi:before {
  content: "\e6b7";
}
.icon-view:before {
  content: "\e6b8";
}
.icon-qingkong:before {
  content: "\e6cc";
}
.icon-dingdan:before {
  content: "\e6bb";
}
.icon-24gf-chartBarUpward:before {
  content: "\e848";
}
.icon-huiyuan3:before {
  content: "\e825";
}
.icon-ly_huiyuanka:before {
  content: "\e6bc";
}
.icon-biaodanyemian2:before {
  content: "\e7cd";
}
.icon-xianlu:before {
  content: "\e64c";
}
.icon-saoyisao1:before {
  content: "\e655";
}
.icon-weixin:before {
  content: "\e63e";
}
.icon-zhifubao:before {
  content: "\e656";
}
.icon-yue:before {
  content: "\e65a";
}
.icon-xuanze:before {
  content: "\e641";
}
.icon-jiantou:before {
  content: "\e643";
}
.icon-sousuo:before {
  content: "\e644";
}
.icon-shouyetubiaohei:before {
  content: "\e646";
}
.icon-address:before {
  content: "\e647";
}
.icon-phone:before {
  content: "\e648";
}
.icon-time:before {
  content: "\e649";
}
.icon-gou:before {
  content: "\e64b";
}
.icon-zengjia:before {
  content: "\e6eb";
}
.icon-jian_sekuai:before {
  content: "\ed21";
}
.icon-guanbi:before {
  content: "\eaf2";
}
.icon-shuaxin:before {
  content: "\e636";
}
.icon-magic-:before {
  content: "\e713";
}
.icon-meibai:before {
  content: "\e635";
}
.icon-zhaoxiangjiqiehuan:before {
  content: "\e875";
}
.icon-xiaolian:before {
  content: "\e75a";
}
.icon-yunyingguanli:before {
  content: "\e614";
}
.icon-icon-lottery:before {
  content: "\e633";
}
.icon-yushoucuifu:before {
  content: "\e631";
}
.icon-zhuanpanshezhi:before {
  content: "\e66b";
}
.icon-quanxianguanli:before {
  content: "\e63a";
}
.icon-xiaoxi2:before {
  content: "\e63d";
}
.icon-fenqifenqishangcheng2:before {
  content: "\e6c1";
}
.icon-shanghuguanli:before {
  content: "\e632";
}
.icon-icon-test2:before {
  content: "\e61b";
}
.icon-geren9:before {
  content: "\e602";
}
.icon-huiyuan:before {
  content: "\e606";
}
.icon-icon-test:before {
  content: "\e607";
}
.icon-application:before {
  content: "\e97b";
}
.icon-hiuyuan:before {
  content: "\e696";
}
.icon-authority:before {
  content: "\e628";
}
.icon-iconset0335:before {
  content: "\e63c";
}
.icon-icon-test1:before {
  content: "\e62b";
}
.icon-xinzengtuanzhang:before {
  content: "\e605";
}
.icon-tuanzhang:before {
  content: "\e638";
}
.icon-vip:before {
  content: "\e74f";
}
.icon-tuichu:before {
  content: "\e600";
}
.icon-chanpin:before {
  content: "\e6c7";
}
.icon-yugu:before {
  content: "\e652";
}
.icon-tongji:before {
  content: "\e676";
}
.icon-xinzengyonghu1:before {
  content: "\e637";
}
.icon-zhuye:before {
  content: "\e608";
}
.icon-shangping:before {
  content: "\e609";
}
.icon-VIPdingdan:before {
  content: "\e720";
}
.icon-chajian1:before {
  content: "\e97c";
}
.icon-huodong:before {
  content: "\eb4f";
}
.icon-caiwu:before {
  content: "\e6bd";
}
.icon-chajian:before {
  content: "\e685";
}
.icon-xiaochengxutubiaoguilei-:before {
  content: "\e621";
}
.icon-yemian:before {
  content: "\e60a";
}
.icon-gongyingshang:before {
  content: "\e6a1";
}
.icon-xiaoshoue:before {
  content: "\e98e";
}
.icon-dingdanshu:before {
  content: "\e6b9";
}
.icon-zhifudingdan:before {
  content: "\e6e0";
}
.icon-supplier:before {
  content: "\e60b";
}
.icon-caiwu1:before {
  content: "\e60f";
}
.icon-iconset03351:before {
  content: "\e6ea";
}
.icon-jifen:before {
  content: "\e61a";
}
.icon-icon1:before {
  content: "\e64d";
}
.icon-dianpu:before {
  content: "\e642";
}
.icon-huiyuan1:before {
  content: "\e67e";
}
.icon-home:before {
  content: "\e62c";
}
.icon-chajian11:before {
  content: "\e653";
}
.icon-zhekou:before {
  content: "\e881";
}
.icon-tongji1:before {
  content: "\e660";
}
.icon-libao:before {
  content: "\e787";
}
.icon-authority1:before {
  content: "\e623";
}
.icon-shangping1:before {
  content: "\e627";
}
.icon-youhuiquan:before {
  content: "\e639";
}
.icon-cedaohang-zhanghao:before {
  content: "\e711";
}
.icon-16:before {
  content: "\e615";
}
.icon-xiaoshou:before {
  content: "\e622";
}
.icon-tubiaozhizuomoban-:before {
  content: "\e611";
}
.icon-xinzengyonghu:before {
  content: "\e634";
}
.icon-piliangxiadan:before {
  content: "\e69d";
}
.icon-tubiao_baoyou:before {
  content: "\e603";
}
.icon-weibiaoti2fuzhi02:before {
  content: "\e61f";
}
.icon-shezhi:before {
  content: "\e626";
}
.icon-gongzhonghaoguanli:before {
  content: "\e664";
}
.icon-zhibo:before {
  content: "\e750";
}
.icon-Invitation:before {
  content: "\e629";
}
.icon-xiaoxi:before {
  content: "\e604";
}
.icon-kanjia:before {
  content: "\e610";
}
.icon-gengduopintuan:before {
  content: "\e60e";
}
.icon-qiandao:before {
  content: "\e612";
}
.icon-collection:before {
  content: "\e60c";
}
.icon-fenxiao:before {
  content: "\e618";
}
.icon-manjian:before {
  content: "\e663";
}
.icon-tuisong:before {
  content: "\e61e";
}
.icon-miaosha:before {
  content: "\e645";
}
.icon-tuijian1:before {
  content: "\e63b";
}
.icon-tupian:before {
  content: "\e64a";
}
.icon-kefu:before {
  content: "\e624";
}
.icon-diqu:before {
  content: "\e620";
}
.icon-diqu1:before {
  content: "\e65b";
}
.icon-xingzhuangjiehe:before {
  content: "\e60d";
}
.icon-gongzhonghao:before {
  content: "\e601";
}
.icon-you:before {
  content: "\e625";
}
.icon-you1:before {
  content: "\e68e";
}
.icon-gongzhonghao1:before {
  content: "\e665";
}
.icon-xiaochengxu:before {
  content: "\e640";
}
.icon-mendian:before {
  content: "\e616";
}
.icon-xiaoxi1:before {
  content: "\e65c";
}
.icon-youxiang:before {
  content: "\e614";
}
.icon-yuyue:before {
  content: "\e62a";
}
.icon-iconset0250:before {
  content: "\e695";
}
.icon-gantanhao:before {
  content: "\e613";
}
.icon-wifi:before {
  content: "\e7e0";
}
.icon-xinhao:before {
  content: "\e7ac";
}
.icon-yonghu:before {
  content: "\e654";
}
.icon-shanchu:before {
  content: "\e72d";
}
.icon-tuodong:before {
  content: "\e64e";
}
.icon-biaodanzujian-biaoge:before {
  content: "\eb94";
}
.icon-choujiangLottery:before {
  content: "\e7bc";
}
.icon-manjian1:before {
  content: "\e617";
}
.icon-fenleiorguangchangorqitatianchong:before {
  content: "\e67d";
}
.icon-quanxianmiyao:before {
  content: "\e84c";
}
.icon-shezhi1:before {
  content: "\e8b8";
}
.icon-neirong:before {
  content: "\e62d";
}
.icon-xingzhuang:before {
  content: "\e619";
}
.icon-renwu:before {
  content: "\e63f";
}
.icon-yushouxiangmu:before {
  content: "\e61c";
}
.icon-dingdanmanjian:before {
  content: "\e68c";
}
.icon-31paishe:before {
  content: "\e61d";
}
.icon-shangchuanshipin:before {
  content: "\e62e";
}
.icon-laxinjiangli:before {
  content: "\e62f";
}
.icon-shouye-zhihui:before {
  content: "\e630";
}
/*每个页面公共css */
body {
  background: #f5f5f5;
  font-size: 24rpx;
}
page button {
  margin: 0;
  font-size: 28rpx;
}
button {
  -webkit-tap-highlight-color: transparent;
  background-color: #f8f8f8;
  border-radius: 5px;
  box-sizing: border-box;
  color: #000;
  cursor: pointer;
  display: block;
  font-size: 18px;
  margin-left: auto;
  margin-right: auto;
  overflow: hidden;
  padding-left: 14px;
  padding-right: 14px;
  position: relative;
  text-align: center;
  text-decoration: none;
}
input {
  cursor: auto;
  display: block;
  overflow: hidden;
  text-overflow: clip;
}
page button::after {
  content: none;
}
.container {
  padding: 20rpx;
  box-sizing: border-box;
}
.border-radio {
  border-radius: 6rpx;
}
.border-radio-sm {
  border-radius: 4rpx;
}
.border-radio-lg {
  border-radius: 12rpx;
}
.border-radio-big {
  border-radius: 35rpx;
}
/*color*/
.gray {
  color: #cccccc;
}
.gray9 {
  color: #999999;
}
.gray6 {
  color: #666666;
}
.gray3 {
  color: #333333;
}
.white {
  color: #ffffff;
}
.red {
  color: #f6220c;
}
.redEe {
  color: #ee1414;
}
.redFb {
  color: #FB5032;
}
.redF6 {
  color: #f6220c;
}
.blue {
  color: #28a5ff;
}
.violet {
  color: #a824e4;
}
.yellow {
  color: #ffd127;
}
.orange {
  color: #ff8a00;
}
.green {
  color: #32d500;
}
.brown {
  color: #4f3a1e;
}
.golden {
  color: #a8700d;
}
[data-theme=theme0] .dominant {
  color: #fee238 !important;
}
[data-theme=theme1] .dominant {
  color: #b91d32 !important;
}
[data-theme=theme2] .dominant {
  color: #09b4f1 !important;
}
[data-theme=theme3] .dominant {
  color: #00a348 !important;
}
[data-theme=theme4] .dominant {
  color: #fd8103 !important;
}
[data-theme=theme5] .dominant {
  color: #b99970 !important;
}
[data-theme=theme6] .dominant {
  color: #a4a4d6 !important;
}
[data-theme=theme0] .theme-notice {
  color: #fa301b !important;
}
[data-theme=theme1] .theme-notice {
  color: #b91d32 !important;
}
[data-theme=theme2] .theme-notice {
  color: #fb5032 !important;
}
[data-theme=theme3] .theme-notice {
  color: #e6444d !important;
}
[data-theme=theme4] .theme-notice {
  color: #fd8103 !important;
}
[data-theme=theme5] .theme-notice {
  color: #fe4b4e !important;
}
[data-theme=theme6] .theme-notice {
  color: #fb5032 !important;
}
[data-theme=theme0] .theme-price {
  color: #fa301b !important;
}
[data-theme=theme1] .theme-price {
  color: #b91d32 !important;
}
[data-theme=theme2] .theme-price {
  color: #fb5032 !important;
}
[data-theme=theme3] .theme-price {
  color: #e6444d !important;
}
[data-theme=theme4] .theme-price {
  color: #111111 !important;
}
[data-theme=theme5] .theme-price {
  color: #111111 !important;
}
[data-theme=theme6] .theme-price {
  color: #111111 !important;
}
[data-theme=theme0] .text_color {
  color: #ffffff !important;
}
[data-theme=theme1] .text_color {
  color: #ffffff !important;
}
[data-theme=theme2] .text_color {
  color: #ffffff !important;
}
[data-theme=theme3] .text_color {
  color: #ffffff !important;
}
[data-theme=theme4] .text_color {
  color: #ffffff !important;
}
[data-theme=theme5] .text_color {
  color: #ffffff !important;
}
[data-theme=theme6] .text_color {
  color: #ffffff !important;
}
[data-theme=theme0] .text_color1 {
  color: #111111 !important;
}
[data-theme=theme1] .text_color1 {
  color: #ffffff !important;
}
[data-theme=theme2] .text_color1 {
  color: #ffffff !important;
}
[data-theme=theme3] .text_color1 {
  color: #ffffff !important;
}
[data-theme=theme4] .text_color1 {
  color: #ffffff !important;
}
[data-theme=theme5] .text_color1 {
  color: #fff !important;
}
[data-theme=theme6] .text_color1 {
  color: #ffffff !important;
}
[data-theme=theme0] .theme-bg {
  background-color: #fee238 !important;
}
[data-theme=theme1] .theme-bg {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .theme-bg {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .theme-bg {
  background-color: #00a348 !important;
}
[data-theme=theme4] .theme-bg {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .theme-bg {
  background-color: #b99970 !important;
}
[data-theme=theme6] .theme-bg {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .theme-border {
  border-color: #fee238 !important;
}
[data-theme=theme1] .theme-border {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .theme-border {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .theme-border {
  border-color: #00a348 !important;
}
[data-theme=theme4] .theme-border {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .theme-border {
  border-color: #b99970 !important;
}
[data-theme=theme6] .theme-border {
  border-color: #a4a4d6 !important;
}
.gray-btn {
  background: #dddddd;
  color: #ffffff;
}
[data-theme=theme0] .theme-btn {
  border-color: #fee238 !important;
}
[data-theme=theme1] .theme-btn {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .theme-btn {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .theme-btn {
  border-color: #00a348 !important;
}
[data-theme=theme4] .theme-btn {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .theme-btn {
  border-color: #b99970 !important;
}
[data-theme=theme6] .theme-btn {
  border-color: #a4a4d6 !important;
}
[data-theme=theme0] .theme-btn {
  background-color: #fee238 !important;
}
[data-theme=theme1] .theme-btn {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .theme-btn {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .theme-btn {
  background-color: #00a348 !important;
}
[data-theme=theme4] .theme-btn {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .theme-btn {
  background-color: #b99970 !important;
}
[data-theme=theme6] .theme-btn {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] .theme-btn {
  color: #111111 !important;
}
[data-theme=theme1] .theme-btn {
  color: #ffffff !important;
}
[data-theme=theme2] .theme-btn {
  color: #ffffff !important;
}
[data-theme=theme3] .theme-btn {
  color: #ffffff !important;
}
[data-theme=theme4] .theme-btn {
  color: #ffffff !important;
}
[data-theme=theme5] .theme-btn {
  color: #fff !important;
}
[data-theme=theme6] .theme-btn {
  color: #ffffff !important;
}
[data-theme=theme0] .theme-linbtn {
  background: linear-gradient(0, #ffffff 0, rgba(255, 204, 0, 0.09) 100%) !important;
}
[data-theme=theme1] .theme-linbtn {
  background: linear-gradient(0, #ffffff 0, #ffe1e1 100%) !important;
}
[data-theme=theme2] .theme-linbtn {
  background: linear-gradient(0, #ffffff 0, #b3e8fb 100%) !important;
}
[data-theme=theme3] .theme-linbtn {
  background: linear-gradient(0, #ffffff 0, #c7ebd6 100%) !important;
}
[data-theme=theme4] .theme-linbtn {
  background: linear-gradient(0, #ffffff 0, #ffe5cb 100%) !important;
}
[data-theme=theme5] .theme-linbtn {
  background: linear-gradient(0, #ffffff 0, #ede4d9 100%) !important;
}
[data-theme=theme6] .theme-linbtn {
  background: linear-gradient(0, #ffffff 0, #d9d9ee 100%) !important;
}
[data-theme=theme0] .theme-linbtn {
  color: #ffffff !important;
}
[data-theme=theme1] .theme-linbtn {
  color: #ffffff !important;
}
[data-theme=theme2] .theme-linbtn {
  color: #ffffff !important;
}
[data-theme=theme3] .theme-linbtn {
  color: #ffffff !important;
}
[data-theme=theme4] .theme-linbtn {
  color: #ffffff !important;
}
[data-theme=theme5] .theme-linbtn {
  color: #ffffff !important;
}
[data-theme=theme6] .theme-linbtn {
  color: #ffffff !important;
}
.theme-borderbtn {
  background: none;
  border: 1px solid;
}
[data-theme=theme0] .theme-borderbtn {
  border-color: #fee238 !important;
}
[data-theme=theme1] .theme-borderbtn {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .theme-borderbtn {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .theme-borderbtn {
  border-color: #00a348 !important;
}
[data-theme=theme4] .theme-borderbtn {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .theme-borderbtn {
  border-color: #b99970 !important;
}
[data-theme=theme6] .theme-borderbtn {
  border-color: #a4a4d6 !important;
}
[data-theme=theme0] .theme-borderbtn {
  color: #fee238 !important;
}
[data-theme=theme1] .theme-borderbtn {
  color: #b91d32 !important;
}
[data-theme=theme2] .theme-borderbtn {
  color: #09b4f1 !important;
}
[data-theme=theme3] .theme-borderbtn {
  color: #00a348 !important;
}
[data-theme=theme4] .theme-borderbtn {
  color: #fd8103 !important;
}
[data-theme=theme5] .theme-borderbtn {
  color: #b99970 !important;
}
[data-theme=theme6] .theme-borderbtn {
  color: #a4a4d6 !important;
}
/*button*/
button {
  margin: 0;
  font-size: 28rpx;
}
button.btn-gcred {
  background: linear-gradient(90deg, #ff6b6b 4%, #f6220c 100%);
  border: none;
  color: #ffffff;
}
button.btn-red {
  border: 1px solid #f6220c;
  background: #f6220c;
  color: #ffffff;
}
button.btn-blue {
  background: #28a5ff;
  color: #ffffff;
}
button.btn-violet {
  background: #a824e4;
  color: #ffffff;
}
button.btn-yellow {
  background: #ffd127;
  color: #ffffff;
}
[data-theme=theme0] button.btn-normal {
  background-color: #fee238 !important;
}
[data-theme=theme1] button.btn-normal {
  background-color: #b91d32 !important;
}
[data-theme=theme2] button.btn-normal {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] button.btn-normal {
  background-color: #00a348 !important;
}
[data-theme=theme4] button.btn-normal {
  background-color: #fd8103 !important;
}
[data-theme=theme5] button.btn-normal {
  background-color: #b99970 !important;
}
[data-theme=theme6] button.btn-normal {
  background-color: #a4a4d6 !important;
}
[data-theme=theme0] button.btn-normal {
  color: #111111 !important;
}
[data-theme=theme1] button.btn-normal {
  color: #ffffff !important;
}
[data-theme=theme2] button.btn-normal {
  color: #ffffff !important;
}
[data-theme=theme3] button.btn-normal {
  color: #ffffff !important;
}
[data-theme=theme4] button.btn-normal {
  color: #ffffff !important;
}
[data-theme=theme5] button.btn-normal {
  color: #fff !important;
}
[data-theme=theme6] button.btn-normal {
  color: #ffffff !important;
}
button.btn-gray {
  background: #cccccc;
  background-color: #cccccc;
  color: #ffffff;
}
button.btn-red-border {
  background: #ffffff;
  border: 1px solid #f6220c;
  color: #f6220c;
}
button.btn-gray-border {
  background: #ffffff;
  border: 1px solid #cccccc;
  color: #333333;
}
button.btn-green {
  background: #44b549;
  color: #ffffff;
}
button::after {
  border: none;
}
button.btn-red[disabled][type=primary],
button.btn-gray-border[disabled][type=primary] {
  background-color: #bbbbbb;
  color: rgba(255, 255, 255, 0.6);
}
/*backgroud*/
.bg-white {
  background: #ffffff;
}
/*border*/
.br12 {
  border-radius: 12rpx;
}
.border {
  border: 1px solid #eeeeee;
}
.border-t {
  border-top: 1px solid #eeeeee;
}
.border-b {
  border-bottom: 1px solid #eeeeee;
}
.border-b-d9 {
  border-bottom: 1px solid #d9d9d9;
}
.border-b-e {
  border-bottom: 1px solid #eeeeee;
}
.border-b-d {
  border-bottom: 1px solid #d9d9d9;
}
.border-b-dash {
  border-bottom: 1rpx dashed #d9d9d9;
}
.border-red {
  border: 1px solid #f6220c;
}
/*position*/
.pr {
  position: relative;
}
.pa {
  position: absolute;
}
/**/
.top0 {
  top: 0;
}
.right0 {
  right: 0;
}
.bottom0 {
  bottom: 0;
}
.left0 {
  left: 0;
}
/*font*/
.f20 {
  font-size: 20rpx;
}
.f22 {
  font-size: 22rpx;
}
.f24 {
  font-size: 24rpx;
}
.f26 {
  font-size: 26rpx;
}
.f28 {
  font-size: 28rpx;
}
.f30 {
  font-size: 30rpx;
}
.f32 {
  font-size: 32rpx;
}
.f34 {
  font-size: 34rpx;
}
.f36 {
  font-size: 36rpx;
}
.f40 {
  font-size: 40rpx;
}
.f42 {
  font-size: 42rpx;
}
.f48 {
  font-size: 48rpx;
}
.f50 {
  font-size: 50rpx;
}
.f52 {
  font-size: 52rpx;
}
.f56 {
  font-size: 56rpx;
}
.f60 {
  font-size: 60rpx;
}
.f68 {
  font-size: 68rpx;
}
.fn {
  font-weight: 400;
}
.fb {
  font-weight: bold;
}
/*text align*/
.tc {
  text-align: center;
}
.tr {
  text-align: right;
}
/*overflow*/
.o-h {
  overflow: hidden;
}
.o-a {
  overflow: auto;
}
/*word*/
.w-b {
  word-wrap: break-word;
}
.w-b-a {
  word-wrap: break-all;
}
/*flex*/
.flex-1 {
  flex: 1;
}
/*icon*/
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.svg-icon {
  width: 32rpx;
  height: 32rpx;
  color: #cccccc;
}
.icon-box {
  display: flex;
  justify-content: center;
  align-items: center;
}
.iconfont {
  color: #cccccc;
}
.box-s-b {
  box-sizing: border-box;
}
/*width*/
.ww100 {
  width: 100%;
}
.ww50 {
  width: 50%;
}
.ww33 {
  width: 33.33333333%;
}
.ww25 {
  width: 25%;
}
.ww20 {
  width: 20%;
}
.hh100 {
  height: 100%;
}
/*padding*/
.p10 {
  padding: 10rpx;
}
.p20 {
  padding: 20rpx;
}
.p24 {
  padding: 24rpx;
}
.p26 {
  padding: 26rpx;
}
.p30 {
  padding: 30rpx;
}
.p-10-0 {
  padding: 10rpx 0;
}
.p-20-0 {
  padding: 20rpx 0;
}
.p-30-0 {
  padding: 30rpx 0;
}
.p-40-0 {
  padding: 40rpx 0;
}
.p-0-10 {
  padding: 0 10rpx;
}
.p-0-16 {
  padding: 0 16rpx;
}
.p-0-20 {
  padding: 0 20rpx;
}
.p-0-26 {
  padding: 0 26rpx;
}
.p-0-30 {
  padding: 0 30rpx;
}
.p-30-40 {
  padding: 30rpx 40rpx;
}
.p-20-40 {
  padding: 20rpx 40rpx;
}
.pt10 {
  padding-top: 10rpx;
}
.pt16 {
  padding-top: 16rpx;
}
.pt20 {
  padding-top: 20rpx;
}
.pt22 {
  padding-top: 22rpx;
}
.pt26 {
  padding-top: 26rpx;
}
.pt30 {
  padding-top: 30rpx;
}
.pt40 {
  padding-top: 40rpx;
}
.pt60 {
  padding-top: 60rpx;
}
.pr20 {
  padding-right: 20rpx;
}
.pr100 {
  padding-right: 100rpx;
}
.pr150 {
  padding-right: 150rpx;
}
.pb0 {
  padding-bottom: 0;
}
.pb10 {
  padding-bottom: 10rpx;
}
.pb20 {
  padding-bottom: 20rpx;
}
.pb30 {
  padding-bottom: 30rpx;
}
.pb60 {
  padding-bottom: 60rpx;
}
.pb14 {
  padding-bottom: 14rpx;
}
.pb38 {
  padding-bottom: 38rpx;
}
.pb100 {
  padding-bottom: 100rpx;
}
.pb200 {
  padding-bottom: 200rpx;
}
/*margin*/
.m20 {
  margin: 20rpx;
}
.m-0-10 {
  margin: 0 10rpx;
}
.m-0-20 {
  margin: 0 20rpx;
}
.mt6 {
  margin-top: 6rpx;
}
.mt10 {
  margin-top: 10rpx;
}
.mt16 {
  margin-top: 16rpx;
}
.mt20 {
  margin-top: 20rpx;
}
.mt24 {
  margin-top: 24rpx;
}
.mt30 {
  margin-top: 30rpx;
}
.mt36 {
  margin-top: 36rpx;
}
.mt40 {
  margin-top: 40rpx;
}
.mt50 {
  margin-top: 50rpx;
}
.mt60 {
  margin-top: 60rpx;
}
.mr8 {
  margin-right: 8rpx;
}
.mr10 {
  margin-right: 10rpx;
}
.mr12 {
  margin-right: 12rpx;
}
.mr16 {
  margin-right: 16rpx;
}
.mr20 {
  margin-right: 20rpx;
}
.mr30 {
  margin-right: 30rpx;
}
.mr40 {
  margin-right: 40rpx;
}
.mb6 {
  margin-bottom: 6rpx;
}
.mb10 {
  margin-bottom: 10rpx;
}
.mb16 {
  margin-bottom: 16rpx;
}
.mb20 {
  margin-bottom: 20rpx;
}
.mb23 {
  margin-bottom: 23rpx;
}
.mb30 {
  margin-bottom: 30rpx;
}
.mb40 {
  margin-bottom: 40rpx;
}
.mb48 {
  margin-bottom: 48rpx;
}
.mb60 {
  margin-bottom: 60rpx;
}
.ml10 {
  margin-left: 10rpx;
}
.ml15 {
  margin-left: 15rpx;
}
.ml18 {
  margin-left: 18rpx;
}
.ml20 {
  margin-left: 20rpx;
}
.ml26 {
  margin-left: 26rpx;
}
.ml30 {
  margin-left: 30rpx;
}
.ml80 {
  margin-left: 80rpx;
}
/*display*/
.d-f {
  display: flex;
}
.d-c-c {
  display: flex;
  justify-content: center;
  align-items: center;
}
.d-c-e {
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
.d-c-s {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}
.d-s-c {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.d-s-e {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
}
.d-s-s {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
.d-e-c {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.d-b-c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.d-b-s {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.d-a-c {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.d-a-s {
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
}
.d-c {
  flex-direction: column;
}
.d-b-e {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.d-e-e {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
.d-e-s {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}
.d-r {
  flex-direction: row;
}
.d-stretch {
  align-items: stretch;
  align-content: stretch;
}
.f-w {
  flex-wrap: wrap;
}
.f-s-0 {
  flex-shrink: 0;
}
.w-s-n {
  white-space: nowrap;
}
.text-l-t {
  text-decoration: line-through;
}
.lh100 {
  line-height: 100%;
}
.lh150 {
  line-height: 150%;
}
.lh200 {
  line-height: 200%;
}
.radius {
  border-radius: 50%;
}
.radius8 {
  border-radius: 8rpx;
}
/*ellipsis*/
.text-ellipsis {
  display: -webkit-box;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.text-ellipsis-2 {
  display: -webkit-box;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
/*btn*/
uni-button:after {
  border: 0;
}
.text-d-line {
  text-decoration: line-through;
}
image {
  display: block;
  vertical-align: top;
  vertical-align: text-top;
  vertical-align: bottom;
  vertical-align: text-bottom;
  font-size: 0;
}
/* image{ background: url(./static/default.png)  center center no-repeat; background-size:100% 100%;} */
/**/
.none-data-box {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 80rpx 30rpx;
}
.none-data-box image {
  background: none;
  width: 484rpx;
}
.none-data-box text {
  padding-top: 30rpx;
  font-size: 30rpx;
  color: #999999;
}
/*search*/
/*search*/
.index-search-box {
  background: #efefef;
  padding: 16rpx 20rpx 18rpx 20rpx;
  border-bottom: 1px solid #d8d8d8;
}
.index-search-box-cate {
  background: #ffcc00;
  padding: 20rpx;
  padding-bottom: 60rpx;
  /* border-bottom: 1px solid #d8d8d8; */
}
.index-search {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 20rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #ffffff;
  border-radius: 16rpx;
  border: 1px solid #d7d7d7;
  overflow: hidden;
  font-size: 30rpx;
  color: #999;
  box-sizing: border-box;
}
.index-search-cate {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #f2f2f2;
  border-radius: 30rpx;
  border: 1px solid #d7d7d7;
  overflow: hidden;
  font-size: 26rpx;
  color: #999;
  box-sizing: border-box;
}
.index-search-box_re {
  background: #ffffff;
  padding: 14rpx 20rpx 18rpx 20rpx;
  border-bottom: none;
}
.index-search_re {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 20rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #f2f2f2;
  border-radius: 30rpx;
  border: none;
  overflow: hidden;
  font-size: 26rpx;
  color: #999;
  box-sizing: border-box;
}
.index-search .svg-icon {
  margin-right: 10rpx;
}
/*group*/
.group {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
}
.group-white {
  margin-top: 30rpx;
  padding: 10rpx 30rpx;
  border-radius: 16rpx;
  background: #ffffff;
}
.group-hd {
  display: flex;
  justify-content: space-between;
  height: 90rpx;
}
.group-hd .left,
.group-hd .right {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.group-hd .left .name {
  margin-right: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  line-height: 90rpx;
}
.group-hd .left .min-name {
  margin-right: 10rpx;
  font-size: 30rpx;
  line-height: 90rpx;
}
.group-hd .left .svg-icon {
  margin-right: 10rpx;
}
.group-hd .left .num {
  font-size: 30rpx;
}
.group-hd .right .more {
  font-size: 28rpx;
  color: #999999;
  line-height: 90rpx;
}
.group-hd .right .svg-icon {
  margin-left: 10rpx;
}
.group-bd .list {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  justify-content: space-between;
}
/*product*/
.product-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.product-list .item {
  width: 340rpx;
  background: #ffffff;
  margin-top: 30rpx;
  border-radius: 8rpx;
}
.product-list .item image {
  width: 340rpx;
  height: 340rpx;
}
.product-list .item .info {
  padding: 0 20rpx;
}
.product-list .item .name {
  height: 80rpx;
  line-height: 40rpx;
}
.product-list .item .price {
  height: 80rpx;
  line-height: 80rpx;
  color: #f6220c;
}
.product-list .item .unit {
  font-size: 22rpx;
}
.product-list .item .num {
  font-size: 44rpx;
}
/*comment*/
.product-comment .group-hd,
.product-content .group-hd {
  padding: 0 30rpx;
}
.comment-list {
  padding: 0 30rpx;
}
.comment-list .item {
  padding: 20rpx 0;
  border-top: 1px solid #dddddd;
}
.comment-list .cmt-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.comment-list .cmt-user .left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.comment-list .cmt-user .photo {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 40rpx;
  background: #eeeeee;
}
.comment-list .cmt-user .name {
  font-size: 24rpx;
}
.comment-list .cmt-user .datetime {
  font-size: 24rpx;
  color: #cccccc;
}
/**/
.one-product .cover,
.one-product .cover image {
  width: 160rpx;
  height: 160rpx;
}
.one-product .pro-info {
  padding: 0 30rpx;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 28rpx;
  color: #666666;
}
.one-product .price {
  color: #e2231a;
}
/*order state*/
.order-state {
  position: relative;
  padding: 30rpx;
  background: #e2231a;
  color: #ffffff;
  opacity: 0.8;
}
.order-state .icon-box .iconfont {
  font-size: 50rpx;
  color: #ffffff;
}
.order-state .state-cont {
  margin-left: 20rpx;
  font-size: 24rpx;
}
.order-state .dot-bg {
  width: 60rpx;
  height: 40rpx;
  right: 200rpx;
  top: 0;
  border-radius: 0 0 50% 50%/50%;
}
.order-state .dot-bg,
.order-state::after {
  content: "";
  position: absolute;
  background-image: linear-gradient(37deg, rgba(255, 255, 255, 0) 8%, rgba(255, 255, 255, 0.1) 78%);
}
.order-state:before {
  content: "";
  position: absolute;
  width: 50px;
  height: 20px;
  left: 0;
  bottom: 0;
  border-radius: 0 100% 0 0;
  background-image: linear-gradient(30deg, rgba(255, 255, 255, 0) 32%, rgba(255, 255, 255, 0.1) 69%);
}
.order-state::after {
  width: 50rpx;
  height: 90rpx;
  right: 0;
  bottom: 0;
  border-radius: 100% 0 0 100%/50%;
}
/*confirm order*/
.add-address {
  padding: 20rpx;
}
.address-defalut-wrap {
  padding: 20rpx 30rpx;
  background: #ffffff;
}
.address-defalut-wrap .info {
  display: flex;
  font-size: 24rpx;
}
.address-defalut-wrap .info .state,
.address-defalut-wrap .info .type {
  padding: 2rpx 10rpx;
  margin-right: 10rpx;
  background: #f6220c;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 22rpx;
}
.address-defalut-wrap .info .type {
  background: #007aff;
}
.address-defalut-wrap .info .province-c-a {
  color: #666666;
  line-height: 1.2;
}
.address-defalut-wrap .address {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.address-defalut-wrap .address text {
  flex: 1;
  font-size: 32rpx;
}
.address-defalut-wrap .address .icon-box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 60rpx;
  height: 60rpx;
}
.address-defalut-wrap .user {
  margin-top: 16rpx;
  font-size: 24rpx;
}
.address-defalut-wrap .user .name {
  margin-right: 10rpx;
}
.addree-bottom {
  width: 750rpx;
  height: 16rpx;
  background: #f2f2f2;
  display: flex;
  overflow: hidden;
}
.addree-bottom .stripe {
  flex: 1;
  display: flex;
}
.addree-bottom .stripe text {
  display: block;
  height: 10rpx;
  transform: skew(-45deg);
}
.addree-bottom .stripe .red {
  flex: 4;
  background: #f6220c;
}
.addree-bottom .stripe .white {
  flex: 1;
  background: #ffffff;
}
.addree-bottom .stripe .blue {
  flex: 4;
  background: #8bb5e8;
}
.vender {
  padding: 0 30rpx;
  padding-top: 30rpx;
  border-radius: 10px;
  position: relative;
  z-index: 20;
  margin-top: 0;
}
.vender,
.wrap .buy-checkout,
.buyer-message {
  margin-top: 20rpx;
  background: #ffffff;
}
.vender .group-hd {
  padding: 0 30rpx;
  border-bottom: 2rpx solid #eeeeee;
}
.vender .list {
  border-bottom: 2rpx solid #eeeeee;
  border-top: 2rpx solid #eeeeee;
}
.vender .list .item {
  /* display: flex; */
  padding: 24rpx 0 24rpx 0;
}
.vender .list .cover {
  width: 148rpx;
  height: 148rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  margin-right: 30rpx;
}
.vender .list .cover image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}
.vender .list .info {
  flex: 1;
  box-sizing: border-box;
  overflow: hidden;
}
.vender .list .title,
.vender .list .describe {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.vender .list .title {
  color: #3b3b3b;
}
.vender .list .describe {
  font-size: 24rpx;
  color: #999999;
}
.vender .list .price {
  color: #333333;
  font-size: 20rpx;
}
.vender .list .price .num {
  padding: 0 4rpx;
  font-size: 32rpx;
}
.vender .level-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vender .other_box .key {
  font-size: 26rpx;
  color: #282828;
  white-space: nowrap;
}
.vender .level-box .num-wrap {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.vender .level-box .icon-box {
  width: 60rpx;
  height: 60rpx;
  border: 1px solid #dddddd;
  background: #f7f7f7;
}
.vender .level-box .text-wrap {
  margin: 0 4rpx;
  height: 60rpx;
  line-height: 0;
  border: 1px solid #dddddd;
  background: #f7f7f7;
}
.vender .level-box .text-wrap input {
  padding: 0 10rpx;
  height: 60rpx;
  line-height: 0;
  text-align: center;
  width: 80rpx;
  font-size: 24rpx;
}
.vender .total-box {
  height: 80rpx;
  line-height: 80rpx;
  text-align: right;
  width: 100%;
  box-sizing: border-box;
  font-size: 22rpx;
}
.buy-checkout .item {
  min-height: 50rpx;
  line-height: 50rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #eeeeee;
  font-size: 26rpx;
}
.buy-checkout .item:last-child {
  border: none;
}
.buyer-message {
  padding: 20rpx;
  font-size: 28rpx;
}
.buyer-message .textarea {
  width: 100%;
  height: 60rpx;
  line-height: 60rpx;
  padding: 10rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  background: #eeeeee;
}
.foot-pay-btns {
  position: fixed;
  padding: 0 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  right: 0;
  bottom: 0;
  left: 0;
  height: 120rpx;
  background: #ffffff;
  border-top: 1px solid #eeeeee;
  z-index: 99;
}
[data-theme=theme0] .foot-pay-btns .price,
[data-theme=theme0] .vender .total-box .price,
[data-theme=theme0] .buy-checkout .price {
  color: #fa301b !important;
}
[data-theme=theme1] .foot-pay-btns .price,
[data-theme=theme1] .vender .total-box .price,
[data-theme=theme1] .buy-checkout .price {
  color: #b91d32 !important;
}
[data-theme=theme2] .foot-pay-btns .price,
[data-theme=theme2] .vender .total-box .price,
[data-theme=theme2] .buy-checkout .price {
  color: #fb5032 !important;
}
[data-theme=theme3] .foot-pay-btns .price,
[data-theme=theme3] .vender .total-box .price,
[data-theme=theme3] .buy-checkout .price {
  color: #e6444d !important;
}
[data-theme=theme4] .foot-pay-btns .price,
[data-theme=theme4] .vender .total-box .price,
[data-theme=theme4] .buy-checkout .price {
  color: #111111 !important;
}
[data-theme=theme5] .foot-pay-btns .price,
[data-theme=theme5] .vender .total-box .price,
[data-theme=theme5] .buy-checkout .price {
  color: #111111 !important;
}
[data-theme=theme6] .foot-pay-btns .price,
[data-theme=theme6] .vender .total-box .price,
[data-theme=theme6] .buy-checkout .price {
  color: #111111 !important;
}
.foot-pay-btns .price .num {
  font-size: 44rpx;
  font-weight: bold;
}
.foot-pay-btns button {
  margin: 0;
  font-size: 32rpx;
  padding: 0 50rpx;
  height: 84rpx;
  line-height: 84rpx;
  border-radius: 50rpx;
}
.buy-checkout .iconfont.icon-weixin {
  color: #24db5a;
  font-size: 50rpx;
}
.buy-checkout .iconfont.icon-zhifubao {
  color: #06b4fd;
  font-size: 50rpx;
}
.buy-checkout .iconfont.icon-yue {
  color: #ffcc00;
  font-size: 50rpx;
}
[data-theme=theme0] .buy-checkout .item.active .iconfont.icon-xuanze {
  color: #fee238 !important;
}
[data-theme=theme1] .buy-checkout .item.active .iconfont.icon-xuanze {
  color: #b91d32 !important;
}
[data-theme=theme2] .buy-checkout .item.active .iconfont.icon-xuanze {
  color: #09b4f1 !important;
}
[data-theme=theme3] .buy-checkout .item.active .iconfont.icon-xuanze {
  color: #00a348 !important;
}
[data-theme=theme4] .buy-checkout .item.active .iconfont.icon-xuanze {
  color: #fd8103 !important;
}
[data-theme=theme5] .buy-checkout .item.active .iconfont.icon-xuanze {
  color: #b99970 !important;
}
[data-theme=theme6] .buy-checkout .item.active .iconfont.icon-xuanze {
  color: #a4a4d6 !important;
}
/*more upload img*/
.upload-list {
  flex-wrap: wrap;
}
.upload-list .item {
  width: 220rpx;
  height: 220rpx;
  margin-top: 20rpx;
  margin-right: 16rpx;
  border: 1px solid #dddddd;
}
.upload-list .item:nth-child(3n),
.upload-list .item.upload-btn {
  margin-right: 0;
}
.upload-list .item,
.upload-list .item image {
  width: 214rpx;
  height: 214rpx;
}
.upload-list .upload-btn .iconfont {
  font-size: 60rpx;
}
/*coupon*/
.coupon-item {
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.coupon-disabled {
  position: absolute;
  z-index: 0;
  width: 146rpx;
  height: 146rpx;
  right: 161rpx;
}
.coupon-item-gray {
  color: #9a9a9a;
  filter: grayscale(100%);
  /* linear-gradient(-128deg, #888888, #999999); */
}
.coupon-item .operation {
  position: relative;
  height: 100%;
  flex: 1;
  overflow: hidden;
  box-sizing: border-box;
}
.range_item .coupon-btn {
  height: 52rpx;
  padding: 0 35rpx;
  min-width: 144rpx;
  box-sizing: border-box;
  line-height: 52rpx;
  border-radius: 26rpx;
  text-align: center;
}
.coupon-item .operation .coupon-content {
  height: 100%;
  padding-left: 30rpx;
  padding-right: 30rpx;
  padding-top: 50rpx;
  box-sizing: border-box;
}
.coupon-item .btns {
  padding: 0 30rpx;
  width: 30rpx;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
}
.coupon-item .right-box {
  padding-right: 30rpx;
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  right: 0;
}
.coupon-item .btns button {
  text-align: 0;
  padding: 0;
  width: 30rpx;
  border-radius: 0;
  font-size: 30rpx;
  line-height: 36rpx;
  background: none;
}
.rule-wrap {
  overflow: hidden;
  height: 0;
}
.rule-wrap.rule-wrap-auto {
  height: auto;
}
.item-wrap {
  margin: 0 20rpx;
  background: #ffffff;
  border-radius: 15rpx;
  margin-top: 24rpx;
}
.item-wrap .range_item {
  height: 86rpx;
  margin: 0 29rpx;
  border-top: 1px dashed #eee;
}
.item-wrap .rule {
  padding: 30rpx;
  box-sizing: border-box;
  border-top: dashed 1px #cccccc;
  background: #ffffff;
}
/*top-tab*/
.top-tabbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  box-sizing: border-box;
  background: #ffffff;
  border-bottom: 1px solid #f2f2f2;
}
.tab-item {
  flex: 1;
  height: 96rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #999999;
}
.tab-item.active {
  /* border-bottom: 2px solid #f6220c; */
  margin-bottom: -2px;
  color: #333333;
  font-size: 28rpx;
  font-family: PingFang SC;
  position: relative;
}
.tab-item.active::after {
  content: "";
  width: 80rpx;
  height: 6rpx;
  border-radius: 4rpx;
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  bottom: 2rpx;
}
[data-theme=theme0] .tab-item.active::after {
  background-color: #fee238 !important;
}
[data-theme=theme1] .tab-item.active::after {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .tab-item.active::after {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .tab-item.active::after {
  background-color: #00a348 !important;
}
[data-theme=theme4] .tab-item.active::after {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .tab-item.active::after {
  background-color: #b99970 !important;
}
[data-theme=theme6] .tab-item.active::after {
  background-color: #a4a4d6 !important;
}
.active-top-tab {
  /* position: absolute; */
  /* bottom: 125rpx; */
  /* z-index: 22; */
  /* width: 100%; */
}
/*列表刷新*/
.top-refresh {
  overflow: hidden;
  height: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: height 0.5s;
}
.top-refresh.open {
  height: 96rpx;
}
.top-refresh .circle {
  width: 30rpx;
  height: 30rpx;
  margin: 0 10rpx;
  background: #cccccc;
  border-radius: 50%;
  transform: scale(0);
  animation: bulge 2s infinite ease-in-out;
}
@keyframes bulge {
50% {
    transform: scale(1);
}
}
.top-refresh .circle:nth-child(1) {
  animation-delay: 0s;
}
.top-refresh .circle:nth-child(2) {
  animation-delay: 0.25s;
}
.top-refresh .circle:nth-child(3) {
  animation-delay: 0.5s;
}
/*foot-btns*/
.foot-btns {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  height: 90rpx;
  padding: 0 30rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  z-index: 99;
  background: #ffffff;
  box-shadow: 0 -2rpx 8rpx 0 rgba(0, 0, 0, 0.1);
}
/*cover*/
/*overflow*/
.o-h {
  overflow: hidden;
}
/*word*/
.w-b {
  word-wrap: break-word;
}
.status_bar {
  height: var(--status-bar-height);
  width: 100%;
  background-color: #f6220c;
}
.top_view {
  height: var(--status-bar-height);
  width: 100%;
  position: fixed;
  background-color: #f6220c;
  top: 0;
  z-index: 9999;
}
/*  */
.select_spec {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 26rpx;
  color: #333333;
}
.state_top {
  width: 100%;
  height: 50rpx;
}
.reg180 {
  padding-right: 20rpx;
  text-align: right;
  transform: rotateY(180deg);
  position: absolute;
  bottom: 0;
  top: 0;
  margin: auto;
}
.cashier-pop.pop-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}
.cashier-pop.pop-bg .pop-content {
  position: fixed;
  z-index: 1001;
  bottom: 0;
  width: 100%;
  height: 719rpx;
  padding: 40rpx 24rpx 0 23rpx;
  box-sizing: border-box;
  transform: translate3d(0, 0, 0);
  transition: transform 0.2s cubic-bezier(0, 0, 0.25, 1);
  background-color: #ffffff;
  border-radius: 15rpx 15rpx 0rpx 0rpx;
}
.cashier-pop.pop-bg .pop-content .icon.icon-guanbi {
  background: #dedede;
  border-radius: 50%;
  color: #ffffff;
  position: absolute;
  right: 26rpx;
  top: 40rpx;
  font-size: 22rpx;
  display: flex;
  width: 40rpx;
  height: 40rpx;
  justify-content: center;
  align-items: center;
}
.cashier-pop .close-img {
  width: 32rpx;
  height: 32rpx;
  position: absolute;
  right: 32rpx;
  top: 32rpx;
}
.cashier-pop.pop-bg.close {
  height: 0;
}
.cashier-pop.pop-bg.close .pop-content {
  transform: translate3d(0, 2000rpx, 0);
}
.cashier-pop .cashier-item {
  height: 89rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eeeeee;
}
.cashier-pop .cashier-item .icon-box {
  width: 38rpx;
  height: 38rpx;
  border: 1px solid #dddddd;
  border-radius: 50%;
}
.cashier-pop .cashier-item .icon-box .icon-tijiaochenggong {
  font-size: 26rpx;
  color: #ffffff;
}
.cashier-pop .cashier-item .icon-box.border {
  border-radius: 50%;
}
.cashier-pop .cashier-item.active .icon-box {
  border: 1px solid #72deed;
}
[data-theme=theme0] .cashier-pop .cashier-item.active .icon-box {
  border-color: #fee238 !important;
}
[data-theme=theme1] .cashier-pop .cashier-item.active .icon-box {
  border-color: #b91d32 !important;
}
[data-theme=theme2] .cashier-pop .cashier-item.active .icon-box {
  border-color: #09b4f1 !important;
}
[data-theme=theme3] .cashier-pop .cashier-item.active .icon-box {
  border-color: #00a348 !important;
}
[data-theme=theme4] .cashier-pop .cashier-item.active .icon-box {
  border-color: #fd8103 !important;
}
[data-theme=theme5] .cashier-pop .cashier-item.active .icon-box {
  border-color: #b99970 !important;
}
[data-theme=theme6] .cashier-pop .cashier-item.active .icon-box {
  border-color: #a4a4d6 !important;
}
[data-theme=theme0] .cashier-pop .cashier-item.active .icon-box {
  background-color: #fee238 !important;
}
[data-theme=theme1] .cashier-pop .cashier-item.active .icon-box {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .cashier-pop .cashier-item.active .icon-box {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .cashier-pop .cashier-item.active .icon-box {
  background-color: #00a348 !important;
}
[data-theme=theme4] .cashier-pop .cashier-item.active .icon-box {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .cashier-pop .cashier-item.active .icon-box {
  background-color: #b99970 !important;
}
[data-theme=theme6] .cashier-pop .cashier-item.active .icon-box {
  background-color: #a4a4d6 !important;
}
.cashier-pop .pay-btn {
  width: 710rpx;
  height: 96rpx;
  font-size: 32rpx;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 20rpx;
  bottom: 20rpx;
  border-radius: 48rpx;
}
[data-theme=theme0] .cashier-pop .pay-btn {
  background-color: #fee238 !important;
}
[data-theme=theme1] .cashier-pop .pay-btn {
  background-color: #b91d32 !important;
}
[data-theme=theme2] .cashier-pop .pay-btn {
  background-color: #09b4f1 !important;
}
[data-theme=theme3] .cashier-pop .pay-btn {
  background-color: #00a348 !important;
}
[data-theme=theme4] .cashier-pop .pay-btn {
  background-color: #fd8103 !important;
}
[data-theme=theme5] .cashier-pop .pay-btn {
  background-color: #b99970 !important;
}
[data-theme=theme6] .cashier-pop .pay-btn {
  background-color: #a4a4d6 !important;
}
page {
  background: #f6f6f6;
  font-size: 0.75rem;
  min-height: 100vh;
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}