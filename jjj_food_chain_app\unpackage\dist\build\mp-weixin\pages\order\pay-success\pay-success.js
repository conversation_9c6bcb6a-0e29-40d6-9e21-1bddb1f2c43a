"use strict";const e=require("../../../common/vendor.js"),d={data:()=>({loadding:!0,indicatorDots:!0,autoplay:!0,interval:2e3,duration:500,order_id:0,detail:{order_status:[],address:{region:[]},product:[],pay_type:[],delivery_type:[],pay_status:[]},codeImage:""}),onLoad(e){this.order_id=e.order_id},mounted(){e.index.showLoading({title:"加载中",mask:!0}),this.getData()},methods:{getData(){let d=this,o=d.order_id;d._get("user.order/paySuccess",{order_id:o},(function(o){d.detail=o.data.order,d.codeImage=o.data.order.qrcode,d.loadding=!1,e.index.hideLoading()}))},goHome(){this.gotoPage("/pages/index/index")},goMyorder(){this.gotoPage("/pages/order/myorder")}}};const o=e._export_sfc(d,[["render",function(d,o,t,a,r,i){return e.e({a:!r.loadding},r.loadding?{}:e.e({b:e.t(r.detail.pay_price),c:r.detail.callNo},r.detail.callNo?{d:e.t(r.detail.callNo)}:{},{e:e.o(((...e)=>i.goMyorder&&i.goMyorder(...e)))}),{f:d.theme(),g:e.n(d.theme()||"")})}]]);wx.createPage(o);
