"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const uniLoadMore = () => "../../components/uni-load-more.js";
const _sfc_main = {
  components: {
    uniLoadMore
  },
  data() {
    return {
      /*顶部刷新*/
      topRefresh: false,
      /*数据*/
      listData: [],
      /*数据类别*/
      dataType: 1,
      /*订单id*/
      order_id: 0,
      /*最后一页码数*/
      last_page: 0,
      /*当前页面*/
      page: 1,
      /*每页条数*/
      list_rows: 10,
      /*有没有等多*/
      no_more: false,
      /*是否正在加载*/
      loading: true
    };
  },
  computed: {
    /*加载中状态*/
    loadingType() {
      if (this.loading) {
        return 1;
      } else {
        if (this.listData.length != 0 && this.no_more) {
          return 2;
        } else {
          return 0;
        }
      }
    }
  },
  onShow() {
    this.initData();
    this.getData();
  },
  onReachBottom() {
    let self = this;
    if (self.page < self.last_page) {
      self.page++;
      self.getData();
    }
    self.no_more = true;
  },
  methods: {
    initData() {
      let self = this;
      self.page = 1;
      self.listData = [];
      self.no_more = false;
    },
    /*状态切换*/
    orderStateFunc(e) {
      let self = this;
      if (self.loading) {
        return;
      }
      if (self.dataType != e) {
        self.page = 1;
        self.loading = true;
        self.listData = [];
        self.dataType = e;
        self.getData();
      }
    },
    /*获取数据*/
    getData() {
      if (!this.getUserId()) {
        this.loading = false;
        return;
      }
      let self = this;
      self.loading = true;
      self._get(
        "user.order/lists",
        {
          dataType: self.dataType,
          page: self.page,
          list_rows: self.list_rows
        },
        function(res) {
          self.loading = false;
          self.listData = self.listData.concat(res.data.list.data);
          self.last_page = res.data.list.last_page;
          if (res.data.list.last_page <= 1) {
            self.no_more = true;
          } else {
            self.no_more = false;
          }
        }
      );
    },
    /*跳转页面*/
    gotoOrder(e) {
      this.gotoPage("/pages/order/order-detail?order_id=" + e);
    },
    /*支付方式选择*/
    onPayOrder(orderId) {
      let self = this;
      let pages = "/pages/order/cashier?order_type=10&order_id=" + orderId;
      self.gotoPage(pages, "reLaunch");
    },
    /*确认收货*/
    orderReceipt(order_id) {
      let self = this;
      common_vendor.index.showModal({
        title: "提示",
        content: "您确定要收货吗?",
        success: function(o) {
          if (o.confirm) {
            common_vendor.index.showLoading({
              title: "正在处理",
              mask: true
            });
            self._post(
              "user.order/receipt",
              {
                order_id
              },
              function(res) {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: res.msg,
                  duration: 2e3,
                  icon: "success"
                });
                self.listData = [];
                self.getData();
              }
            );
          } else {
            common_vendor.index.showToast({
              title: "取消收货",
              duration: 1e3,
              icon: "none"
            });
          }
        }
      });
    },
    /*取消订单*/
    cancelOrder(e) {
      let self = this;
      let order_id = e;
      common_vendor.index.showModal({
        title: "提示",
        content: "您确定要取消吗?",
        success: function(o) {
          if (o.confirm) {
            common_vendor.index.showLoading({
              title: "正在处理",
              mask: true
            });
            self._get(
              "user.order/cancel",
              {
                order_id
              },
              function(res) {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "操作成功",
                  duration: 2e3,
                  icon: "success"
                });
                self.listData = [];
                self.getData();
              }
            );
          }
        }
      });
    },
    gohome() {
      this.gotoPage("/pages/index/index");
    }
  }
};
if (!Array) {
  const _component_uni_load_more = common_vendor.resolveComponent("uni-load-more");
  _component_uni_load_more();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.n($data.dataType == 1 ? "tab-item active" : "tab-item"),
    b: common_vendor.o(($event) => $options.orderStateFunc(1)),
    c: common_vendor.n($data.dataType == 2 ? "tab-item active" : "tab-item"),
    d: common_vendor.o(($event) => $options.orderStateFunc(2)),
    e: common_vendor.f($data.listData, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.order_label),
        b: item.supplier
      }, item.supplier ? {
        c: common_vendor.t(item.supplier.name)
      } : {}, {
        d: common_vendor.t(item.state_text),
        e: common_vendor.f(item.product, (img, num, i1) => {
          return common_vendor.e({
            a: num <= 2
          }, num <= 2 ? {
            b: img.image ? img.image.file_path : "",
            c: common_vendor.t(img.product_name),
            d: num
          } : {});
        }),
        f: common_vendor.t(item.pay_price),
        g: common_vendor.t(item.productNum),
        h: common_vendor.t(item.create_time),
        i: item.order_status.value == 10
      }, item.order_status.value == 10 ? common_vendor.e({
        j: item.pay_status.value == 10 && item.order_source != 30
      }, item.pay_status.value == 10 && item.order_source != 30 ? {
        k: common_vendor.o(($event) => $options.cancelOrder(item.order_id), index)
      } : {}, {
        l: item.pay_status.value == 10 && item.order_source != 30
      }, item.pay_status.value == 10 && item.order_source != 30 ? {
        m: common_vendor.o(($event) => $options.onPayOrder(item.order_id), index)
      } : {}, {
        n: item.pay_status.value == 10 && item.order_source == 30 && item.supplier.pay_open == 1
      }, item.pay_status.value == 10 && item.order_source == 30 && item.supplier.pay_open == 1 ? {
        o: common_vendor.o(($event) => $options.onPayOrder(item.order_id), index)
      } : {}) : {}, {
        p: index,
        q: common_vendor.o(($event) => $options.gotoOrder(item.order_id), index)
      });
    }),
    f: $data.listData.length == 0 && !$data.loading
  }, $data.listData.length == 0 && !$data.loading ? {
    g: common_assets._imports_0$2,
    h: common_vendor.o(($event) => _ctx.gotoPage("/pages/index/index"))
  } : {
    i: common_vendor.p({
      loadingType: $options.loadingType
    })
  }, {
    j: _ctx.theme(),
    k: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/myorder.js.map
