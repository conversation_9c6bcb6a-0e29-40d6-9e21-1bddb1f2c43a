"use strict";
const common_vendor = require("../../common/vendor.js");
const common_pay = require("../../common/pay.js");
const cashier = () => "../../components/cashier/cashier.js";
const _sfc_main = {
  components: {
    cashier
  },
  data() {
    return {
      checkedPay: [10, 20],
      /*支付方式*/
      pay_type: 20,
      /*是否加载完成*/
      loadding: true,
      indicatorDots: true,
      autoplay: true,
      interval: 2e3,
      duration: 500,
      /*是否显示支付类别弹窗*/
      isPayPopup: false,
      /*订单id*/
      order_id: 0,
      /*订单详情*/
      detail: {
        order_status: [],
        address: {
          region: []
        },
        product: [],
        pay_type: [],
        delivery_type: [],
        pay_status: []
      },
      extractStore: {},
      /*是否显示拼团*/
      is_fightgroup: false,
      /*是否显示支付宝支付，只有在h5，非微信内打开才显示*/
      showAlipay: false,
      qrimg: ""
    };
  },
  onLoad(e) {
    this.order_id = e.order_id;
  },
  mounted() {
    common_vendor.index.showLoading({
      title: "加载中"
    });
    this.getData();
  },
  methods: {
    /*获取数据*/
    getData() {
      let self = this;
      let order_id = self.order_id;
      self._get(
        "user.order/detail",
        {
          order_id
        },
        function(res) {
          self.detail = res.data.order;
          self.extractStore = res.data.order.extractStore;
          self.loadding = false;
          common_vendor.index.hideLoading();
        }
      );
    },
    /*显示支付方式*/
    hidePopupFunc() {
      this.isPayPopup = false;
    },
    /*取消订单*/
    cancelOrder(e) {
      let self = this;
      let order_id = e;
      common_vendor.index.showModal({
        title: "提示",
        content: "您确定要取消当前订单吗?",
        success: function(o) {
          if (o.confirm) {
            common_vendor.index.showLoading({
              title: "正在处理"
            });
            self._get(
              "user.order/cancel",
              {
                order_id
              },
              function(res) {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "操作成功",
                  duration: 2e3,
                  icon: "success"
                });
                self.getData();
              }
            );
          }
        }
      });
    },
    /*确认收货*/
    orderReceipt(order_id) {
      let self = this;
      common_vendor.index.showModal({
        title: "提示",
        content: "您确定要收货吗?",
        success: function(o) {
          if (o.confirm) {
            common_vendor.index.showLoading({
              title: "正在处理"
            });
            self._post(
              "user.order/receipt",
              {
                order_id
              },
              function(res) {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: res.msg,
                  duration: 2e3,
                  icon: "success"
                });
                self.getData();
              }
            );
          }
        }
      });
    },
    /*查看物流*/
    gotoExpress(order_id) {
      common_vendor.index.navigateTo({
        url: "/pages/order/express/express?order_id=" + order_id
      });
    },
    /*申请售后*/
    onApplyRefund(e) {
      common_vendor.index.navigateTo({
        url: "/pages/order/refund/apply/apply?order_product_id=" + e
      });
    },
    /*去支付*/
    payTypeFunc(payType) {
      let self = this;
      self.pay_type = payType;
    },
    subFunc(e) {
      let self = this;
      if (!self.isPayPopup) {
        return;
      }
      self.isPayPopup = false;
      let order_id = self.order_id;
      common_vendor.index.showLoading({
        title: "加载中"
      });
      self._post(
        "user.order/pay",
        {
          payType: e,
          order_id,
          pay_source: self.getPlatform()
        },
        function(res) {
          common_vendor.index.hideLoading();
          common_pay.pay(res, self);
        }
      );
    },
    /*支付方式选择*/
    onPayOrder(orderId) {
      let self = this;
      self.isPayPopup = true;
      self.order_id = orderId;
    }
  }
};
if (!Array) {
  const _easycom_cashier2 = common_vendor.resolveComponent("cashier");
  _easycom_cashier2();
}
const _easycom_cashier = () => "../../components/cashier/cashier.js";
if (!Math) {
  _easycom_cashier();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.loadding
  }, !$data.loadding ? common_vendor.e({
    b: common_vendor.t($data.detail.state_text),
    c: $data.detail.callNo && $data.detail.pay_status.value != 10 && ($data.detail.order_status.value == 10 || $data.detail.order_status.value == 30)
  }, $data.detail.callNo && $data.detail.pay_status.value != 10 && ($data.detail.order_status.value == 10 || $data.detail.order_status.value == 30) ? {
    d: common_vendor.t($data.detail.callNo)
  } : {}, {
    e: $data.detail.pay_status.value == 10 && $data.detail.order_status.value != 20 && $data.detail.pay_end_time
  }, $data.detail.pay_status.value == 10 && $data.detail.order_status.value != 20 && $data.detail.pay_end_time ? {
    f: common_vendor.t($data.detail.pay_end_time)
  } : {}, {
    g: $data.detail.order_type != 1 && $data.detail.delivery_type.value == 10 && $data.detail.order_status.value == 10 && $data.detail.deliver_status >= 1 && $data.detail.pay_status.value != 10
  }, $data.detail.order_type != 1 && $data.detail.delivery_type.value == 10 && $data.detail.order_status.value == 10 && $data.detail.deliver_status >= 1 && $data.detail.pay_status.value != 10 ? common_vendor.e({
    h: $data.detail.deliver_status == 1
  }, $data.detail.deliver_status == 1 ? {} : {}, {
    i: $data.detail.deliver_status == 2
  }, $data.detail.deliver_status == 2 ? {} : {}, {
    j: $data.detail.deliver_status == 3
  }, $data.detail.deliver_status == 3 ? {} : {}, {
    k: $data.detail.deliver_status == 4
  }, $data.detail.deliver_status == 4 ? {} : {}, {
    l: _ctx.deliver_source
  }, _ctx.deliver_source ? {} : {}, {
    m: common_vendor.o((...args) => _ctx.gotoMap && _ctx.gotoMap(...args))
  }) : {}, {
    n: $data.detail.supplier.logo || "/static/default.png",
    o: common_vendor.t($data.detail.supplier.name),
    p: $data.detail.supplier
  }, $data.detail.supplier ? {
    q: common_vendor.o(($event) => _ctx.openmap($data.detail.supplier.latitude, $data.detail.supplier.longitude, $data.detail.supplier.name, $data.detail.supplier.address)),
    r: common_vendor.o(($event) => _ctx.callPhone($data.detail.supplier.link_phone))
  } : {}, {
    s: common_vendor.f($data.detail.product, (good, index, i0) => {
      return {
        a: good.image.file_path,
        b: common_vendor.t(good.product_name),
        c: common_vendor.t(good.product_attr),
        d: common_vendor.t(good.total_num),
        e: common_vendor.t(good.product_price),
        f: common_vendor.t(good.line_price),
        g: index
      };
    }),
    t: common_vendor.t($data.detail.total_price),
    v: $data.detail.bag_price != 0
  }, $data.detail.bag_price != 0 ? {
    w: common_vendor.t($data.detail.bag_price)
  } : {}, {
    x: $data.detail.express_price > 0
  }, $data.detail.express_price > 0 ? {
    y: common_vendor.t($data.detail.express_price)
  } : {}, {
    z: common_vendor.t($data.detail.product.length),
    A: common_vendor.t($data.detail.pay_price),
    B: common_vendor.t($data.detail.order_type_text),
    C: $data.detail.mealtime
  }, $data.detail.mealtime ? {
    D: common_vendor.t($data.detail.mealtime)
  } : {}, {
    E: $data.detail.order_type != 1 && $data.detail.address != null
  }, $data.detail.order_type != 1 && $data.detail.address != null ? {
    F: common_vendor.t($data.detail.address.detail + $data.detail.address.address),
    G: common_vendor.t($data.detail.address.name + " " + $data.detail.address.phone)
  } : {}, {
    H: common_vendor.t($data.detail.order_no),
    I: $data.detail.table_no
  }, $data.detail.table_no ? {
    J: common_vendor.t($data.detail.table_no)
  } : {}, {
    K: common_vendor.t($data.detail.create_time),
    L: $data.detail.pay_status.value != 10
  }, $data.detail.pay_status.value != 10 ? common_vendor.e({
    M: $data.detail.online_money > 0
  }, $data.detail.online_money > 0 ? {
    N: common_vendor.t($data.detail.pay_type.text),
    O: common_vendor.t($data.detail.online_money)
  } : {}, {
    P: $data.detail.balance > 0
  }, $data.detail.balance > 0 ? {
    Q: common_vendor.t($data.detail.balance)
  } : {}) : {}, {
    R: common_vendor.t($data.detail.buyer_remark),
    S: $data.detail.pay_status.value == 10 && $data.detail.order_status == 10
  }, $data.detail.pay_status.value == 10 && $data.detail.order_status == 10 ? {
    T: common_vendor.o(($event) => $options.onPayOrder($data.detail.order_id))
  } : {}, {
    U: common_vendor.o($options.hidePopupFunc),
    V: common_vendor.o($options.subFunc),
    W: common_vendor.p({
      isPayPopup: $data.isPayPopup
    })
  }) : {}, {
    X: _ctx.theme(),
    Y: common_vendor.n(_ctx.theme() || "")
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/order/order-detail.js.map
