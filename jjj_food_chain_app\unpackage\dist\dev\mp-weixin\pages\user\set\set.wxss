/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 行为相关颜色 */
/* 主题色 */
/* 文字基本颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* start--主题色--start */
.avatar-box {
  height: 361rpx;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.avatar-box .info-image {
  position: relative;
  width: 198rpx;
  height: 198rpx;
  padding: 0;
  background: none;
  border: none;
  border-radius: 50%;
  overflow: initial;
}
.avatar-box .info-image .ava-image {
  width: 198rpx;
  height: 198rpx;
  border-radius: 50%;
}
.avatar-box .info-image .icon.iconfont {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 1;
  width: 46rpx;
  height: 46rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.55);
  color: #fff;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.user-info {
  margin-top: 22rpx;
  padding: 0 28rpx 0 34rpx;
  margin-bottom: 36rpx;
}
.user-info .key-name {
  width: 134rpx;
}
.user-info .userinfo-text,
.user-info .userinfo-input {
  flex: 1;
  height: 98rpx;
  line-height: 98rpx;
  font-size: 26rpx;
  color: #333;
}
.sub-btn {
  width: 692rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  display: flex;
  justify-content: center;
  margin: 0 auto;
  font-weight: bold;
}
.setup-btn {
  position: fixed;
  bottom: 43rpx;
  width: 100%;
  font-size: 26rpx;
  color: #999;
  text-align: center;
}
.make-item {
  height: 60rpx;
}
.pop-input {
  width: 100%;
  margin: 26rpx 0;
  box-sizing: border-box;
  border-bottom: 2rpx solid #d9d9d9;
  line-height: 60rpx;
}
.pop-input input {
  width: 100%;
  padding-left: 15rpx;
  font-size: 26rpx;
  color: #333333;
  margin: 16rpx 0;
  text-align: left;
  height: 60rpx;
  line-height: 60rpx;
}
.pop-input .icon.icon-guanbi {
  width: 38rpx;
  height: 38rpx;
  background-color: #999999;
  color: #ffffff;
  font-size: 22rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  opacity: 0.8;
}
.info-image {
  width: 112rpx;
  height: 112rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
}
.info-image image {
  width: 112rpx;
  height: 112rpx;
  border-radius: 10rpx;
}
.btns {
  border-radius: 35rpx;
  min-width: 220rpx;
}
.pop-box {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 995;
}
.pop-box .pop-bg {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.65);
}
.pop-box .pop-content {
  width: 578rpx;
  background: #FFFFFF;
  border-radius: 25rpx;
  padding: 45rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
  position: relative;
  z-index: 1001;
}
.pop-box .pop-content .close-btn {
  width: 44rpx;
  height: 44rpx;
  background: rgba(0, 0, 0, 0.45);
  border-radius: 50%;
  color: #fff;
  font-size: 26rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 17rpx;
  top: 17rpx;
  z-index: 1;
}
.pop-box .pop-content .pop-title {
  font-size: 30rpx;
  color: #333333;
  text-align: center;
  margin-bottom: 34rpx;
  font-weight: bold;
}
.pop-box .pop-content .pop-dsc {
  color: #666666;
  font-size: 24rpx;
  line-height: 1.5;
}
.pop-box .pop-content .pop-formitem {
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 38rpx;
  height: 74rpx;
}
.pop-box .pop-content .pop-formitem .pop-formitem-input {
  height: 74rpx;
  font-size: 26rpx;
}
.pop-box .pop-content .pop-formitem .get-code-btn {
  min-width: 182rpx;
  height: 58rpx;
  line-height: 58rpx;
  padding: 0rpx 30rpx;
  border-radius: 50rpx;
  white-space: nowrap;
  background-color: #ffffff;
  font-size: 26rpx;
  box-sizing: border-box;
}
[data-theme=theme0] .pop-box .pop-content .pop-formitem .get-code-btn {
  color: #fa301b !important;
}
[data-theme=theme1] .pop-box .pop-content .pop-formitem .get-code-btn {
  color: #b91d32 !important;
}
[data-theme=theme2] .pop-box .pop-content .pop-formitem .get-code-btn {
  color: #fb5032 !important;
}
[data-theme=theme3] .pop-box .pop-content .pop-formitem .get-code-btn {
  color: #e6444d !important;
}
[data-theme=theme4] .pop-box .pop-content .pop-formitem .get-code-btn {
  color: #fd8103 !important;
}
[data-theme=theme5] .pop-box .pop-content .pop-formitem .get-code-btn {
  color: #fe4b4e !important;
}
[data-theme=theme6] .pop-box .pop-content .pop-formitem .get-code-btn {
  color: #fb5032 !important;
}
.pop-box .pop-content .pop-formitem .get-code-btn[disabled=true] {
  background: #f7f7f7;
}
.pop-box .pop-content .pop-sub-btn {
  width: 498rpx;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 60rpx;
  font-weight: bold;
}