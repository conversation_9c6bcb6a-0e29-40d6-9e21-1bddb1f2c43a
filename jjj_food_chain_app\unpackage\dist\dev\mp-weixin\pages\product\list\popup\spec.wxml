<view wx:if="{{a}}" class="pop-spec"><view class="pop-bg" catchtap="{{b}}"></view><view data-theme="{{F}}" class="{{['good-detail-modal', G]}}"><view class="icon iconfont icon-guanbi1" catchtap="{{c}}"></view><view class="product-images"><image wx:if="{{d}}" src="{{e}}" class="product-image" mode="aspectFill"></image></view><view class="product-name f-s-0"><view class="text-ellipsis">{{f}}</view></view><view class="good_basic"><view class="f22 gray9">已售{{g}}</view><view wx:if="{{h}}" class="gray6 sell-point" style="line-height:1.5">{{i}}</view></view><view class="product-specs"><view wx:if="{{j}}" class="property"><view class="title"><text class="name">规格</text></view><view class="values"><view wx:for="{{k}}" wx:for-item="value" wx:key="c" catchtap="{{value.b}}" class="{{['value', value.d && 'default']}}">{{value.a}}</view></view></view><block wx:if="{{l}}"><view wx:for="{{m}}" wx:for-item="item" wx:key="c" class="property"><view class="title"><text class="name">{{item.a}}</text></view><view class="values"><block wx:for="{{item.b}}" wx:for-item="value"><view wx:if="{{value.a}}" catchtap="{{value.c}}" key="{{value.d}}" class="{{['value', value.e && 'default']}}">{{value.b}}</view></block></view></view></block><view wx:if="{{n}}" class="property"><view class="title"><text class="name">加料</text></view><view class="values"><view wx:for="{{o}}" wx:for-item="item" wx:key="c" catchtap="{{item.b}}" class="{{['value', item.d && 'default']}}"> +{{item.a}}</view></view></view></view><view class="product-content"><view class="border-b-e"><view class="group-hd d-s-c"><text class="min-name pr f30 fb">详情</text></view></view><view class="content-box"><view class=""><rich-text nodes="{{p}}"/></view></view></view><view class="spec-bottom"><view class="action"><view class="d-c d-a-s"><view class="d-s-c"><text class="f42 theme-price">￥</text><text class="f48 mr16 theme-price">{{q}}</text><text class="f22 gray9 fb text-l-t mr8">￥{{r}}</text><text wx:if="{{s}}" class="f22 gray9">打包费￥{{t}}</text></view><view class="f22 gray3">{{v}}</view></view><view class="btn-group"><image catchtap="{{w}}" class="add-image" src="{{x}}"></image><view class="number">{{y}}</view><image wx:if="{{z}}" catchtap="{{A}}" class="add-image" src="{{B}}" mode=""></image><image wx:else catchtap="{{C}}" class="add-image" src="{{D}}" mode=""></image></view></view><view><view class="addCart-btn" bindtap="{{E}}"><view>加入购物车</view></view></view></view></view></view>